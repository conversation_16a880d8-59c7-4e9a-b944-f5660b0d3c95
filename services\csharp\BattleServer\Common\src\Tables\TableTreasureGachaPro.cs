#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTreasureGachaPro
	{

		public static readonly string TName="TreasureGachaPro.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 卡包ID 
		/// </summary> 
		public int Bag {get; set;}
		/// <summary> 
		/// 道具ID 
		/// </summary> 
		public int Item {get; set;}
		/// <summary> 
		/// 道具数量 
		/// </summary> 
		public int Count {get; set;}
		/// <summary> 
		/// 概率 
		/// </summary> 
		public int Pro {get; set;}
		#endregion

		public static TableTreasureGachaPro GetData(int ID)
		{
			return TableManager.TreasureGachaProData.Get(ID);
		}

		public static List<TableTreasureGachaPro> GetAllData()
		{
			return TableManager.TreasureGachaProData.GetAll();
		}

	}
	public sealed partial class TableTreasureGachaProData
	{
		private Dictionary<int, TableTreasureGachaPro> dict = new Dictionary<int, TableTreasureGachaPro>();
		private List<TableTreasureGachaPro> dataList = new List<TableTreasureGachaPro>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTreasureGachaPro.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTreasureGachaPro>>(jsonContent);
			foreach (TableTreasureGachaPro config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTreasureGachaPro Get(int id)
		{
			if (dict.TryGetValue(id, out TableTreasureGachaPro item))
				return item;
			return null;
		}

		public List<TableTreasureGachaPro> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
