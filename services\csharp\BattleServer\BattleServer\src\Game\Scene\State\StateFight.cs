﻿
using BattleServer.Game.Core;
using BattleServer.Service;
using BattleServer.Nats;
using Game.Core;
using Aurora.Framework;

namespace BattleServer.Game
{
    public class StateFight : State
    {
        protected Scene _scene;

        private float _waitTime;
        public StateFight(StateComponent stateComponent) : base(stateComponent)
        {
            _scene = stateComponent.GetScene();
        }
        public override void OnInit()
        {
            base.OnInit();
        }

        public override void OnEnter()
        {
            Log.Debug("[StateFight] OnEnter");

            // 保存战斗阵容
            foreach(var player in _scene.GetPlayers().Values)
            {
                player.SaveToLastBattleHeroList();
            }

            // 生成随机种子
            var seed = Random.Shared.Next();
            foreach (var battle in _scene.GetBattles())
            {
                battle.OnBattleStart(seed);
            }

            TablePlayMode playMode = TablePlayMode.GetData(1);
            _waitTime = playMode.BattleDuration * 1000 + 5;
            //_waitTime = 5 * 1000;
        }

        public override void OnUpdate(float deltaTime)
        {
            if (IsAllPlayerBattleEnd())
            {
                _stateComponent.ChangeState(StateType.End);
            }
            else
            {
                _waitTime -= deltaTime;
                if (_waitTime <= 0)
                {
                    Log.Debug("[StateFight] Wait time out, changing to End state.");
                    // 如果等待时间超时，强制所有玩家进入战斗结束状态
                    foreach (var battle in _scene.GetBattles())
                    {
                        if (battle.IsBattleEnd())
                        {
                            continue;
                        }
                        battle.OnBattleTimeout();
                    }
                    return;
                }
                else
                {
                    //Log.Debug($"[StateFight] Waiting for battles to end. Remaining time: {_waitTime / 1000} seconds.");
                }
            }
        }

        private bool IsAllPlayerBattleEnd()
        {
            // 检查所有玩家是否都已准备好
            foreach (var battle in _scene.GetBattles())
            {
                if (!battle.IsBattleEnd())
                {
                    return false;
                }
            }
            Log.Debug("[StateFight] All battles are end.");
            return true;
        }
    }
}
