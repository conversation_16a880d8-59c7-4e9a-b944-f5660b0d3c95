#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuildShop
	{

		public static readonly string TName="GuildShop.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 商店物品组ID 
		/// </summary> 
		public int GroupID {get; set;}
		/// <summary> 
		/// 解锁条件（先不做） 
		/// </summary> 
		public int[] Condition {get; set;}
		/// <summary> 
		/// 标签名 
		/// </summary> 
		public int LanKey {get; set;}
		#endregion

		public static TableGuildShop GetData(int ID)
		{
			return TableManager.GuildShopData.Get(ID);
		}

		public static List<TableGuildShop> GetAllData()
		{
			return TableManager.GuildShopData.GetAll();
		}

	}
	public sealed partial class TableGuildShopData
	{
		private Dictionary<int, TableGuildShop> dict = new Dictionary<int, TableGuildShop>();
		private List<TableGuildShop> dataList = new List<TableGuildShop>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuildShop.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuildShop>>(jsonContent);
			foreach (TableGuildShop config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuildShop Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuildShop item))
				return item;
			return null;
		}

		public List<TableGuildShop> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
