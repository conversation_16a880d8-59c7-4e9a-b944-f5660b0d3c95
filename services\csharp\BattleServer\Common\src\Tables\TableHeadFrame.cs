#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableHeadFrame
	{

		public static readonly string TName="HeadFrame.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 类型（0：永久 1：限时） 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 限时过期时间戳(单位：秒) 
		/// </summary> 
		public int ExpireTime {get; set;}
		/// <summary> 
		/// 品质 
		/// </summary> 
		public int Quality {get; set;}
		/// <summary> 
		/// 名字 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 头像框描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 道具圆icon 
		/// </summary> 
		public int RoundIcon {get; set;}
		/// <summary> 
		/// 道具方icon(用于道具掉落展示) 
		/// </summary> 
		public int Icon {get; set;}
		/// <summary> 
		/// 道具资源 
		/// </summary> 
		public int Assets {get; set;}
		/// <summary> 
		/// 获取途径 
		/// </summary> 
		public int GetStr {get; set;}
		/// <summary> 
		/// 是否显示（0：显示 1：解锁后显示） 
		/// </summary> 
		public int Show {get; set;}
		/// <summary> 
		/// 指定日期类型（0：开服时间戳 1：指定日期） 
		/// </summary> 
		public int ShowTimeType {get; set;}
		/// <summary> 
		/// 指定日期显示（1、指定固定日期2、开服多久开）分钟数或指定日期 
		/// </summary> 
		public string ShowTime {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		/// <summary> 
		/// 显示顺序 
		/// </summary> 
		public int SortOrder {get; set;}
		#endregion

		public static TableHeadFrame GetData(int ID)
		{
			return TableManager.HeadFrameData.Get(ID);
		}

		public static List<TableHeadFrame> GetAllData()
		{
			return TableManager.HeadFrameData.GetAll();
		}

	}
	public sealed partial class TableHeadFrameData
	{
		private Dictionary<int, TableHeadFrame> dict = new Dictionary<int, TableHeadFrame>();
		private List<TableHeadFrame> dataList = new List<TableHeadFrame>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableHeadFrame.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableHeadFrame>>(jsonContent);
			foreach (TableHeadFrame config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableHeadFrame Get(int id)
		{
			if (dict.TryGetValue(id, out TableHeadFrame item))
				return item;
			return null;
		}

		public List<TableHeadFrame> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
