#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMonthlyCardNew
	{

		public static readonly string TName="MonthlyCardNew.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 立即掉落表id 
		/// </summary> 
		public int DropGroupId1 {get; set;}
		/// <summary> 
		/// 每日掉落表id 
		/// </summary> 
		public int DropGroupId2 {get; set;}
		/// <summary> 
		/// 价格 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 获得奖励提示，对应语言表内容 
		/// </summary> 
		public int[] DesId1 {get; set;}
		/// <summary> 
		/// 获得奖励数值 
		/// </summary> 
		public int[] DesId2 {get; set;}
		/// <summary> 
		/// 支付ID 
		/// </summary> 
		public int MidasId {get; set;}
		#endregion

		public static TableMonthlyCardNew GetData(int ID)
		{
			return TableManager.MonthlyCardNewData.Get(ID);
		}

		public static List<TableMonthlyCardNew> GetAllData()
		{
			return TableManager.MonthlyCardNewData.GetAll();
		}

	}
	public sealed partial class TableMonthlyCardNewData
	{
		private Dictionary<int, TableMonthlyCardNew> dict = new Dictionary<int, TableMonthlyCardNew>();
		private List<TableMonthlyCardNew> dataList = new List<TableMonthlyCardNew>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMonthlyCardNew.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMonthlyCardNew>>(jsonContent);
			foreach (TableMonthlyCardNew config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMonthlyCardNew Get(int id)
		{
			if (dict.TryGetValue(id, out TableMonthlyCardNew item))
				return item;
			return null;
		}

		public List<TableMonthlyCardNew> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
