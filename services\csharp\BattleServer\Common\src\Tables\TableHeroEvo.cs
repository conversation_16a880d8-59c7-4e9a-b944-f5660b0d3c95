#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableHeroEvo
	{

		public static readonly string TName="HeroEvo.json";

		#region 属性定义
		/// <summary> 
		/// ID 角色id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 觉醒消耗:物品组id 
		/// </summary> 
		public int ItemId {get; set;}
		/// <summary> 
		/// 1-5级觉醒消耗数量 
		/// </summary> 
		public int[] ItemCost {get; set;}
		/// <summary> 
		/// 英雄等级限制 
		/// </summary> 
		public int[] HeroLv {get; set;}
		#endregion

		public static TableHeroEvo GetData(int ID)
		{
			return TableManager.HeroEvoData.Get(ID);
		}

		public static List<TableHeroEvo> GetAllData()
		{
			return TableManager.HeroEvoData.GetAll();
		}

	}
	public sealed partial class TableHeroEvoData
	{
		private Dictionary<int, TableHeroEvo> dict = new Dictionary<int, TableHeroEvo>();
		private List<TableHeroEvo> dataList = new List<TableHeroEvo>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableHeroEvo.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableHeroEvo>>(jsonContent);
			foreach (TableHeroEvo config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableHeroEvo Get(int id)
		{
			if (dict.TryGetValue(id, out TableHeroEvo item))
				return item;
			return null;
		}

		public List<TableHeroEvo> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
