#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMidasDiamond
	{

		public static readonly string TName="MidasDiamond.json";

		#region 属性定义
		/// <summary> 
		/// 序列id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 渠道商品ID 
		/// </summary> 
		public string CID {get; set;}
		/// <summary> 
		/// IOS商品ID 
		/// </summary> 
		public string IOSCID {get; set;}
		/// <summary> 
		/// IOS商品ID 
		/// </summary> 
		public string IOSStoreID {get; set;}
		/// <summary> 
		/// android商品id 
		/// </summary> 
		public string AndroidStoreID {get; set;}
		/// <summary> 
		/// Payment表Id 
		/// </summary> 
		public int PaymentID {get; set;}
		#endregion

		public static TableMidasDiamond GetData(int ID)
		{
			return TableManager.MidasDiamondData.Get(ID);
		}

		public static List<TableMidasDiamond> GetAllData()
		{
			return TableManager.MidasDiamondData.GetAll();
		}

	}
	public sealed partial class TableMidasDiamondData
	{
		private Dictionary<int, TableMidasDiamond> dict = new Dictionary<int, TableMidasDiamond>();
		private List<TableMidasDiamond> dataList = new List<TableMidasDiamond>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMidasDiamond.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMidasDiamond>>(jsonContent);
			foreach (TableMidasDiamond config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMidasDiamond Get(int id)
		{
			if (dict.TryGetValue(id, out TableMidasDiamond item))
				return item;
			return null;
		}

		public List<TableMidasDiamond> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
