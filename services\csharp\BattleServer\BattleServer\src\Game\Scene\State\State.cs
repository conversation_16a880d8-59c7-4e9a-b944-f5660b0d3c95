﻿
namespace BattleServer.Game
{
    // 战斗状态机的状态类型定义
    public enum StateType
    {
        None,           // 初始状态
        Wait,           // 等待状态
        Match,          // 匹配状态
        Buffer,         // buffer状态
        UpdateHero,     // 更新英雄状态
        Preparation,    // 准备状态
        Start,          // 战斗开始状态
        Fight,          // 战斗状态
        End,            // 战斗结束状态
        Settlement,
    }

    public class State
    {
        protected StateComponent _stateComponent;

        public State(StateComponent stateComponent)
        {
            _stateComponent = stateComponent;
        }

        public virtual void OnInit()
        {
        }
        public virtual bool TryEnter(StateType curStateType) { return false; }
        public virtual void OnEnter() { }
        public virtual void OnUpdate(float deltaTime) { }
        public virtual void OnLateUpdate(float deltaTime) { }
        public virtual void OnExit()
        {
        }
    }
}
