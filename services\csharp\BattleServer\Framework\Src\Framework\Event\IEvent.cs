﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************


using System;

namespace Aurora.Framework
{
    //public interface IEventHandler
    //{
    //    Type EventType();
    //}
    ////事件处理器的基类，此处用struct减少GC
    //public abstract class EventHandler<TEvent> : IEventHandler where TEvent : struct
    //{
    //    public Type EventType()
    //    {
    //        return typeof(TEvent);
    //    }

    //    public void Excute(TEvent e)
    //    {
    //        try
    //        {
    //            Handle(e);
    //        }
    //        catch (Exception exception)
    //        {
    //            Log.Exception(exception.Message);
    //        }
    //    }

    //    protected abstract void Handle(TEvent e);
    //}

    ////基于异步的事件处理基类，返回异步ATask
    //public abstract class EventHandlerAsync<TEvent> : IEventHandler where TEvent : struct
    //{
    //    public Type EventType()
    //    {
    //        return typeof(TEvent);
    //    }

    //    public async ATask Excute(TEvent e)
    //    {
    //        try
    //        {
    //            await Handle(e);
    //        }
    //        catch (Exception exception)
    //        {
    //            Log.Exception(exception.Message);
    //        }
    //    }

    //    protected abstract ATask Handle(TEvent e);
    //}
}
