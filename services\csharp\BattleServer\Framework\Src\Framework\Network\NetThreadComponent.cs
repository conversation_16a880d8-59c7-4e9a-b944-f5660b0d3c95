﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-15
//*********************************************************


using System.Collections.Generic;

namespace Aurora.Framework
{
    public class NetThreadComponent:BaseComponent,IAwake,ILateUpdate,IDestroy
    {
        public const int CheckInteral = 2000;
        public const int RecvMaxIdleTime = 60000;
        public const int SendMaxIdleTime = 60000;

        public ThreadSyncContext SyncContext;

        public HashSet<AService> Services = new HashSet<AService>();

        public static NetThreadComponent Create(ThreadSyncContext syncContext)
        {
            NetThreadComponent comp = ReferencePool.Acquire<NetThreadComponent>();
            comp.SyncContext = syncContext;
            return comp;
        }
    }

    [ComponentSystem(typeof(NetThreadComponent))]
    public static class NetThreadComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(NetThreadComponent self)
        {
        }

        [MethodLateUpdate]
        public static void OnLateUpdate(NetThreadComponent self)
        {
            if (self == null) return;
            //驱动网络
            foreach(var p in self.Services)
            {
                p.Update();
            }
        }
        [MethodDestroy]
        public static void OnDestroy(NetThreadComponent self)
        {
            if (self == null) return;
            foreach (var p in self.Services)
            {
                p.Release();
            }
            self.Services.Clear();
        }

        public static void AddService(this NetThreadComponent self, AService kService)
        {
            if (self == null) return;
            // 这里要去下一帧添加，避免foreach错误
            self.SyncContext.Post(() =>
            {
                //因为要到主线程执行，中间可能会已经释放了
                if (kService.IsReleased())
                {
                    return;
                }
                self.Services.Add(kService);
            });
        }
        public static void RemoveService(this NetThreadComponent self, AService kService)
        {
            if (self == null) return;
            // 这里要去下一帧添加，避免foreach错误
            self.SyncContext.Post(() =>
            {
                //因为要到主线程执行，中间可能会已经释放了
                if (kService.IsReleased())
                {
                    return;
                }
                self.Services.Remove(kService);
            });
        }
    }
}
