//using BattleServer.Framework;
//using BattleServer.Game.AutoChess;
//using BattleServer.Service;
//using Game.Core;
//using Aurora.Framework;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace LiteFrame.Game
//{
//    /// <summary>
//    /// AutoChess场景消息处理器
//    /// 专门处理AutoChess相关的消息，与传统Scene处理器完全隔离
//    /// </summary>
//    public static partial class AutoChessSceneHandler
//    {
//        /// <summary>
//        /// 创建AutoChess战斗处理器
//        /// </summary>
//        [Msg(typeof(AutoChessCreateBattleReq))]
//        public static void CreateBattleReqHandler(Session session, AutoChessCreateBattleReq request, AutoChessCreateBattleResp response, Action reply)
//        {
//            try
//            {
//                // 验证请求数据
//                var players = request.players;
//                var teams = request.teams;

//                if (players == null || players.Count != 4)
//                {
//                    Log.Error($"[AutoChessSceneHandler] Invalid player count {players?.Count ?? 0}, expected 4");
//                    response.Error = -1;
//                    response.Message = "Invalid player count";
//                    reply();
//                    return;
//                }

//                if (teams == null || teams.Count != 4)
//                {
//                    Log.Error($"[AutoChessSceneHandler] Invalid team count {teams?.Count ?? 0}, expected 4");
//                    response.Error = -1;
//                    response.Message = "Invalid team count";
//                    reply();
//                    return;
//                }

//                // 创建AutoChessScene
//                long battleId = SceneManager.Instance.CreateAutoChessScene(request.players, request.teams);
//                if (battleId == 0)
//                {
//                    Log.Error($"[AutoChessSceneHandler] Failed to create AutoChess battle");
//                    response.Error = -1;
//                    response.Message = "Failed to create AutoChess battle";
//                    reply();
//                    return;
//                }

//                response.BattleId = battleId;
//                response.Error = 0;
//                Log.Info($"[AutoChessSceneHandler] Created AutoChess battle {battleId} on thread {Thread.CurrentThread.ManagedThreadId}");

//                reply();
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"[AutoChessSceneHandler] CreateBattleReqHandler error: {ex.Message}");
//                response.Error = -99;
//                response.Message = "Internal server error";
//                reply();
//            }
//        }

//        /// <summary>
//        /// AutoChess玩家进入战斗处理器
//        /// </summary>
//        [Msg(typeof(AutoChessEnterBattleReq))]
//        public static void EnterBattleReqHandler(Session session, AutoChessEnterBattleReq request, AutoChessEnterBattleResp response, Action reply)
//        {
//            try
//            {
//                // 通过SceneManager处理玩家进入，确保状态一致性
//                var result = SceneManager.Instance.HandlePlayerEnterBattle((long)request.Uid);

//                response.Error = 0;
//                response.Code = result.Code;
//                if (result.Code != 0)
//                {
//                    response.Message = "Failed to enter battle";
//                }

//                Log.Debug($"[AutoChessSceneHandler] Player {request.Uid} enter battle result: {result.Code} on thread {Thread.CurrentThread.ManagedThreadId}");

//                reply();
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"[AutoChessSceneHandler] EnterBattleReqHandler error: {ex.Message}");
//                response.Error = -99;
//                response.Code = -99;
//                response.Message = "Internal server error";
//                reply();
//            }
//        }

//        /// <summary>
//        /// AutoChess选择Buff处理器
//        /// </summary>
//        [Msg(typeof(AutoChessSelectBuffReq))]
//        public static void SelectBuffReqHandler(Session session, AutoChessSelectBuffReq request, AutoChessSelectBuffResp response, Action reply)
//        {
//            try
//            {
//                var autoChessScene = SceneManager.Instance.GetAutoChessSceneByPlayerId((long)request.Uid);
//                if (autoChessScene == null)
//                {
//                    Log.Error($"[AutoChessSceneHandler] Player {request.Uid} not found in any AutoChess battle");
//                    response.Error = -1;
//                    response.Code = -1;
//                    response.Message = "Player not found in any battle";
//                    reply();
//                    return;
//                }

//                var result = autoChessScene.SelectBuff((long)request.Uid, request.BufferID);
//                response.Error = 0;
//                response.Code = result.Code;
//                response.NewBoards = result.NewHeroes?.ToList() ?? new List<PBCheckerBoard>();

//                Log.Debug($"[AutoChessSceneHandler] Player {request.Uid} select buff {request.BufferID} in battle {autoChessScene.BattleId} result: {result.Code} on thread {Thread.CurrentThread.ManagedThreadId}");

//                reply();
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"[AutoChessSceneHandler] SelectBuffReqHandler error: {ex.Message}");
//                response.Error = -99;
//                response.Code = -99;
//                response.Message = "Internal server error";
//                reply();
//            }
//        }

//        /// <summary>
//        /// AutoChess合成英雄处理器
//        /// </summary>
//        [Msg(typeof(AutoChessMergeHeroReq))]
//        public static void MergeHeroReqHandler(Session session, AutoChessMergeHeroReq request, AutoChessMergeHeroResp response, Action reply)
//        {
//            try
//            {
//                var autoChessScene = SceneManager.Instance.GetAutoChessSceneByPlayerId((long)request.pbMsg.Uid);
//                if (autoChessScene == null)
//                {
//                    Log.Error($"[AutoChessSceneHandler] Player {request.pbMsg.Uid} not found in any AutoChess battle");
//                    response.Error = -1;
//                    response.pbMsg = new MergeHeroResp { Code = -1 };
//                    response.Message = "Player not found in any battle";
//                    reply();
//                    return;
//                }

//                var result = autoChessScene.MergeHero(request.pbMsg);
//                response.Error = 0;
//                response.pbMsg = result;

//                Log.Debug($"[AutoChessSceneHandler] Player {request.pbMsg.Uid} merge hero in battle {autoChessScene.BattleId} result: {result.Code} on thread {Thread.CurrentThread.ManagedThreadId}");

//                reply();
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"[AutoChessSceneHandler] MergeHeroReqHandler error: {ex.Message}");
//                response.Error = -99;
//                response.pbMsg = new MergeHeroResp { Code = -99 };
//                response.Message = "Internal server error";
//                reply();
//            }
//        }

//        /// <summary>
//        /// AutoChess玩家准备处理器
//        /// </summary>
//        [Msg(typeof(AutoChessBattleReadyReq))]
//        public static void BattleReadyReqHandler(Session session, AutoChessBattleReadyReq request, AutoChessBattleReadyResp response, Action reply)
//        {
//            try
//            {
//                var autoChessScene = SceneManager.Instance.GetAutoChessSceneByPlayerId((long)request.Uid);
//                if (autoChessScene == null)
//                {
//                    Log.Error($"[AutoChessSceneHandler] Player {request.Uid} not found in any AutoChess battle");
//                    response.Error = -1;
//                    response.Code = -1;
//                    response.Message = "Player not found in any battle";
//                    reply();
//                    return;
//                }

//                var result = autoChessScene.SetPlayerReady((long)request.Uid, request.Moves);
//                response.Error = 0;
//                response.Code = result.Code;

//                Log.Debug($"[AutoChessSceneHandler] Player {request.Uid} ready in battle {autoChessScene.BattleId} result: {result.Code} on thread {Thread.CurrentThread.ManagedThreadId}");

//                reply();
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"[AutoChessSceneHandler] BattleReadyReqHandler error: {ex.Message}");
//                response.Error = -99;
//                response.Code = -99;
//                response.Message = "Internal server error";
//                reply();
//            }
//        }

//        /// <summary>
//        /// AutoChess结束战斗处理器
//        /// </summary>
//        [Msg(typeof(AutoChessEndBattleReq))]
//        public static void EndBattleReqHandler(Session session, AutoChessEndBattleReq request, AutoChessEndBattleResp response, Action reply)
//        {
//            try
//            {
//                var autoChessScene = SceneManager.Instance.GetAutoChessSceneByPlayerId((long)request.Uid);
//                if (autoChessScene == null)
//                {
//                    Log.Error($"[AutoChessSceneHandler] Player {request.Uid} not found in any AutoChess battle");
//                    response.Error = -1;
//                    response.Code = -1;
//                    response.Message = "Player not found in any battle";
//                    reply();
//                    return;
//                }

//                var result = autoChessScene.HandleBattleEnd((long)request.Uid, request.Win);
//                response.Error = 0;
//                response.Code = result.Code;

//                Log.Debug($"[AutoChessSceneHandler] Player {request.Uid} end battle {autoChessScene.BattleId} win: {request.Win} result: {result.Code} on thread {Thread.CurrentThread.ManagedThreadId}");

//                reply();
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"[AutoChessSceneHandler] EndBattleReqHandler error: {ex.Message}");
//                response.Error = -99;
//                response.Code = -99;
//                response.Message = "Internal server error";
//                reply();
//            }
//        }

//        /// <summary>
//        /// AutoChess离开战斗处理器
//        /// </summary>
//        [Msg(typeof(AutoChessLeaveBattleReq))]
//        public static void LeaveBattleReqHandler(Session session, AutoChessLeaveBattleReq request, AutoChessLeaveBattleResp response, Action reply)
//        {
//            try
//            {
//                var autoChessScene = SceneManager.Instance.GetAutoChessSceneByPlayerId((long)request.Uid);
//                if (autoChessScene == null)
//                {
//                    Log.Warning($"[AutoChessSceneHandler] Player {request.Uid} not found in any AutoChess battle for leave");
//                    response.Error = 0; // 不在战斗中也算成功离开
//                    response.Code = 0;
//                    reply();
//                    return;
//                }

//                var result = autoChessScene.HandlePlayerLeave((long)request.Uid);
//                response.Error = 0;
//                response.Code = result.Code;

//                Log.Debug($"[AutoChessSceneHandler] Player {request.Uid} leave battle {autoChessScene.BattleId} result: {result.Code} on thread {Thread.CurrentThread.ManagedThreadId}");

//                reply();
//            }
//            catch (Exception ex)
//            {
//                Log.Error($"[AutoChessSceneHandler] LeaveBattleReqHandler error: {ex.Message}");
//                response.Error = -99;
//                response.Code = -99;
//                response.Message = "Internal server error";
//                reply();
//            }
//        }
//    }
//}
