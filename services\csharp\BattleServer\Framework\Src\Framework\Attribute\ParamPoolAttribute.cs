﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************

using System;

namespace Aurora.Framework
{
    //ParamPool的函数标签，用于在变更的时候调用
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class ParamChangeAttribute : AuroraMethodAttribute
    {
        public int PoolType { get; }
        public int Index { get; }
        public ParamChangeAttribute(int poolType, int index)
        {
            this.PoolType = poolType;
            this.Index = index;
        }
    }
    //ParamPool的函数标签，用于在变更触发调用
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class ParamListenChangeAttribute : AuroraMethodAttribute
    {
        public ParamListenChangeAttribute()
        {
        }
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public sealed class ParamIncrementChangeAttribute : AuroraMethodAttribute
    {
        public int PoolType { get; }
        public int Index { get; }

        public EParamIncrementType type { get; }
        public Type dataType { get; }
        
        public ParamIncrementChangeAttribute(int poolType, int index,EParamIncrementType type,Type dataType)
        {
            this.PoolType = poolType;
            this.Index = index;
            this.type = type;
            this.dataType = dataType;
        }
    }

}
