#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuildConfig
	{

		public static readonly string TName="GuildConfig.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名字字符最大长度 
		/// </summary> 
		public int NameMaxLength {get; set;}
		/// <summary> 
		/// 宣言字符最大长度 
		/// </summary> 
		public int NoticeMaxLength {get; set;}
		/// <summary> 
		/// 图标最大个数[1-5] 
		/// </summary> 
		public int IconMaxCount {get; set;}
		/// <summary> 
		/// 创建消耗的钻石数 
		/// </summary> 
		public int CreateDiamondCost {get; set;}
		/// <summary> 
		/// 审批关卡需求配置4档关卡ID 
		/// </summary> 
		public int[] ReqStageIds {get; set;}
		/// <summary> 
		/// 日志最大条数 
		/// </summary> 
		public int LogMaxCount {get; set;}
		/// <summary> 
		/// 聊天最大条数 
		/// </summary> 
		public int ChatMaxCount {get; set;}
		/// <summary> 
		/// 修改名称消耗的钻石数 
		/// </summary> 
		public int EditNameDiamondCost {get; set;}
		/// <summary> 
		/// 会长超过7天未登录被弹劾时间，单位：秒 
		/// </summary> 
		public int PresidentOfflineTime1 {get; set;}
		/// <summary> 
		/// 会长超过14天未登录自动转让时间，单位：秒 
		/// </summary> 
		public int PresidentOfflineTime2 {get; set;}
		/// <summary> 
		/// 入会申请最大数量 
		/// </summary> 
		public int ApplyMaxCount {get; set;}
		/// <summary> 
		/// 世界聊天发布招募消耗钻石 
		/// </summary> 
		public int WorldSendRecruitCost {get; set;}
		/// <summary> 
		/// 世界聊天发布招募信息最大字符长度 
		/// </summary> 
		public int WorldSendRecruitTextMaxNum {get; set;}
		/// <summary> 
		/// 未砍价通知CD时间，单位：秒 
		/// </summary> 
		public int BargainingCDTime {get; set;}
		/// <summary> 
		/// 商店刷新类型0每日刷新1每周刷新 
		/// </summary> 
		public int ShopRefreshType {get; set;}
		/// <summary> 
		/// 公会推荐列表刷新CD时间，单位：秒 
		/// </summary> 
		public int RecommendCDTime {get; set;}
		/// <summary> 
		/// 申请同一公会CD时间，单位：秒 
		/// </summary> 
		public int ApplySameCDTime {get; set;}
		/// <summary> 
		/// 每日申请上限 
		/// </summary> 
		public int PlayerApplyMaxNum {get; set;}
		/// <summary> 
		/// 退出(主动)公会CD时间，单位：秒 
		/// </summary> 
		public int QuitCDTime {get; set;}
		/// <summary> 
		/// 退出(被踢)公会CD时间，单位：秒 
		/// </summary> 
		public int KickCDTime {get; set;}
		/// <summary> 
		/// 公会列表刷新个数 
		/// </summary> 
		public int RecommendMaxCount {get; set;}
		/// <summary> 
		/// 公会列表刷新活跃前100名 
		/// </summary> 
		public int RecommendActiveMaxCount {get; set;}
		/// <summary> 
		/// 公会排行列表最大个数 
		/// </summary> 
		public int RankMaxCount {get; set;}
		/// <summary> 
		/// 公会科技初始ID 
		/// </summary> 
		public int TechInitID {get; set;}
		/// <summary> 
		/// 公会科技强化材料物品ID 6表示公会币 
		/// </summary> 
		public int TechLevelupItemId {get; set;}
		/// <summary> 
		/// 公会科技重置钻石数 
		/// </summary> 
		public int TechResetDiamondCost {get; set;}
		/// <summary> 
		/// 公会普通商品消耗材料物品ID 6表示公会币 
		/// </summary> 
		public int GoodsCostItemId {get; set;}
		/// <summary> 
		/// 公会砍价礼包消耗材料物品ID 6表示公会币 
		/// </summary> 
		public int BargainingGoodsCostItemId {get; set;}
		/// <summary> 
		/// 未砍价邮件标题 
		/// </summary> 
		public int NoBargainingTitle {get; set;}
		/// <summary> 
		/// 未砍价邮件内容 
		/// </summary> 
		public int NoBargainingContent {get; set;}
		/// <summary> 
		/// BOSS购买最大次数上限 
		/// </summary> 
		public int BossBuyLimit {get; set;}
		/// <summary> 
		/// BOSS购买价格 
		/// </summary> 
		public int BossBuyPrice {get; set;}
		/// <summary> 
		/// BOSS击杀邮件标题 
		/// </summary> 
		public int BossKillTitle {get; set;}
		/// <summary> 
		/// BOSS击杀邮件内容 
		/// </summary> 
		public int BossKillContent {get; set;}
		/// <summary> 
		/// BOSS排行邮件标题 
		/// </summary> 
		public int BossRankTitle {get; set;}
		/// <summary> 
		/// BOSS排行邮件内容 
		/// </summary> 
		public int BossRankContent {get; set;}
		/// <summary> 
		/// BOSS功能开服后多长时间后开启，单位：秒 
		/// </summary> 
		public int BossOpenTime {get; set;}
		/// <summary> 
		/// 科技功能开服后多长时间后开启，单位：秒 
		/// </summary> 
		public int TechOpenTime {get; set;}
		#endregion

		public static TableGuildConfig GetData(int ID)
		{
			return TableManager.GuildConfigData.Get(ID);
		}

		public static List<TableGuildConfig> GetAllData()
		{
			return TableManager.GuildConfigData.GetAll();
		}

	}
	public sealed partial class TableGuildConfigData
	{
		private Dictionary<int, TableGuildConfig> dict = new Dictionary<int, TableGuildConfig>();
		private List<TableGuildConfig> dataList = new List<TableGuildConfig>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuildConfig.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuildConfig>>(jsonContent);
			foreach (TableGuildConfig config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuildConfig Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuildConfig item))
				return item;
			return null;
		}

		public List<TableGuildConfig> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
