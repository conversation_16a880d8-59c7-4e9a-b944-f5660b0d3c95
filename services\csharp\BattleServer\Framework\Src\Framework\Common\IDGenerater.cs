﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-7
//*********************************************************

using System;
using System.Runtime.InteropServices;
using System.Text;

namespace Aurora.Framework
{
    //ID
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct IdStruct
    {
        public uint Time;    // 30bit--1073741824秒=34年
        public int Process;  // 18bit--max 262143
        public ushort Value; // 16bit--max 65535

        public long ToLong()
        {
            ulong result = 0;
            result |= this.Value;
            result |= (ulong)this.Process << 16;
            result |= (ulong)this.Time << 34;
            return (long)result;
        }

        public IdStruct(uint time, int process, ushort value)
        {
            this.Process = process;
            this.Time = time;
            this.Value = value;
        }

        public IdStruct(long id)
        {
            ulong result = (ulong)id;
            this.Value = (ushort)(result & ushort.MaxValue);
            result >>= 16;
            this.Process = (int)(result & IDGenerater.Mask18bit);
            result >>= 18;
            this.Time = (uint)result;
        }

        public override string ToString()
        {
            return ($"process: {Process}, time: {Time}, value: {Value}");
        }
    }

    //InstanceId
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct InstanceIdStruct
    {
        public uint Time;   // 当年开始的tick 28bit
        public int Process; // 18bit
        public uint Value;  // 18bit

        public long ToLong()
        {
            ulong result = 0;
            result |= this.Value;
            result |= (ulong)this.Process << 18;
            result |= (ulong)this.Time << 36;
            return (long)result;
        }

        public InstanceIdStruct(long id)
        {
            ulong result = (ulong)id;
            this.Value = (uint)(result & IDGenerater.Mask18bit);
            result >>= 18;
            this.Process = (int)(result & IDGenerater.Mask18bit);
            result >>= 18;
            this.Time = (uint)result;
        }

        public InstanceIdStruct(uint time, int process, uint value)
        {
            this.Time = time;
            this.Process = process;
            this.Value = value;
        }

        // 给SceneId使用
        public InstanceIdStruct(int process, uint value)
        {
            this.Time = 0;
            this.Process = process;
            this.Value = value;
        }

        public override string ToString()
        {
            return ($"process: {Process}, time: {Value}, value: {Time}");
        }
    }

    //Unit
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct UnitIdStruct
    {
        public uint Time;        // 30bit 34年
        public ushort Zone;      // 10bit 1024个区
        public byte ProcessMode; // 8bit  Process % 256  一个区最多256个进程
        public ushort Value;     // 16bit 每秒每个进程最大16K个Unit--max：65535

        public long ToLong()
        {
            ulong result = 0;
            result |= this.Value;
            result |= (uint)this.ProcessMode << 16;
            result |= (ulong)this.Zone << 24;
            result |= (ulong)this.Time << 34;
            return (long)result;
        }

        public UnitIdStruct(int zone, int process, uint time, ushort value)
        {
            this.Time = time;
            this.ProcessMode = (byte)(process % 256);
            this.Value = value;
            this.Zone = (ushort)zone;
        }

        public UnitIdStruct(long id)
        {
            ulong result = (ulong)id;
            this.Value = (ushort)(result & ushort.MaxValue);
            result >>= 16;
            this.ProcessMode = (byte)(result & byte.MaxValue);
            result >>= 8;
            this.Zone = (ushort)(result & 0x03ff);
            result >>= 10;
            this.Time = (uint)result;
        }

        public override string ToString()
        {
            return $"ProcessMode: {this.ProcessMode}, value: {this.Value} time: {this.Time}";
        }

        public static int GetUnitZone(long unitId)
        {
            int v = (int)((unitId >> 24) & 0x03ff); // 取出10bit
            return v;
        }
    }
    
    public class IDGenerater
    {
        public const int Mask18bit = 0x03ffff;
        public static IDGenerater Instance = new IDGenerater();

        public const int MaxZone = 1024;

        private DateTime epoch2020;
        private ushort value;
        private uint lastIdTime;

        private DateTime epochThisYear;
        private uint instanceIdValue;
        private uint lastInstanceIdTime;

        private ushort unitIdValue;
        private uint lastUnitIdTime;

        private int Process;
        public IDGenerater()
        {
            this.epoch2020 = new DateTime(2020, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            this.epochThisYear = new DateTime(ATimer.Instance.UtcNow.Year, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            this.lastInstanceIdTime = TimeSinceThisYear();
            if (this.lastInstanceIdTime <= 0)
            {
                Log.Warning($"lastInstanceIdTime less than 0: {lastInstanceIdTime}");
                this.lastInstanceIdTime = 1;
            }
            this.lastIdTime = TimeSince2020();
            if (this.lastIdTime <= 0)
            {
                Log.Warning($"lastIdTime less than 0: {lastIdTime}");
                this.lastIdTime = 1;
            }
            this.lastUnitIdTime = TimeSince2020();
            if (this.lastUnitIdTime <= 0)
            {
                Log.Warning($"lastUnitIdTime less than 0: {lastUnitIdTime}");
                this.lastUnitIdTime = 1;
            }
        }

        public void Init(int nID) 
        {
            Process = nID;
        }
        public void Dispose()
        {
            this.epoch2020 = DateTime.MinValue;
            this.epochThisYear = DateTime.MinValue;
            this.value = 0;
        }

        private uint TimeSince2020()
        {
            return (uint)(ATimer.Instance.UtcNow - this.epoch2020).TotalSeconds;
        }

        private uint TimeSinceThisYear()
        {
            return (uint)(ATimer.Instance.UtcNow - this.epochThisYear).TotalSeconds;
        }

        public long InstanceId()
        {
            uint time = TimeSinceThisYear();

            if (time > this.lastInstanceIdTime)
            {
                this.lastInstanceIdTime = time;
                this.instanceIdValue = 0;
            }
            else
            {
                ++this.instanceIdValue;

                if (this.instanceIdValue > IDGenerater.Mask18bit - 1) // 18bit
                {
                    ++this.lastInstanceIdTime; // 借用下一秒
                    this.instanceIdValue = 0;

                    Log.Error($"instanceid count per sec overflow: {time} {lastInstanceIdTime}");
                }
            }

            InstanceIdStruct instanceIdStruct = new InstanceIdStruct(this.lastInstanceIdTime, Process, this.instanceIdValue);
            return instanceIdStruct.ToLong();
        }

        public long ID()
        {
            uint time = TimeSince2020();

            if (time > this.lastIdTime)
            {
                this.lastIdTime = time;
                this.value = 0;
            }
            else
            {
                ++this.value;

                if (value > ushort.MaxValue - 1)
                {
                    this.value = 0;
                    ++this.lastIdTime; // 借用下一秒
                    Log.Error($"id count per sec overflow: {time} {lastIdTime}");
                }
            }

            IdStruct idStruct = new IdStruct(this.lastIdTime, Process, value);
            return idStruct.ToLong();
        }

        public long UnitId(int zone)
        {
            if (zone > MaxZone)
            {
                throw new FrameworkException("zone > MaxZone(1024)!");
            }
            uint time = TimeSince2020();

            if (time > this.lastUnitIdTime)
            {
                this.lastUnitIdTime = time;
                this.unitIdValue = 0;
            }
            else
            {
                ++this.unitIdValue;

                if (this.unitIdValue > ushort.MaxValue - 1)
                {
                    this.unitIdValue = 0;
                    ++this.lastUnitIdTime; // 借用下一秒
                    Log.Error($"unitid count per sec overflow: {time} {lastUnitIdTime}");
                }
            }

            UnitIdStruct unitIdStruct = new UnitIdStruct(zone, Process, this.lastUnitIdTime, this.unitIdValue);
            return unitIdStruct.ToLong();
        }
    }
}
