#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityTimeConfig
	{

		public static readonly string TName="ActivityTimeConfig.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 活动类型枚举值0.战令 1.七日任务（仅占位） 2.七日签到（仅占位）3. 开服活动.4英雄赛季buff 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 时间计算类型0.以开服时间日零点为基准1.以绝对时间为基准2.以玩家创建角色时间为基准 
		/// </summary> 
		public int TimeCalcType {get; set;}
		/// <summary> 
		/// 准备时间(约定非固定日期的。 则生成的配置。是今天的秒比如 10：00:00 是36000秒。 这里的格式 只能是 时分秒。或是年月日。时分秒。 显示的有问题需要特殊处理)(暂不使用，根据需求再实现） 
		/// </summary> 
		public string ReadyTime {get; set;}
		/// <summary> 
		/// 开始时间(分钟数或时间格式字符串） 
		/// </summary> 
		public string StartTime {get; set;}
		/// <summary> 
		/// 结束时间(分钟数或时间格式字符串） 
		/// </summary> 
		public string EndTime {get; set;}
		/// <summary> 
		/// 关闭时间(暂不使用，根据需求再实现） 
		/// </summary> 
		public string CloseTime {get; set;}
		/// <summary> 
		/// 下期活动的Id 
		/// </summary> 
		public int NextId {get; set;}
		/// <summary> 
		/// 兑换商店ID 
		/// </summary> 
		public int ExchangeId {get; set;}
		#endregion

		public static TableActivityTimeConfig GetData(int ID)
		{
			return TableManager.ActivityTimeConfigData.Get(ID);
		}

		public static List<TableActivityTimeConfig> GetAllData()
		{
			return TableManager.ActivityTimeConfigData.GetAll();
		}

	}
	public sealed partial class TableActivityTimeConfigData
	{
		private Dictionary<int, TableActivityTimeConfig> dict = new Dictionary<int, TableActivityTimeConfig>();
		private List<TableActivityTimeConfig> dataList = new List<TableActivityTimeConfig>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityTimeConfig.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityTimeConfig>>(jsonContent);
			foreach (TableActivityTimeConfig config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityTimeConfig Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityTimeConfig item))
				return item;
			return null;
		}

		public List<TableActivityTimeConfig> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
