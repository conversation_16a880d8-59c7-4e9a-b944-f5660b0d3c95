//using System;
//using System.Collections.Generic;
//using System.Linq;
//using Aurora.Framework;
//using BattleServer.Game.Core;
//using Game.Core;

//namespace BattleServer.Game.Player
//{
//    /// <summary>
//    /// 玩家棋盘实体数据
//    /// </summary>
//    public class PlayerBoardEntity
//    {
//        public int ConfigId { get; set; }
//        public int StarLevel { get; set; }
//        public int GridX { get; set; }
//        public int GridY { get; set; }

//        public PlayerBoardEntity(int configId, int starLevel, int gridX, int gridY)
//        {
//            ConfigId = configId;
//            StarLevel = starLevel;
//            GridX = gridX;
//            GridY = gridY;
//        }
//    }

//    /// <summary>
//    /// 玩家数据类
//    /// </summary>
//    public class PlayerData
//    {
//        public long PlayerId { get; }
//        public string ServerId { get; }
//        public string PlayerName { get; } // 新增：玩家名称
//        public int PlayerLevel { get; } // 新增：玩家等级
//        public int Trophy { get; } // 新增：奖杯数
//        public bool IsReady { get; set; }
//        public bool HasEntered { get; set; } = false; // 新增：是否已进入战斗
//        public int Health { get; set; }
//        public List<int> Lineup { get; }
//        public List<PBBattleHeroInfo> HeroInfos { get; } // 完整英雄信息
//        public bool IsEliminated { get; set; }
//        public long OpponentId { get; set; }
//        public List<PlayerBoardEntity> BoardEntities { get; set; } // 玩家的棋盘实体数据
//        public List<PlayerBoardEntity> PreviousRoundBoardEntities { get; set; } // 上一回合结束时的棋盘数据

//        public PlayerData(long playerId, string serverId, List<int> lineup, List<PBBattleHeroInfo> heroInfos = null,
//                         string playerName = null, int playerLevel = 1, int trophy = 1000)
//        {
//            PlayerId = playerId;
//            ServerId = serverId ?? "unknown";
//            PlayerName = playerName ?? $"Player{playerId}";
//            PlayerLevel = playerLevel;
//            Trophy = trophy;
//            Lineup = new List<int>(lineup ?? new List<int>());
//            HeroInfos = new List<PBBattleHeroInfo>(heroInfos ?? new List<PBBattleHeroInfo>()); // 保存完整英雄信息
//            Health = 3; // 默认血量，实际血量在PlayerManager中统一设置
//            IsReady = false;
//            IsEliminated = false;
//            OpponentId = 0;
//            BoardEntities = new List<PlayerBoardEntity>(); // 初始化棋盘实体列表
//            PreviousRoundBoardEntities = new List<PlayerBoardEntity>(); // 初始化上一回合棋盘实体列表
//        }
//    }

//    /// <summary>
//    /// 玩家管理器 - 负责管理玩家血量、准备状态、淘汰状态等数据
//    /// </summary>
//    public class PlayerManager : BattleComponentBase
//    {
//        private readonly Dictionary<long, PlayerData> _players = new();
//        private BattleEventBus _eventBus;


//        private Dictionary<ulong, BattlePlayer> mBattlePlayers = new Dictionary<ulong, BattlePlayer>();

//        /// <summary>
//        /// 构造函数
//        /// </summary>
//        /// <param name="eventBus">事件总线</param>
//        public PlayerManager(BattleEventBus? eventBus = null)
//        {
//            _eventBus = eventBus ?? new BattleEventBus();
//        }
        
//        /// <summary>
//        /// 初始化玩家管理器
//        /// </summary>
//        /// <param name="playerIds">玩家ID列表</param>
//        /// <param name="playerLineups">玩家阵容数据</param>
//        /// <param name="playerHeroInfos">玩家完整英雄信息</param>
//        /// <param name="playerServerIds">玩家服务器ID数据</param>
//        /// <param name="playerBasicInfos">玩家基本信息（名称、等级、奖杯）</param>
//        public void Initialize(List<long> playerIds, Dictionary<long, List<int>> playerLineups, Dictionary<long, List<PBBattleHeroInfo>> playerHeroInfos = null, Dictionary<long, string> playerServerIds = null, Dictionary<long, (string name, int level, int trophy)> playerBasicInfos = null)
//        {
//            if (playerIds == null || playerIds.Count == 0)
//                throw new ArgumentException("Player IDs cannot be null or empty", nameof(playerIds));

//            if (playerIds.Count < BattleConfig.Player.MinPlayers || playerIds.Count > BattleConfig.Player.MaxPlayers)
//                throw new ArgumentException($"Player count must be between {BattleConfig.Player.MinPlayers} and {BattleConfig.Player.MaxPlayers}", nameof(playerIds));

//            _players.Clear();

//            // 收集所有玩家的奖杯数，用于计算统一血量
//            var playerTrophies = new Dictionary<long, int>();
//            foreach (var playerId in playerIds)
//            {
//                var basicInfo = playerBasicInfos?.GetValueOrDefault(playerId) ?? ($"Player{playerId}", 1, 1000);
//                playerTrophies[playerId] = basicInfo.Item3; // trophy
//            }

//            // 根据策划要求：取最高杯段玩家血量作为所有玩家的血量
//            int battleHealth = BattleConfig.Player.GetMaxHealthFromTrophies(playerTrophies);

//            // 初始化玩家数据
//            foreach (var playerId in playerIds)
//            {
//                var lineup = playerLineups?.GetValueOrDefault(playerId) ?? new List<int>();
//                var heroInfos = playerHeroInfos?.GetValueOrDefault(playerId) ?? new List<PBBattleHeroInfo>(); // 获取完整英雄信息
//                var serverId = playerServerIds?.GetValueOrDefault(playerId) ?? "unknown";

//                // 获取玩家基本信息
//                var basicInfo = playerBasicInfos?.GetValueOrDefault(playerId) ?? ($"Player{playerId}", 1, 1000);
//                var playerData = new PlayerData(playerId, serverId, lineup, heroInfos, basicInfo.Item1, basicInfo.Item2, basicInfo.Item3); // 传递完整信息

//                // 设置根据奖杯数计算的血量
//                playerData.Health = battleHealth;

//                _players[playerId] = playerData;

//                Log.Info($"[{LogName}] Player {playerId} ({basicInfo.Item1}) Trophy {basicInfo.Item3} Health {battleHealth}");

//                if (lineup.Count == 0)
//                {
//                    Log.Warning($"[{LogName}] Player {playerId} has no lineup data");
//                }
//            }

//            Log.Info($"[{LogName}] Initialized with {playerIds.Count} players");
//        }

//        protected override void OnInitialize()
//        {
//            // 基类初始化已完成
//        }
        
//        /// <summary>
//        /// 获取所有玩家ID
//        /// </summary>
//        public List<long> GetAllPlayerIds()
//        {
//            ThrowIfNotInitialized();
//            return _players.Keys.ToList();
//        }

//        /// <summary>
//        /// 获取活跃（未淘汰）玩家ID
//        /// </summary>
//        public List<long> GetActivePlayerIds()
//        {
//            ThrowIfNotInitialized();
//            return _players.Values
//                .Where(p => !p.IsEliminated)
//                .Select(p => p.PlayerId)
//                .ToList();
//        }

//        /// <summary>
//        /// 获取玩家数据
//        /// </summary>
//        public PlayerData GetPlayerData(long playerId)
//        {
//            ThrowIfNotInitialized();
//            return _players.GetValueOrDefault(playerId);
//        }

//        /// <summary>
//        /// 尝试让玩家进入战斗
//        /// </summary>
//        public bool TryEnterPlayer(long playerId)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            if (player == null || player.HasEntered)
//                return false;

//            player.HasEntered = true;
//            Log.Info($"[{LogName}] Player {playerId} entered battle");

//            // 检查是否所有玩家都已进入
//            if (CheckAllPlayersEntered())
//            {
//                _eventBus.Publish(new AllPlayersEnteredEvent(BattleId));
//            }
//            return true;
//        }

//        /// <summary>
//        /// 检查所有玩家是否都已进入
//        /// </summary>
//        public bool CheckAllPlayersEntered()
//        {
//            ThrowIfNotInitialized();
//            return _players.Values.All(p => p.HasEntered);
//        }

//        /// <summary>
//        /// 重置玩家进入状态（用于新回合）
//        /// </summary>
//        public void ResetPlayersEntryStatus()
//        {
//            ThrowIfNotInitialized();
//            foreach (var player in _players.Values)
//            {
//                player.HasEntered = false;
//            }
//            Log.Info($"[{LogName}] Reset all players entry status");
//        }

//        /// <summary>
//        /// 获取玩家血量
//        /// </summary>
//        public int GetPlayerHealth(long playerId)
//        {
//            return GetPlayerData(playerId)?.Health ?? 0;
//        }

//        /// <summary>
//        /// 获取玩家阵容
//        /// </summary>
//        public List<int> GetPlayerLineup(long playerId)
//        {
//            return GetPlayerData(playerId)?.Lineup ?? new List<int>();
//        }

//        /// <summary>
//        /// 获取玩家完整英雄信息
//        /// </summary>
//        public List<PBBattleHeroInfo> GetPlayerHeroInfos(long playerId)
//        {
//            return GetPlayerData(playerId)?.HeroInfos ?? new List<PBBattleHeroInfo>();
//        }

//        /// <summary>
//        /// 根据英雄ID获取玩家阵容中的英雄信息（用于获取Level和AwakeLevel）
//        /// </summary>
//        /// <param name="playerId">玩家ID</param>
//        /// <param name="heroId">英雄ID</param>
//        /// <returns>阵容中的英雄信息，如果未找到则返回null</returns>
//        public PBBattleHeroInfo GetLineupHeroInfo(long playerId, int heroId)
//        {
//            var heroInfos = GetPlayerHeroInfos(playerId);
//            return heroInfos.FirstOrDefault(h => h.Id == heroId);
//        }

//        /// <summary>
//        /// 获取玩家服务器ID
//        /// </summary>
//        public string GetPlayerServerId(long playerId)
//        {
//            return GetPlayerData(playerId)?.ServerId ?? "unknown";
//        }

//        /// <summary>
//        /// 获取玩家名称
//        /// </summary>
//        public string GetPlayerName(long playerId)
//        {
//            return GetPlayerData(playerId)?.PlayerName ?? $"Player{playerId}";
//        }

//        /// <summary>
//        /// 获取玩家等级
//        /// </summary>
//        public int GetPlayerLevel(long playerId)
//        {
//            return GetPlayerData(playerId)?.PlayerLevel ?? 1;
//        }

//        /// <summary>
//        /// 获取玩家奖杯数
//        /// </summary>
//        public int GetPlayerTrophy(long playerId)
//        {
//            return GetPlayerData(playerId)?.Trophy ?? 1000;
//        }

//        /// <summary>
//        /// 获取玩家准备状态
//        /// </summary>
//        public bool IsPlayerReady(long playerId)
//        {
//            return GetPlayerData(playerId)?.IsReady ?? false;
//        }

//        /// <summary>
//        /// 获取玩家淘汰状态
//        /// </summary>
//        public bool IsPlayerEliminated(long playerId)
//        {
//            return GetPlayerData(playerId)?.IsEliminated ?? true;
//        }
        
//        /// <summary>
//        /// 设置玩家准备状态
//        /// </summary>
//        public void SetPlayerReady(long playerId, bool isReady)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            if (player == null || player.IsEliminated) return;

//            player.IsReady = isReady;
//            Log.Info($"[{LogName}] Player {playerId} ready status set to {isReady}");

//            // 检查是否所有玩家都已准备
//            if (isReady && CheckAllPlayersReady())
//            {
//                Log.Info($"[{LogName}] All players are ready!");
//                _eventBus.Publish(new AllPlayersReadyEvent(BattleId));
//            }
//        }

//        /// <summary>
//        /// 检查是否所有玩家都已准备
//        /// </summary>
//        public bool CheckAllPlayersReady()
//        {
//            ThrowIfNotInitialized();

//            var activePlayers = _players.Values.Where(p => !p.IsEliminated);
//            return activePlayers.All(p => p.IsReady);
//        }

//        /// <summary>
//        /// 新回合开始时重置所有玩家准备状态
//        /// </summary>
//        public void ResetPlayersReadyStatus()
//        {
//            ThrowIfNotInitialized();

//            foreach (var player in _players.Values.Where(p => !p.IsEliminated))
//            {
//                player.IsReady = false;
//            }
//            Log.Info($"[{LogName}] Reset all players ready status");
//        }
        
//        /// <summary>
//        /// 设置玩家对战匹配
//        /// </summary>
//        public void SetPlayerOpponents(List<(long player1, long player2)> playerPairs)
//        {
//            ThrowIfNotInitialized();

//            if (playerPairs == null) return;

//            foreach (var (player1, player2) in playerPairs)
//            {
//                var playerData1 = GetPlayerData(player1);
//                if (playerData1 != null)
//                {
//                    playerData1.OpponentId = player2;
//                }

//                var playerData2 = GetPlayerData(player2);
//                if (playerData2 != null)
//                {
//                    playerData2.OpponentId = player1;
//                }
//            }

//            Log.Info($"[{LogName}] Set player opponents, count: {playerPairs.Count} pairs");
//        }

//        /// <summary>
//        /// 获取玩家对手ID
//        /// </summary>
//        public long GetPlayerOpponent(long playerId)
//        {
//            return GetPlayerData(playerId)?.OpponentId ?? 0;
//        }

//        /// <summary>
//        /// 扣除玩家血量
//        /// </summary>
//        public void ReducePlayerHealth(long playerId, int amount = BattleConfig.GameRules.HealthLossPerDefeat)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            if (player == null || player.IsEliminated) return;

//            player.Health = Math.Max(0, player.Health - amount);
//            Log.Info($"[{LogName}] Player {playerId} health reduced by {amount}, current health: {player.Health}");

//            // 检查玩家是否被淘汰
//            if (player.Health <= 0)
//            {
//                EliminatePlayer(playerId);
//            }
//        }

//        /// <summary>
//        /// 保存玩家的棋盘数据
//        /// </summary>
//        public void SavePlayerBoardData(long playerId, List<Entity> entities)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            if (player == null) return;

//            player.BoardEntities.Clear();
//            foreach (var entity in entities)
//            {
//                // 只保存在棋盘上的实体（排除临时位）
//                if (entity.GridX > 0 && entity.GridY > 0 && entity.EntityId != 0)
//                {
//                    player.BoardEntities.Add(new PlayerBoardEntity(
//                        entity.ConfigId,
//                        entity.StarLevel,
//                        entity.GridX,
//                        entity.GridY
//                    ));
//                }
//            }

//            Log.Info($"[{LogName}] Saved board data: player:{playerId} entities:{player.BoardEntities.Count}");
//        }

//        /// <summary>
//        /// 获取玩家的棋盘数据
//        /// </summary>
//        public List<PlayerBoardEntity> GetPlayerBoardData(long playerId)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            return player?.BoardEntities ?? new List<PlayerBoardEntity>();
//        }

//        /// <summary>
//        /// 清空玩家的棋盘数据
//        /// </summary>
//        public void ClearPlayerBoardData(long playerId)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            if (player != null)
//            {
//                player.BoardEntities.Clear();
//                Log.Info($"[{LogName}] Cleared board data for player {playerId}");
//            }
//        }

//        /// <summary>
//        /// 保存玩家上一回合结束时的棋盘数据
//        /// </summary>
//        public void SavePlayerPreviousRoundBoardData(long playerId, List<Entity> entities)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            if (player == null) return;

//            player.PreviousRoundBoardEntities.Clear();
//            foreach (var entity in entities)
//            {
//                // 只保存在棋盘上的实体（排除临时位）
//                if (entity.GridX > 0 && entity.GridY > 0 && entity.EntityId != 0)
//                {
//                    player.PreviousRoundBoardEntities.Add(new PlayerBoardEntity(
//                        entity.ConfigId,
//                        entity.StarLevel,
//                        entity.GridX,
//                        entity.GridY
//                    ));
//                }
//            }

//            Log.Info($"[{LogName}] Saved prev round data: player:{playerId} entities:{player.PreviousRoundBoardEntities.Count}");
//        }

//        /// <summary>
//        /// 获取玩家上一回合的棋盘数据
//        /// </summary>
//        public List<PlayerBoardEntity> GetPlayerPreviousRoundBoardData(long playerId)
//        {
//            ThrowIfNotInitialized();

//            var player = GetPlayerData(playerId);
//            return player?.PreviousRoundBoardEntities ?? new List<PlayerBoardEntity>();
//        }
        
//        /// <summary>
//        /// 淘汰玩家
//        /// </summary>
//        public void EliminatePlayer(long playerId)
//        {
//            var player = GetPlayerData(playerId);
//            if (player == null || player.IsEliminated) return;

//            player.IsEliminated = true;
//            player.Health = 0;

//            Log.Info($"[{LogName}] Player {playerId} eliminated from battle {BattleId}");

//            // 发布玩家淘汰事件
//            _eventBus.Publish(new PlayerEliminatedEvent(BattleId, playerId));

//            // 检查游戏是否结束
//            CheckGameOver();
//        }

//        /// <summary>
//        /// 检查游戏是否结束
//        /// </summary>
//        public void CheckGameOver()
//        {
//            ThrowIfNotInitialized();

//            var activePlayers = GetActivePlayerIds();

//            // 如果只剩一名玩家，游戏结束
//            if (activePlayers.Count == 1)
//            {
//                var winnerId = activePlayers[0];
//                Log.Info($"[{LogName}] Game over! Winner: Player {winnerId}");
//                _eventBus.Publish(new GameOverEvent(BattleId, winnerId));
//            }
//        }

//        /// <summary>
//        /// 处理回合战斗结果
//        /// </summary>
//        /// <param name="battleResults">战斗结果 <玩家ID, 是否胜利></param>
//        public void HandleRoundBattleResults(Dictionary<long, bool> battleResults)
//        {
//            ThrowIfNotInitialized();

//            if (battleResults == null) return;

//            foreach (var (playerId, isWinner) in battleResults)
//            {
//                if (!isWinner)
//                {
//                    ReducePlayerHealth(playerId);
//                    Log.Info($"[{LogName}] Player {playerId} lost the battle");
//                }
//            }
//        }

//        /// <summary>
//        /// 处理平局情况
//        /// </summary>
//        /// <param name="player1Id">玩家1 ID</param>
//        /// <param name="player2Id">玩家2 ID</param>
//        public void HandleDraw(long player1Id, long player2Id)
//        {
//            ReducePlayerHealth(player1Id, BattleConfig.GameRules.HealthLossPerDraw);
//            ReducePlayerHealth(player2Id, BattleConfig.GameRules.HealthLossPerDraw);

//            Log.Info($"[{LogName}] Draw between Player {player1Id} and Player {player2Id}");
//        }

//        /// <summary>
//        /// 获取淘汰玩家数量
//        /// </summary>
//        public int GetEliminatedPlayerCount()
//        {
//            ThrowIfNotInitialized();
//            return _players.Values.Count(p => p.IsEliminated);
//        }

//        /// <summary>
//        /// 获取淘汰玩家列表
//        /// </summary>
//        public List<long> GetEliminatedPlayerIds()
//        {
//            ThrowIfNotInitialized();
//            return _players.Values
//                .Where(p => p.IsEliminated)
//                .Select(p => p.PlayerId)
//                .ToList();
//        }

//        /// <summary>
//        /// 获取当前存活玩家数量
//        /// </summary>
//        public int GetActivePlayerCount()
//        {
//            ThrowIfNotInitialized();
//            return _players.Values.Count(p => !p.IsEliminated);
//        }

//        protected override void OnClear()
//        {
//            _players.Clear();
//            _eventBus?.Clear();
//        }

//        /// <summary>
//        /// 订阅事件
//        /// </summary>
//        public void Subscribe<T>(Action<T> handler) where T : BattleEvent
//        {
//            _eventBus.Subscribe(handler);
//        }
//    }
//}
