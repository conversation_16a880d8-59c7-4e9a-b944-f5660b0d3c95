﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;

namespace Aurora.Framework
{
    public class TService : AService
    {
        private readonly Dictionary<long, TChannel> m_Channels = new Dictionary<long, TChannel>();
        private Socket m_Acceptor;
        private readonly SocketAsyncEventArgs m_AcceptArgs = new SocketAsyncEventArgs();

        public override void Update()
        {
            foreach(var p in m_Channels)
            {
                if(p.Value != null)
                {
                    p.Value.Update();
                }
            }
        }
        //非监听的网络Service
        public TService(ThreadSyncContext syncContext)
        {
            ThreadSyncContext = syncContext;
            
        }
        //监听的网络Service
        public TService(ThreadSyncContext syncContext, IPEndPoint ipEndPoint)
        {
            ThreadSyncContext = syncContext;

            try
            {
                // 记录Socket服务端监听开始
                SocketLogHelper.LogSocketServerStart("TService", ipEndPoint);

                m_Acceptor = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                m_Acceptor.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                m_AcceptArgs.Completed += OnComplete;
                m_Acceptor.Bind(ipEndPoint);
                m_Acceptor.Listen(1000);
                ThreadSyncContext.Post(this.AcceptAsync);

                // 记录Socket服务端监听成功
                SocketLogHelper.LogSocketServerSuccess("TService", ipEndPoint);
            }
            catch (Exception ex)
            {
                // 记录Socket服务端监听失败
                SocketLogHelper.LogSocketServerFailed("TService", ipEndPoint, ex.Message);
                throw;
            }
        }

        public override void Remove(long id)
        {
            if (m_Channels.TryGetValue(id, out TChannel channel))
            {
                ReferencePool.Release(channel);
            }
            m_Channels.Remove(id);
        }

        public override void Release()
        {
            foreach (long id in m_Channels.Keys.ToArray())
            {
                TChannel channel = m_Channels[id];
                ReferencePool.Release(channel);
            }
            m_Channels.Clear();
            ThreadSyncContext = null;
        }

        public override void Send(long channelId, Packet pkt)
        {
            try
            {
                TChannel channel = null;
                if (!m_Channels.TryGetValue(channelId, out channel))
                {
                    Log.Error("TService want to send with null channel!");
                    return;
                }
                channel.Send(pkt);
            }
            catch (Exception e)
            {
                Log.Exception($"Exception:{e.ToString()}\nStackTrace:\n{e.StackTrace}");
            }
        }

        public override void Create(long id, IPEndPoint ipEndPoint)
        {
            //TChannel channel = new TChannel(id, ipEndPoint);
            //m_Channels.Add(channel.ID, channel);
        }

        //主线程调用
        private void OnAcceptComplete(SocketError socketError, Socket acceptSocket)
        {
            if (m_Acceptor == null)
                return;

            if (socketError != SocketError.Success)
            {
                Log.Error($"accept error: {socketError.ToString()}");
                return;
            }

            try
            {
                // 记录Socket连接接受
                if (acceptSocket != null && acceptSocket.RemoteEndPoint != null)
                {
                    SocketLogHelper.LogSocketAccept("TService", (IPEndPoint)acceptSocket.RemoteEndPoint);
                }
                
                //long id = /*IDGenerater.Instance.InstanceId()*/0;
                //TChannel channel = new TChannel(id, acceptSocket);
                //m_Channels.Add(channel.ID, channel);
                //long channelId = channel.ID;
                //Log.Debug($"Accept Adress:{channel.RemoteAddress.ToString()} successful!");
                ////通知外层网络组件，加上对应的Session
                //OnAccept(channelId, channel.RemoteAddress);
            }
            catch (Exception exception)
            {
                Log.Error($"Exception:{exception.ToString()}\nStackTrace:\n{exception.StackTrace}");
            }

            // 开始新的accept
            AcceptAsync();
        }

        private void AcceptAsync()
        {
            m_AcceptArgs.AcceptSocket = null;
            if (m_Acceptor.AcceptAsync(m_AcceptArgs))
            {
                return;
            }
            //同步完成
            OnAcceptComplete(m_AcceptArgs.SocketError, m_AcceptArgs.AcceptSocket);
        }

        //异步网络线程调用
        private void OnComplete(object sender, SocketAsyncEventArgs e)
        {
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.Accept:
                    SocketError socketError = e.SocketError;
                    Socket acceptSocket = e.AcceptSocket;
                    ThreadSyncContext.Post(() => { this.OnAcceptComplete(socketError, acceptSocket); });
                    break;
                default:
                    throw new Exception($"socket error: {e.LastOperation}");
            }
        }

        public override bool IsReleased()
        {
            return ThreadSyncContext == null;
        }
    }
}
