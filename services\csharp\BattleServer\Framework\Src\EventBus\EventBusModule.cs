/*
 * @description: Event bus module
 * @Auther: Kimsee
 * @Date: 2022-11-16 21:09
 * 
*/
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Aurora.Framework
{

    public struct DelayEvent
    {
        public long m_ExpireTime;
        public EventParamBase m_evt;
        public long m_SenderId;
    }
    public interface IStaticEventCallbackBuilder
    {
        public object Build(MethodInfo mi, EventInvokerAttribute attr);
    }
    public interface IDynamicEventCallbackBuilder
    {
        public object Build(MethodInfo mi, EventDynamicListenerAttribute attr);
    }
    public class StaticEntityEventCallbackBuilder : IStaticEventCallbackBuilder
    {
        public object Build(MethodInfo mi, EventInvokerAttribute attr)
        {
            if (attr == null) return null;
            Type actionGenericType = typeof(Action<,>);
            Type actionType = actionGenericType.MakeGenericType(new Type[] { typeof(Entity), attr.ParamType });//replace
            Type callbackGenericType = typeof(EventInvokeCallback<>);//replace
            Type callbackType = callbackGenericType.MakeGenericType(new Type[] { attr.ParamType });
            var cb = Activator.CreateInstance(callbackType, new object[] { mi, Delegate.CreateDelegate(actionType, mi) });
            return cb;
        }
    }
    public class DynamicEntityEventCallbackBuilder : IDynamicEventCallbackBuilder
    {
        public object Build(MethodInfo mi, EventDynamicListenerAttribute attr)
        {
            if (attr == null) return null;
            Type actionGenericType = typeof(Action<,,>);
            Type actionType = actionGenericType.MakeGenericType(new Type[] { typeof(Entity), typeof(Entity), attr.ParamType });
            Type callbackGenericType = typeof(EventDynamicCallback<>);
            Type callbackType = callbackGenericType.MakeGenericType(new Type[] { attr.ParamType });
            var cb = Activator.CreateInstance(callbackType, new object[] { mi, Delegate.CreateDelegate(actionType, mi) });
            return cb;
        }
    }

    public class EventParamBase : IReference
    {
        public long ID;

        public virtual void Clear()
        {
            ID = -1;
        }
    }
    public interface IEventInvokeCallback
    {
        public void Invoke(EntitySystem es, long senderInstanceID, EventParamBase arg);
    }
    public class EventInvokeCallback<T> : IEventInvokeCallback where T : EventParamBase
    {
        public MethodInfo Method { get; set; }
        public Action<Entity, T> Callback { get; set; }
        public EventInvokeCallback(MethodInfo mi, Action<Entity, T> cb)
        {
            Method = mi;
            Callback = cb;
        }
        public void Invoke(EntitySystem es, long senderInstanceID, EventParamBase arg)
        {
            try
            {
                if (Callback != null)
                {
                    if (es == null) return;
                    Entity sender = es.GetEntity(senderInstanceID);
                    Callback(sender, arg as T);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Event invoke call back error ：{ex.Message} , StackTrace : {ex.StackTrace}");
            }
        }
    }

    public interface IEventDynamicCallback
    {
        public void Invoke(EntitySystem es, long receiverInstanceID, long senderInstanceID, EventParamBase arg);
    }
    public class EventDynamicCallback<T> : IEventDynamicCallback where T : EventParamBase
    {
        public MethodInfo Method { get; set; }
        public Action<Entity, Entity, T> Callback { get; set; }
        public EventDynamicCallback(MethodInfo mi, Action<Entity, Entity, T> cb)
        {
            Set( mi, cb);
        }
        public void Set(MethodInfo mi, Action<Entity, Entity, T> cb)
        {
            Method = mi;
            Callback = cb;
        }
        public void Clean()
        {
            Method = null;
            Callback = null;
        }
        public void Invoke(EntitySystem es, long receiverInstanceID, long senderInstanceID, EventParamBase arg)
        {
            try
            {
                if (Callback != null)
                {
                    if (es == null) return;
                    Entity receiver = es.GetEntity(receiverInstanceID);
                    Entity sender = es.GetEntity(senderInstanceID);
                    Callback(receiver, sender, arg as T);
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Event dynamic call back error  ：{ex.Message} , StackTrace : {ex.StackTrace}");
            }
        }
    }

    //主线程驱动,用于分发所有event,驱动异步协程
    public class EventBus
    {
        private static IStaticEventCallbackBuilder m_StaticCBBuilder = new StaticEntityEventCallbackBuilder();
        private static IDynamicEventCallbackBuilder m_DynamicCBBuilder = new DynamicEntityEventCallbackBuilder();

        private static readonly Dictionary<string, Type> m_AllTypes = new Dictionary<string, Type>();
        private static readonly UnOrderMultiHashSet<Type, Type> m_Types = new UnOrderMultiHashSet<Type, Type>();
        private static Dictionary<long, List<IEventInvokeCallback>> m_AllInvokes = new Dictionary<long, List<IEventInvokeCallback>>();
        private static Dictionary<long, List<IEventDynamicCallback>> m_AllListeners = new Dictionary<long, List<IEventDynamicCallback>>();
        private static Dictionary<long, List<IEventDynamicCallback>> m_DelListeners = new Dictionary<long, List<IEventDynamicCallback>>();
        private static readonly Dictionary<Type, long> m_Type2ID = new Dictionary<Type, long>();

        //线程不安全集合
        private List<DelayEvent> m_DelayEvents = new List<DelayEvent>();
        private EntitySystem m_ThreadEntitySystem;
        public EventBus(EntitySystem threadOwnEntitySystem)
        {
            m_ThreadEntitySystem = threadOwnEntitySystem;
        }

        public static void PreInit_SetCallbackFormat(IStaticEventCallbackBuilder sBuilder, IDynamicEventCallbackBuilder dBuilder)
        {
            m_StaticCBBuilder = sBuilder;
            m_DynamicCBBuilder = dBuilder;
        }
        
        public static bool ClientInit()
        {
            m_AllInvokes.Clear();
            m_AllListeners.Clear();
            m_DelListeners.Clear();
            List<Type> list = AssemblyManager.Instance.GetAllTypes<EventAttribute>();
            for(int i = 0; i < list.Count; i ++)
            {
                // 整理Event
                EventAttribute evtAttr = list[i].GetCustomAttribute<EventAttribute>();
                if (evtAttr != null)
                {
                    m_Type2ID.TryAdd(list[i], evtAttr.EventId);
                }
            }
            Dictionary<Type, List<string>> methodsList = AssemblyManager.Instance.GetAllTypeMethods<EventInvokerAttribute>();
            // 查找所有类的Method里含有EventInvoker标签的,建立callback列表
            foreach (Type type in methodsList.Keys)
            {
                // 整理callback
                List<string> methods = methodsList[type];
                for (int j = 0; j < methods.Count; ++j)
                {
                    MethodInfo tmpMethodInfo = type.GetMethod(methods[j]);
                    object[] attrs = tmpMethodInfo.GetCustomAttributes(typeof(EventInvokerAttribute), false);

                    if (attrs.Length > 0)
                    {
                        EventInvokerAttribute attr = attrs[0] as EventInvokerAttribute;
                        if (!m_Type2ID.ContainsKey(attr.ParamType))
                            continue;

                        long evtId = m_Type2ID[attr.ParamType];
                        var cb = m_StaticCBBuilder.Build(tmpMethodInfo, attr);
                        //todo: 直接转接口,消灭运行时开销

                        if (!m_AllInvokes.TryGetValue(evtId, out var dict))
                        {
                            dict = new List<IEventInvokeCallback>();
                            m_AllInvokes.Add(evtId, dict);
                        }

                        if (!dict.Contains(cb as IEventInvokeCallback))
                        {
                            dict.Add(cb as IEventInvokeCallback);
                        }
                        else
                        {
                            throw new Exception($"action type duplicate: {type.FullName} {tmpMethodInfo.Name}");
                        }
                    }
                }

            }

            methodsList = AssemblyManager.Instance.GetAllTypeMethods<EventDynamicListenerAttribute>();
            // 查找所有类的Method里含有EventInvoker标签的,建立callback列表
            foreach (Type type in methodsList.Keys)
            {
                List<string> methods = methodsList[type];
                for (int j = 0; j < methods.Count; ++j)
                {
                    MethodInfo tmpMethodInfo = type.GetMethod(methods[j]);
                    object[] attrDynamicList = tmpMethodInfo.GetCustomAttributes(typeof(EventDynamicListenerAttribute), false);

                    if (attrDynamicList.Length > 0)
                    {
                        EventDynamicListenerAttribute attr = attrDynamicList[0] as EventDynamicListenerAttribute;
                        if (!m_Type2ID.ContainsKey(attr.ParamType))
                            continue;
                        long evtId = m_Type2ID[attr.ParamType];
                        var cb = m_DynamicCBBuilder.Build(tmpMethodInfo, attr);

                        if (!m_AllListeners.TryGetValue(evtId, out var dict))
                        {
                            dict = new List<IEventDynamicCallback>();
                            m_AllListeners.Add(evtId, dict);
                        }

                        try
                        {
                            dict.Add(cb as IEventDynamicCallback);
                        }
                        catch (Exception e)
                        {
                            throw new Exception($"listener action type duplicate: {type.FullName} {tmpMethodInfo.Name}", e);
                        }
                    }
                }
            }
            return true;
        }
        public static bool Init(string configFilePath, bool clearList = true)
        {
            Log.Info("EventBus init begin.");
            ParseAllAssembly(clearList);
            Log.Info("EventBus init end.");
            return true;
        }
        public bool Release()
        {
            return true;
        }

        public static T Create<T>() where T : EventParamBase, new()
        {
            long id = 0;
            if (EventBus.m_Type2ID.TryGetValue(typeof(T), out id))
            {
                T evt = ReferencePool.Acquire<T>();
                //T evt = new T(); 
                evt.ID = id;
                return evt;
            }
            throw new FrameworkException($"EventBus Create Event[{typeof(T).FullName}] failed!");
        }
        public static EventParamBase Create(string name)
        {
            if (!m_AllTypes.TryGetValue(name, out Type value))
            {
                return null;
            }
            long id = 0;
            Type t = value;
            if (EventBus.m_Type2ID.TryGetValue(t, out id))
            {
                EventParamBase evt = Activator.CreateInstance(t) as EventParamBase;
                evt.ID = id;
                return evt;
            }
            throw new FrameworkException($"EventBus Create Event[{name}] failed!");
        }
        //给客户端用：优化客户端运行时动态取反射
        public static long GetEventID<T>()
        {
            Type type = typeof(T);
            if (m_Type2ID.TryGetValue(type, out long result))
            {
                return result;
            }
            return -1;
        }
        public static long GetEventID(Type type)
        {
            if (m_Type2ID.TryGetValue(type, out long result))
            {
                return result;
            }
            return -1;
        }
        public static void Recycle<T>(T evt) where T : EventParamBase, new()
        {
            // todo: objectpool
            ReferencePool.Release(evt);
        }

        public void Run(double deltaTime)
        {
            //todo : 这里处理异步

            // 延迟消息处理
            // 
            if (m_DelayEvents.Count > 0)
            {
                for (int i = 0; i < m_DelayEvents.Count;)
                {
                    DelayEvent evt = m_DelayEvents[i];
                    if (evt.m_ExpireTime <= ATimer.Instance.SystemTicks)
                    {
                        Entity rSender = m_ThreadEntitySystem.GetEntity(evt.m_SenderId);
                        Send<EventParamBase>(evt.m_evt, rSender);
                        m_DelayEvents.RemoveAt(i);
                    }
                    else
                        ++i;
                }
            }
        }
        // 检索整理所有Assembly,初始化EventBus
        private static void ParseAllAssembly(bool clearList = true)
        {
            m_AllTypes.Clear();
            foreach (Type addType in AssemblyManager.Instance.GetAllTypes())
            {
                m_AllTypes[addType.FullName] = addType;
            }

            // 整理所有含基础类标签的类
            m_Types.Clear();
            // 所有标签类
            List<Type> baseAttributeTypes = GetBaseAttributes(AssemblyManager.Instance.GetAllTypesDic());
            foreach (Type baseAttributeType in baseAttributeTypes)
            {
                foreach (Type type in AssemblyManager.Instance.GetAllTypes())
                {
                    if (type.IsAbstract)
                    {
                        continue;
                    }
                    //这个只能取到类打的标签
                    object[] objects = type.GetCustomAttributes(baseAttributeType, false);
                    if (objects.Length == 0)
                    {
                        continue;
                    }

                    m_Types.Add(baseAttributeType, type);
                }
            }



            // 整理Event
            m_Type2ID.Clear();
            foreach (Type type in AssemblyManager.Instance.GetAllTypes())
            {
                // 整理Event
                EventAttribute evtAttr = type.GetCustomAttribute<EventAttribute>();
                if (evtAttr != null)
                {
                    if (!m_Type2ID.TryAdd(type, evtAttr.EventId))
                    {
                        Log.Error($"EventBus add event[{evtAttr.EventId}] type[{type.FullName}] failed, maybe dupulicated");
                    }
                }
            }
            // 查找所有类的Method里含有EventInvoker标签的,建立callback列表
            if(clearList)
            {
                m_AllInvokes.Clear();
                m_AllListeners.Clear();
            }
            foreach (Type type in AssemblyManager.Instance.GetAllTypes())
            {
                // 整理callback
                MethodInfo[] mi = type.GetMethods();
                for (int j = 0; j < mi.Length; ++j)
                {
                    MethodInfo tmpMethodInfo = mi[j];
                    object[] attrs = tmpMethodInfo.GetCustomAttributes(typeof(EventInvokerAttribute), false);

                    if (attrs.Length > 0)
                    {
                        Log.Info($"EventInvokerAttribute Type:[{type.FullName}]--Method:[{tmpMethodInfo.Name}]");
                        EventInvokerAttribute attr = attrs[0] as EventInvokerAttribute;
                        if (!m_Type2ID.ContainsKey(attr.ParamType))
                            continue;

                        long evtId = m_Type2ID[attr.ParamType];
                        var cb = m_StaticCBBuilder.Build(tmpMethodInfo, attr);
                        //todo: 直接转接口,消灭运行时开销

                        if (!m_AllInvokes.TryGetValue(evtId, out var dict))
                        {
                            dict = new List<IEventInvokeCallback>();
                            m_AllInvokes.Add(evtId, dict);
                        }

                        try
                        {
                            dict.Add(cb as IEventInvokeCallback);
                        }
                        catch (Exception e)
                        {
                            throw new Exception($"action type duplicate: {type.FullName} {tmpMethodInfo.Name}", e);
                        }

                    }

                    object[] attrDynamicList = tmpMethodInfo.GetCustomAttributes(typeof(EventDynamicListenerAttribute), false);

                    if (attrDynamicList.Length > 0)
                    {
                        Log.Info($"EventDynamicListenerAttribute Type:[{type.FullName}]--Method:[{tmpMethodInfo.Name}]");
                        EventDynamicListenerAttribute attr = attrDynamicList[0] as EventDynamicListenerAttribute;
                        if (!m_Type2ID.ContainsKey(attr.ParamType))
                            continue;
                        long evtId = m_Type2ID[attr.ParamType];
                        var cb = m_DynamicCBBuilder.Build(tmpMethodInfo, attr);

                        if (!m_AllListeners.TryGetValue(evtId, out var dict))
                        {
                            dict = new List<IEventDynamicCallback>();
                            m_AllListeners.Add(evtId, dict);
                        }

                        try
                        {
                            dict.Add(cb as IEventDynamicCallback);
                        }
                        catch (Exception e)
                        {
                            throw new Exception($"listener action type duplicate: {type.FullName} {tmpMethodInfo.Name}", e);
                        }

                    }
                }

            }



        }
        private static List<Type> GetBaseAttributes(Dictionary<string, Type> addTypes)
        {
            List<Type> attributeTypes = new List<Type>();
            foreach (Type type in addTypes.Values)
            {
                if (type.IsAbstract)
                {
                    continue;
                }

                if (type.IsSubclassOf(typeof(AuroraAttribute)))
                {
                    attributeTypes.Add(type);
                }
            }

            return attributeTypes;
        }

        public static HashSet<Type> GetTypes(Type type)
        {
            if (!m_Types.TryGetValue(type, out HashSet<Type> value))
            {
                return new HashSet<Type>();
            }

            return value;
        }

        // 静态的事件处理,只处理发送者自身
        public void Send<T>(T eventParam, Entity rSender)
        {
            //todo: 探测递归调用
            EventParamBase iEvent = eventParam as EventParamBase;
            if (iEvent == null)
            {
                Recycle(iEvent);
                return;
            }
            if (null == rSender)
            {
                Recycle(iEvent);
                return;
            }
            // 先调用静态的事件,静态的只处理自己的事件
            if (m_AllInvokes.TryGetValue(iEvent.ID, out var dict))
            {
                foreach (IEventInvokeCallback cb in dict)
                {
                    if (cb != null)
                    {
                        try
                        {
                            cb.Invoke(m_ThreadEntitySystem, rSender.InstanceID, iEvent);
                        }
                        catch (Exception e)
                        {
                            Log.Error($"Event error  ：{e.Message} , StackTrace : {e.StackTrace}");
                        }
                    }
                }
            }
            EventListenerComponent comEventListener = rSender.GetComponent<EventListenerComponent>();
            if (comEventListener == null)
            {
                Recycle(iEvent);
                return; 
            }
            else
            {
                if (!comEventListener.CanTriggerEvent(iEvent.ID))
                {
                    Recycle(iEvent);
                    return;
                }
            }
            comEventListener.TriggerEvent<T>(iEvent.ID, eventParam, rSender);
            Recycle(iEvent);
        }

        // 非线程安全,延迟发送消息
        public void DelaySend<T>(T eventParam, Entity rSender, long delayMs)
        {
            EventParamBase iEvent = eventParam as EventParamBase;
            if (iEvent != null)
            {
                if (rSender == null) return;
                DelayEvent evt;
                evt.m_evt = iEvent;
                evt.m_SenderId = rSender.InstanceID;
                evt.m_ExpireTime = ATimer.Instance.SystemTicks + delayMs;
                m_DelayEvents.Add(evt);//非线程安全
                return;
            }
        }
        // 外部禁止使用这个接口!!!动态的事件监听,需要发送和接受者双方
        public void SendToListener<T>(T eventParam, Entity rSender, long receiverInstanceID)
        {
            EventParamBase iEvent = eventParam as EventParamBase;
            if (iEvent == null)
            {
                return;
            }
            if (rSender == null) return;
            EventListenerComponent comEventListener = rSender.GetComponent<EventListenerComponent>();
            if (comEventListener == null)
                return;
            else
            {
                if (!comEventListener.CanTriggerEvent(iEvent.ID))
                {
                    return;
                }
            }
            // 先调用静态的事件,静态的只处理自己的事件
            if (m_AllListeners.TryGetValue(iEvent.ID, out List<IEventDynamicCallback> dict))
            {
                for(int i = 0; i < dict.Count; i++)
                {
                    if (dict[i] != null)
                    {
                        try
                        {
                            dict[i].Invoke(m_ThreadEntitySystem, receiverInstanceID, rSender.InstanceID, iEvent);
                        }
                        catch (Exception e)
                        {
                            Log.Error($"Event listener error  ：{e.Message} , StackTrace : {e.StackTrace}");
                        }
                    }
                }
            }
            if (m_DelListeners.Count > 0)
            {
                foreach (var kvp in m_DelListeners)
                {
                    for(int i = 0; i < kvp.Value.Count;i++)
                    {
                        ToRemoveClientListener(kvp.Key, kvp.Value[i]);
                    }
                }
                m_DelListeners.Clear();
            }
        }
        private void ToRemoveClientListener(long evtID, IEventDynamicCallback cb)
        {
            List<IEventDynamicCallback> dict;
            if (!m_AllListeners.TryGetValue(evtID, out dict))
            {
                return;
            }
            try
            {
                if (dict.Contains(cb as IEventDynamicCallback))
                {
                    dict.Remove(cb as IEventDynamicCallback);
                }
            }
            catch (Exception e)
            {
                throw new Exception($"RemoveGameListener action type evtID : {evtID} . ", e);
            }
        }
        public void AddClientListener(long evtID, IEventDynamicCallback cb)
        {
            List<IEventDynamicCallback> dict;
            if (!m_AllListeners.TryGetValue(evtID, out dict))
            {
                dict = new List<IEventDynamicCallback>();
                m_AllListeners.Add(evtID, dict);
            }
            try
            {
                if (!dict.Contains(cb as IEventDynamicCallback))
                {
                    dict.Add(cb as IEventDynamicCallback);
                }
            }
            catch (Exception e)
            {
                throw new Exception($"AddGameListener action type evtID : {evtID} . ", e);
            }
        }
        public void RemoveClientListener(long evtID, IEventDynamicCallback cb)
        {
            if (!m_DelListeners.TryGetValue(evtID, out List<IEventDynamicCallback> value))
            {
                m_DelListeners.Add(evtID, new List<IEventDynamicCallback>());
            }
            m_DelListeners[evtID].Add(cb);
        }
        public int GetClientListenerCount(long evtID)
        {
            List<IEventDynamicCallback> dict;
            if (!m_AllListeners.TryGetValue(evtID, out dict))
            {
                return 0;
            }
            try
            {
                return dict.Count;
            }
            catch (Exception e)
            {
                throw new Exception($"RemoveGameListener action type evtID : {evtID} . ", e);
            }
        }
    }
}