/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableGuildConfig struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 名字字符最大长度
	NameMaxLength int32
	// 宣言字符最大长度
	NoticeMaxLength int32
	// 图标最大个数[1-5]
	IconMaxCount int32
	// 创建消耗的钻石数
	CreateDiamondCost int32
	// 审批关卡需求配置4档关卡ID
	ReqStageIds []int32
	// 日志最大条数
	LogMaxCount int32
	// 聊天最大条数
	ChatMaxCount int32
	// 修改名称消耗的钻石数
	EditNameDiamondCost int32
	// 会长超过7天未登录被弹劾时间，单位：秒
	PresidentOfflineTime1 int32
	// 会长超过14天未登录自动转让时间，单位：秒
	PresidentOfflineTime2 int32
	// 入会申请最大数量
	ApplyMaxCount int32
	// 世界聊天发布招募消耗钻石
	WorldSendRecruitCost int32
	// 世界聊天发布招募信息最大字符长度
	WorldSendRecruitTextMaxNum int32
	// 未砍价通知CD时间，单位：秒
	BargainingCDTime int32
	// 商店刷新类型0每日刷新1每周刷新
	ShopRefreshType int32
	// 公会推荐列表刷新CD时间，单位：秒
	RecommendCDTime int32
	// 申请同一公会CD时间，单位：秒
	ApplySameCDTime int32
	// 每日申请上限
	PlayerApplyMaxNum int32
	// 退出(主动)公会CD时间，单位：秒
	QuitCDTime int32
	// 退出(被踢)公会CD时间，单位：秒
	KickCDTime int32
	// 公会列表刷新个数
	RecommendMaxCount int32
	// 公会列表刷新活跃前100名
	RecommendActiveMaxCount int32
	// 公会排行列表最大个数
	RankMaxCount int32
	// 公会科技初始ID
	TechInitID int32
	// 公会科技强化材料物品ID 6表示公会币
	TechLevelupItemId int32
	// 公会科技重置钻石数
	TechResetDiamondCost int32
	// 公会普通商品消耗材料物品ID 6表示公会币
	GoodsCostItemId int32
	// 公会砍价礼包消耗材料物品ID 6表示公会币
	BargainingGoodsCostItemId int32
	// 未砍价邮件标题
	NoBargainingTitle int32
	// 未砍价邮件内容
	NoBargainingContent int32
	// BOSS购买最大次数上限
	BossBuyLimit int32
	// BOSS购买价格
	BossBuyPrice int32
	// BOSS击杀邮件标题
	BossKillTitle int32
	// BOSS击杀邮件内容
	BossKillContent int32
	// BOSS排行邮件标题
	BossRankTitle int32
	// BOSS排行邮件内容
	BossRankContent int32
	// BOSS功能开服后多长时间后开启，单位：秒
	BossOpenTime int32
	// 科技功能开服后多长时间后开启，单位：秒
	TechOpenTime int32
}




// TableGuildConfigData 表格
type TableGuildConfigData struct {
	file    string
	dataMap map[int32]*TableGuildConfig
	Data    []*TableGuildConfig
	md5     string
}

// load 加载
func (tb *TableGuildConfigData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableGuildConfig{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableGuildConfig, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuildConfig)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableGuildConfig, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableGuildConfigData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableGuildConfig{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableGuildConfig, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuildConfig)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableGuildConfigData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableGuildConfigData) GetById(id int32) *TableGuildConfig {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableGuildConfigData) GetCloneById(id int32) *TableGuildConfig {
	v := tb.dataMap[id]
	out := &TableGuildConfig{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableGuildConfigData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableGuildConfigData) Foreach(call func(*TableGuildConfig) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableGuildConfigData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableGuildConfigData) Clone() ITable {
	ntb := &TableGuildConfigData{
		file:    tb.file,
		dataMap: make(map[int32]*TableGuildConfig),
		Data:    make([]*TableGuildConfig, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableGuildConfig{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
