/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TablePayment struct {
	// ============= 变量定义 =============
	// 序列id
	ID int32
	// 名字
	Name int32
	// 首充标记
	FirstFlag int32
	// 商品类型（1 普通充值 2 普通月卡 3 超级月卡）
	GoodType int32
	// 商品价格
	Price int32
	// 钻石数量
	DiamondNum int32
	// 首冲赠送
	FirstBuy int32
	// 非首充赠送
	ExDiamondNum int32
	// 米大师表ID
	MidasDiamondId int32
	// 商品价格
	PriceShow float32
	// 消耗多少现金券
	CashCouponPay int32
}




// TablePaymentData 表格
type TablePaymentData struct {
	file    string
	dataMap map[int32]*TablePayment
	Data    []*TablePayment
	md5     string
}

// load 加载
func (tb *TablePaymentData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TablePayment{}))
	if err != nil {
		return err
	}

	typeData := make([]*TablePayment, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TablePayment)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TablePayment, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TablePaymentData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TablePayment{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TablePayment, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TablePayment)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TablePaymentData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TablePaymentData) GetById(id int32) *TablePayment {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TablePaymentData) GetCloneById(id int32) *TablePayment {
	v := tb.dataMap[id]
	out := &TablePayment{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TablePaymentData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TablePaymentData) Foreach(call func(*TablePayment) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TablePaymentData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TablePaymentData) Clone() ITable {
	ntb := &TablePaymentData{
		file:    tb.file,
		dataMap: make(map[int32]*TablePayment),
		Data:    make([]*TablePayment, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TablePayment{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
