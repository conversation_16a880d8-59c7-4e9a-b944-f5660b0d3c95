#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityGift
	{

		public static readonly string TName="ActivityGift.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 礼包表组ID 
		/// </summary> 
		public int GroupId {get; set;}
		/// <summary> 
		/// 货币类型 
		/// </summary> 
		public int CurrencyType {get; set;}
		/// <summary> 
		/// 价格 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 类型 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 类型参数 
		/// </summary> 
		public int TypeValue {get; set;}
		/// <summary> 
		/// 触发条件 
		/// </summary> 
		public int TriggerCondition {get; set;}
		/// <summary> 
		/// 条件参数 
		/// </summary> 
		public int TriggerConditionValue {get; set;}
		/// <summary> 
		/// 限购次数 
		/// </summary> 
		public int BuyNumber {get; set;}
		/// <summary> 
		/// 重置类型 
		/// </summary> 
		public int ResetType {get; set;}
		/// <summary> 
		/// 奖励 
		/// </summary> 
		public int[][] Drop {get; set;}
		/// <summary> 
		/// 持续时间/S 
		/// </summary> 
		public int Duration {get; set;}
		/// <summary> 
		/// 标签文字 
		/// </summary> 
		public int IconDes {get; set;}
		#endregion

		public static TableActivityGift GetData(int ID)
		{
			return TableManager.ActivityGiftData.Get(ID);
		}

		public static List<TableActivityGift> GetAllData()
		{
			return TableManager.ActivityGiftData.GetAll();
		}

	}
	public sealed partial class TableActivityGiftData
	{
		private Dictionary<int, TableActivityGift> dict = new Dictionary<int, TableActivityGift>();
		private List<TableActivityGift> dataList = new List<TableActivityGift>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityGift.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityGift>>(jsonContent);
			foreach (TableActivityGift config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityGift Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityGift item))
				return item;
			return null;
		}

		public List<TableActivityGift> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
