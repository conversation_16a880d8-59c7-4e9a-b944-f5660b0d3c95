/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableTimeGiftPacks struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 礼包类型（GiftPacksType枚举）1-日礼包 2-周礼包 3-月礼包
	GiftPacksType int32
	// 礼包名称
	Name int32
	// 礼包价格
	NowPrice int32
	// 礼包图标
	Icon string
	// 推荐礼包背景图
	GiftBg string
	// 礼包描述
	GiftPacksDesc int32
	// 礼包ID（DropGroupId）
	DropGroupId int32
	// 终身可购买次数
	LifeLongBuyLimit int32
	// 本周期可购买次数
	BuyLimit int32
	// 购买类型（GiftPacksBuyType枚举）0-人民币 1-游戏货币
	BuyType int32
	// 购买消耗(货币或普通物品 id|数量)，购买类型为游戏货币不填时代表免费
	Cost []int32
	// 折扣
	Discount int32
	// 刷新限制条件-开服天数(开服当天是0)（闭区间）(大于等于配一个参数就行)
	OpenServerDayLimit []int32
	// 刷新限制条件-关卡（闭区间）(大于等于配一个参数就行)
	StageLimit []int32
	// 刷新限制条件-前置商城礼包（前置礼包达到终身限购次数才算完成）
	PreGiftPacksLimit int32
	// MidasItem表ID
	MidasItemId int32
	// 购买条件限制（关卡）
	BuyStageLimit int32
}




// TableTimeGiftPacksData 表格
type TableTimeGiftPacksData struct {
	file    string
	dataMap map[int32]*TableTimeGiftPacks
	Data    []*TableTimeGiftPacks
	md5     string
}

// load 加载
func (tb *TableTimeGiftPacksData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableTimeGiftPacks{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableTimeGiftPacks, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableTimeGiftPacks)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableTimeGiftPacks, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableTimeGiftPacksData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableTimeGiftPacks{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableTimeGiftPacks, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableTimeGiftPacks)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableTimeGiftPacksData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableTimeGiftPacksData) GetById(id int32) *TableTimeGiftPacks {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableTimeGiftPacksData) GetCloneById(id int32) *TableTimeGiftPacks {
	v := tb.dataMap[id]
	out := &TableTimeGiftPacks{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableTimeGiftPacksData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableTimeGiftPacksData) Foreach(call func(*TableTimeGiftPacks) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableTimeGiftPacksData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableTimeGiftPacksData) Clone() ITable {
	ntb := &TableTimeGiftPacksData{
		file:    tb.file,
		dataMap: make(map[int32]*TableTimeGiftPacks),
		Data:    make([]*TableTimeGiftPacks, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableTimeGiftPacks{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
