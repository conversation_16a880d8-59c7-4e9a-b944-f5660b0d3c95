﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-27
//*********************************************************
/*
namespace Aurora.Framework
{
    public class LocationProxyComponent:BaseComponent,IAwake
    {
        public static LocationProxyComponent Instance;
    }

    [ComponentSystem(typeof(LocationProxyComponent))]
    public static class LocationProxyComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(LocationProxyComponent self)
        {
            LocationProxyComponent.Instance = self;
        }

        private static long GetLocationID(long key)
        {
            //TODO:这里读取服务器配置，得到进程ID
            return 1;
        }
        public static void Add(this LocationProxyComponent self, long entityID,long instanceID)
        {
            
        }

        public static void Remove(this LocationProxyComponent self,long entityID)
        {

        }

        public static async ATask<long> Get(this LocationProxyComponent self, long entityID)
        {
            return await new ATask<long>();
        }
    }
}
*/