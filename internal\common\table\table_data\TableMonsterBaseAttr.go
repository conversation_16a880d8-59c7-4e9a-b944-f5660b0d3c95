/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableMonsterBaseAttr struct {
	// ============= 变量定义 =============
	// Id
	ID int32
	// Model表ID
	ModelID int32
	// 拥有的技能id
	OwnSkillConfigId int32
	// 描述
	Describe int32
	// 头像
	headIcon string
	// 战斗中头像（小）
	headIconLittle string
	// 怪的名字
	Name int32
	// 职业（1、物理    2、冰系  3、电系  4、火系  5、能量   6、风系）
	OccupationType int32
	// 攻击距离类型（1近战   2远程）
	AtkDistanceType int32
	// 空中类型（ 1、地面 2、空中）
	MidairType int32
	// 怪物类型（1、小怪 2、精英、3 boss）
	MonsterType int32
	// 攻击
	Attack int32
	// 生命
	Hp int32
	// 防御
	Def int32
	// 命中（万分比）
	HitPre int32
	// 闪避（万分比）
	DodgePre int32
	// 移速
	MoveSpeed int32
	// 基础抗性
	BaseOccupationDef int32
	// 抗性类型数组
	OccupationDefArr []int32
	// 抗性的值
	OccupationDefValue int32
	// 穿透值
	Penetrate int32
	// 穿透率
	PenetratePre int32
	// 暴击抵抗
	CriticalResistPre int32
	// 警戒范围
	WarnRange float32
	// 受击效果（0无任何反应，1、反击，2、逃跑）
	HurtEffectType int32
	// 受击触发的概率（1-100）
	HurtEffectPro int32
	// 逃跑的速度加成百分比（1 + 当前值 / 10000） * 基础速度
	AddSpeedPre int32
	// 是否霸体（0非霸体，1霸体）
	IsSuperArmor int32
	// 是否是公会boss
	IsGuildBoss int32
	// 怪的拥有经验
	HaveExp int32
	// 战斗币
	HaveBattleMoney int32
	// 怪的能量值
	HaveBattleEnergy int32
}




// TableMonsterBaseAttrData 表格
type TableMonsterBaseAttrData struct {
	file    string
	dataMap map[int32]*TableMonsterBaseAttr
	Data    []*TableMonsterBaseAttr
	md5     string
}

// load 加载
func (tb *TableMonsterBaseAttrData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableMonsterBaseAttr{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableMonsterBaseAttr, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableMonsterBaseAttr)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableMonsterBaseAttr, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableMonsterBaseAttrData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableMonsterBaseAttr{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableMonsterBaseAttr, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableMonsterBaseAttr)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableMonsterBaseAttrData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableMonsterBaseAttrData) GetById(id int32) *TableMonsterBaseAttr {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableMonsterBaseAttrData) GetCloneById(id int32) *TableMonsterBaseAttr {
	v := tb.dataMap[id]
	out := &TableMonsterBaseAttr{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableMonsterBaseAttrData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableMonsterBaseAttrData) Foreach(call func(*TableMonsterBaseAttr) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableMonsterBaseAttrData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableMonsterBaseAttrData) Clone() ITable {
	ntb := &TableMonsterBaseAttrData{
		file:    tb.file,
		dataMap: make(map[int32]*TableMonsterBaseAttr),
		Data:    make([]*TableMonsterBaseAttr, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableMonsterBaseAttr{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
