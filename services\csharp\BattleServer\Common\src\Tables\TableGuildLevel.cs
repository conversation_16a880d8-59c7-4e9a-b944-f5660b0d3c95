#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuildLevel
	{

		public static readonly string TName="GuildLevel.json";

		#region 属性定义
		/// <summary> 
		/// 等级 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 升下一级经验 
		/// </summary> 
		public int Exp {get; set;}
		/// <summary> 
		/// 副会长最大个数 
		/// </summary> 
		public int ViceMaxCount {get; set;}
		/// <summary> 
		/// 成员最大个数 
		/// </summary> 
		public int MemberMaxCount {get; set;}
		/// <summary> 
		/// 免费捐献配置 
		/// </summary> 
		public int[] DonateType0 {get; set;}
		/// <summary> 
		/// 金币捐献配置 
		/// </summary> 
		public int[] DonateType1 {get; set;}
		/// <summary> 
		/// 钻石捐献配置 
		/// </summary> 
		public int[] DonateType2 {get; set;}
		/// <summary> 
		/// 砍价人数上限 
		/// </summary> 
		public int BargainingMaxCount {get; set;}
		#endregion

		public static TableGuildLevel GetData(int ID)
		{
			return TableManager.GuildLevelData.Get(ID);
		}

		public static List<TableGuildLevel> GetAllData()
		{
			return TableManager.GuildLevelData.GetAll();
		}

	}
	public sealed partial class TableGuildLevelData
	{
		private Dictionary<int, TableGuildLevel> dict = new Dictionary<int, TableGuildLevel>();
		private List<TableGuildLevel> dataList = new List<TableGuildLevel>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuildLevel.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuildLevel>>(jsonContent);
			foreach (TableGuildLevel config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuildLevel Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuildLevel item))
				return item;
			return null;
		}

		public List<TableGuildLevel> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
