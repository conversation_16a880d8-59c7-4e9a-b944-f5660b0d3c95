package log_lumberjack

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"github.com/smallnest/chanx"
	"liteframe/pkg/log_lumberjack/lumberjack"
	"os"
	"path/filepath"
	"sync"
	"time"
)

const (
	_bufferFlushInterval     = time.Millisecond * 100
	_defaultRotateSize       = 100 * 1024 * 1024 // 100M
	_defaultRenameTimeFormat = "2006-01-02-15"
	_defaultBuffSize         = 102400
	_defaultInRecBuffSize    = 102400
)

var bufPool = &sync.Pool{New: func() interface{} { return new(bytes.Buffer) }}

func getBuf() *bytes.Buffer {
	return bufPool.Get().(*bytes.Buffer)
}

func putBuf(buf *bytes.Buffer) {
	if buf.Cap() > 64<<10 {
		buf = nil
	} else {
		buf.Reset()
		bufPool.Put(buf)
	}
}

type Writer struct {
	filepath string
	//file     *os.File
	lumberjack *lumberjack.Logger
	bw         *bufio.Writer

	buffSize      int
	createTime    time.Time
	lastRotateDay int
	writtenSize   int

	recInCh   chan *bytes.Buffer
	recBuffCh *chanx.UnboundedChan[*bytes.Buffer]

	rotateCh chan struct{}
	closeCh  chan struct{}
	done     chan struct{}
}

type LogConf struct {
	// Filename is the file to write logs to.  Backup log files will be retained
	// in the same directory.  It uses <processname>-lumberjack.log in
	// os.TempDir() if empty.
	Filename string `json:"filename" yaml:"filename"`

	// MaxSize is the maximum size in megabytes of the log file before it gets
	// rotated. It defaults to 100 megabytes.
	MaxSize int `json:"maxsize" yaml:"maxsize"`

	// MaxAge is the maximum number of days to retain old log files based on the
	// timestamp encoded in their filename.  Note that a day is defined as 24
	// hours and may not exactly correspond to calendar days due to daylight
	// savings, leap seconds, etc. The default is not to remove old log files
	// based on age.
	MaxAge int `json:"maxage" yaml:"maxage"`

	// MaxBackups is the maximum number of old log files to retain.  The default
	// is to retain all old log files (though MaxAge may still cause them to get
	// deleted.)
	MaxBackups int `json:"maxbackups" yaml:"maxbackups"`

	// LocalTime determines if the time used for formatting the timestamps in
	// backup files is the computer's local time.  The default is to use UTC
	// time.
	LocalTime bool `json:"localtime" yaml:"localtime"`

	// Compress determines if the rotated log files should be compressed
	// using gzip. The default is not to perform compression.
	Compress bool `json:"compress" yaml:"compress"`
}

func New(config LogConf) (*Writer, error) {

	w := &Writer{
		filepath: config.Filename,
		rotateCh: make(chan struct{}),
		closeCh:  make(chan struct{}),
		done:     make(chan struct{}),
	}
	if w.buffSize <= 0 {
		w.buffSize = _defaultBuffSize
	}

	if _, err := os.Stat(filepath.Dir(w.filepath)); err != nil {
		if os.IsNotExist(err) {
			if err = os.MkdirAll(filepath.Dir(w.filepath), 0777); err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	w.lumberjack = &lumberjack.Logger{
		Filename:   config.Filename,
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   config.Compress,
		LocalTime:  true,
	}

	w.recInCh = make(chan *bytes.Buffer, _defaultInRecBuffSize)

	w.recBuffCh = chanx.NewUnboundedChan[*bytes.Buffer](context.Background(), w.buffSize)

	go w.process()

	return w, nil
}

func (w *Writer) Write(p []byte) (n int, err error) {
	// p may be reused outside, so copy it
	buf := getBuf()
	n, err = buf.Write(p)
	w.recBuffCh.In <- buf
	return n, err
}

func (w *Writer) Close() error {
	close(w.closeCh)

	<-w.done

	close(w.recBuffCh.In)

	for rec := range w.recBuffCh.Out {
		if err := w.write(rec.Bytes()); err != nil {
			_, _ = fmt.Fprintln(os.Stderr, "recBuffCh write error="+err.Error())
		}
		putBuf(rec)
	}

	for more := true; more; {
		select {
		case rec := <-w.recInCh:
			if err := w.write(rec.Bytes()); err != nil {
				_, _ = fmt.Fprintln(os.Stderr, "recInCh write error="+err.Error())
			}
			putBuf(rec)
		default:
			more = false
		}
	}

	if w.lumberjack != nil {
		if err := w.closeFile(); err != nil {
			return err
		}
	}

	return nil
}

func (w *Writer) Rotate() {
	w.rotateCh <- struct{}{}
}

func (w *Writer) process() {
	defer func() {
		w.done <- struct{}{}
	}()

	flushTicker := time.NewTicker(_bufferFlushInterval)
	defer flushTicker.Stop()

	secondTicker := time.NewTicker(time.Second)
	defer secondTicker.Stop()

	for {
		select {
		case rec := <-w.recInCh:
			w.recBuffCh.In <- rec
		case rec := <-w.recBuffCh.Out:
			if err := w.write(rec.Bytes()); err != nil {
				_, _ = fmt.Fprintln(os.Stderr, "write error="+err.Error())
			}
			putBuf(rec)
		case <-w.closeCh:
			return
		case <-flushTicker.C:
			w.flush()
		}
	}
}

func (w *Writer) write(p []byte) error {
	if w.lumberjack == nil {
		_, _ = fmt.Fprintln(os.Stderr, "rotate error=")
	}
	if w.bw == nil {
		//w.bw = bufio.NewWriter(w.lumberjack)
		//w.writtenSize = 0
		w.rotating()
	}
	n, err := w.bw.Write(p)
	w.writtenSize += n
	return err
}

func (w *Writer) closeFile() error {
	if err := w.bw.Flush(); err != nil {
		return err
	}
	if err := w.lumberjack.Close(); err != nil {
		return err
	}
	w.writtenSize = 0
	return nil
}

func (w *Writer) rotating() error {
	w.bw = bufio.NewWriter(w.lumberjack)
	w.writtenSize = 0
	w.lumberjack.Rotate()
	return nil
}

func (w *Writer) flush() {
	if w.bw == nil {
		return
	}
	if err := w.bw.Flush(); err != nil {
		_, _ = fmt.Fprintf(os.Stderr, "flush file=%s error=%s\n", w.filepath, err.Error())
	}
}
