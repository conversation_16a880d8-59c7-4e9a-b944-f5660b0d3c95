#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuildTech
	{

		public static readonly string TName="GuildTech.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 等级上限 
		/// </summary> 
		public int MaxLevel {get; set;}
		/// <summary> 
		/// 品质 
		/// </summary> 
		public int Quality {get; set;}
		/// <summary> 
		/// 名字 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 模型的名字 
		/// </summary> 
		public int ModelId {get; set;}
		/// <summary> 
		/// 前置科技ID数组 
		/// </summary> 
		public int[] PreTechIds {get; set;}
		/// <summary> 
		/// 后置科技ID数组 
		/// </summary> 
		public int[] AfterTechIds {get; set;}
		/// <summary> 
		/// 升级消耗公会币数值 
		/// </summary> 
		public int[] LevelUpConfig {get; set;}
		/// <summary> 
		/// 拥有属性id 
		/// </summary> 
		public int OwnProIdId {get; set;}
		/// <summary> 
		/// 拥有属性数值 
		/// </summary> 
		public int[] OwnProId {get; set;}
		/// <summary> 
		/// 拥有属性id1 
		/// </summary> 
		public int OwnProIdId1 {get; set;}
		/// <summary> 
		/// 拥有属性数值1 
		/// </summary> 
		public int[] OwnProId1 {get; set;}
		/// <summary> 
		/// 道具icon 
		/// </summary> 
		public string Icon {get; set;}
		#endregion

		public static TableGuildTech GetData(int ID)
		{
			return TableManager.GuildTechData.Get(ID);
		}

		public static List<TableGuildTech> GetAllData()
		{
			return TableManager.GuildTechData.GetAll();
		}

	}
	public sealed partial class TableGuildTechData
	{
		private Dictionary<int, TableGuildTech> dict = new Dictionary<int, TableGuildTech>();
		private List<TableGuildTech> dataList = new List<TableGuildTech>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuildTech.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuildTech>>(jsonContent);
			foreach (TableGuildTech config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuildTech Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuildTech item))
				return item;
			return null;
		}

		public List<TableGuildTech> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
