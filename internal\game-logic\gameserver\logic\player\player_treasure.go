package player

import (
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/pkg/log"
	"math/rand"
)

// TreasureGachaItem 卡包道具信息
type TreasureGachaItem struct {
	ItemId  int32
	Count   int32
	BasePro int32
}

// Treasure 宝物模块
type Treasure struct {
	player *Player
	db     *dbstruct.Treasure

	// 懒加载索引：卡包ID -> 道具列表
	bagItemsIndex map[int32][]*TreasureGachaItem
}

func NewTreasure(p *Player) *Treasure {
	return &Treasure{
		player:        p,
		bagItemsIndex: make(map[int32][]*TreasureGachaItem),
	}
}

// InitDB 初始化模块数据
func (t *Treasure) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Treasure == nil {
		db.Game.Treasure = &dbstruct.Treasure{
			TreasureList:         make([]*public.PBTreasureInfo, 0),
			TotalAcquiredCount:   make(map[int32]int32),
			GachaPityCounts:      make(map[int32]int32),
			DailyAdGachaCounts:   0,
			LastAdGachaResetTime: 0,
		}
	}
	t.db = db.Game.Treasure

	log.Info("Treasure InitDB", log.Kv("player_id", t.player.Uid()))
}

// OnCrossDay 跨天处理
func (t *Treasure) OnCrossDay(natural bool, nowUnix int64) {
	// 重置每日广告抽取次数
	t.db.DailyAdGachaCounts = 0
	t.db.LastAdGachaResetTime = nowUnix

	log.Info("Treasure daily ad gacha counts reset",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("resetTime", nowUnix))
}

// findTreasure 查找玩家已有的宝物
func (t *Treasure) findTreasure(treasureId int32) *public.PBTreasureInfo {
	for _, treasure := range t.db.TreasureList {
		if treasure.TreasureId == treasureId {
			return treasure
		}
	}
	return nil
}

// activateTreasure 激活宝物（首次获得时）
func (t *Treasure) activateTreasure(treasureId int32) *public.PBTreasureInfo {
	// 检查宝物配置是否存在
	treasureConfig := table.GetTable().TableTreasure.GetById(treasureId)
	if treasureConfig == nil {
		log.Error("Treasure config not found", log.Kv("treasureId", treasureId))
		return nil
	}

	// 创建新宝物
	newTreasure := &public.PBTreasureInfo{
		TreasureId: treasureId,
		Level:      1, // 初始等级为1
		Star:       1, // 初始星级为1
		Count:      0, // 初始数量为0（激活时不增加数量）
	}

	// 添加到宝物列表
	t.db.TreasureList = append(t.db.TreasureList, newTreasure)

	log.Info("Treasure activated", log.Kv("player_id", t.player.Uid()), log.Kv("treasureId", treasureId))
	return newTreasure
}

// addTreasureCount 增加宝物数量
func (t *Treasure) addTreasureCount(treasureId int32, count int32) {
	treasure := t.findTreasure(treasureId)
	if treasure == nil {
		// 首次获得，激活宝物
		treasure = t.activateTreasure(treasureId)
		if treasure == nil {
			return
		}
	}

	// 增加数量
	treasure.Count += count

	// 更新累计获得数量
	if t.db.TotalAcquiredCount == nil {
		t.db.TotalAcquiredCount = make(map[int32]int32)
	}
	t.db.TotalAcquiredCount[treasureId] += count

	log.Info("Treasure count added",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("treasureId", treasureId),
		log.Kv("addCount", count),
		log.Kv("totalCount", treasure.Count))
}

// getTreasureCount 获取宝物当前数量
func (t *Treasure) getTreasureCount(treasureId int32) int32 {
	treasure := t.findTreasure(treasureId)
	if treasure == nil {
		return 0
	}
	return treasure.Count
}

// getTotalAcquiredCount 获取宝物累计获得数量
func (t *Treasure) getTotalAcquiredCount(treasureId int32) int32 {
	if t.db.TotalAcquiredCount == nil {
		return 0
	}
	return t.db.TotalAcquiredCount[treasureId]
}

// getRemainingAdGachaTimes 获取剩余广告抽取次数
func (t *Treasure) getRemainingAdGachaTimes() int32 {
	maxAdTimes := table.GetTreasureGachaAdTimes()
	usedTimes := t.db.DailyAdGachaCounts
	remaining := maxAdTimes - usedTimes
	if remaining < 0 {
		return 0
	}
	return remaining
}

// buildBagItemsIndex 建立卡包道具索引（懒加载）
func (t *Treasure) buildBagItemsIndex(bagId int32) {
	if t.bagItemsIndex == nil {
		t.bagItemsIndex = make(map[int32][]*TreasureGachaItem)
	}

	// 检查索引是否已存在
	if _, exists := t.bagItemsIndex[bagId]; exists {
		return
	}

	// 建立索引
	items := make([]*TreasureGachaItem, 0)
	table.GetTable().TableTreasureGachaPro.Foreach(func(config *table_data.TableTreasureGachaPro) bool {
		if config.Bag == bagId {
			items = append(items, &TreasureGachaItem{
				ItemId:  config.Item,
				Count:   config.Count,
				BasePro: config.Pro,
			})
		}
		return false
	})

	// 存储索引
	t.bagItemsIndex[bagId] = items

	log.Info("Built bag items index",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("bagId", bagId),
		log.Kv("itemCount", len(items)))
}

// getBagItems 获取卡包道具列表（使用索引）
func (t *Treasure) getBagItems(bagId int32) []*TreasureGachaItem {
	// 懒加载：如果索引不存在则建立
	if _, exists := t.bagItemsIndex[bagId]; !exists {
		t.buildBagItemsIndex(bagId)
	}

	return t.bagItemsIndex[bagId]
}

// selectBagByWeight 根据权重选择卡包
func (t *Treasure) selectBagByWeight(bags [][]int32) int32 {
	if len(bags) == 0 {
		return 0
	}

	// 计算总权重
	totalWeight := int32(0)
	for _, bag := range bags {
		if len(bag) >= 2 {
			totalWeight += bag[1] // bag[1]是权重
		}
	}

	if totalWeight <= 0 {
		return 0
	}

	// 随机选择
	randomValue := rand.Int31n(totalWeight)
	currentWeight := int32(0)

	for _, bag := range bags {
		if len(bag) >= 2 {
			currentWeight += bag[1]
			if randomValue < currentWeight {
				return bag[0] // bag[0]是卡包ID
			}
		}
	}

	// 兜底返回第一个卡包
	if len(bags[0]) >= 1 {
		return bags[0][0]
	}
	return 0
}

// calculateProbabilityModifier 计算概率修正系数
func (t *Treasure) calculateProbabilityModifier(itemId int32) int32 {
	// 检查是否是宝物
	treasureConfig := table.GetTable().TableTreasure.GetById(itemId)
	if treasureConfig == nil {
		// 不是宝物，不进行概率修正
		return 10000 // 默认系数
	}

	// 获取已有数量
	acquiredCount := t.getTotalAcquiredCount(itemId)

	// 获取概率修正配置
	modifyConfig := table.GetTreasureGachaProModify()
	if modifyConfig == nil || len(modifyConfig) == 0 {
		return 10000 // 默认系数
	}

	// 根据数量获取对应的修正系数
	if int(acquiredCount) < len(modifyConfig) {
		return modifyConfig[acquiredCount]
	}

	// 超出配置范围，使用最后一个系数
	return modifyConfig[len(modifyConfig)-1]
}

// selectItemFromBag 从卡包中选择道具（使用索引优化）
func (t *Treasure) selectItemFromBag(bagId int32) (int32, int32) {
	// 收集该卡包的所有道具和概率
	type ItemProb struct {
		ItemId  int32
		Count   int32
		BasePro int32
		RealPro int32
	}

	// 使用索引获取卡包道具列表（替代全表遍历）
	bagItems := t.getBagItems(bagId)

	items := make([]*ItemProb, 0, len(bagItems))
	for _, bagItem := range bagItems {
		modifier := t.calculateProbabilityModifier(bagItem.ItemId)
		realPro := bagItem.BasePro * modifier / 10000

		items = append(items, &ItemProb{
			ItemId:  bagItem.ItemId,
			Count:   bagItem.Count,
			BasePro: bagItem.BasePro,
			RealPro: realPro,
		})
	}

	if len(items) == 0 {
		log.Error("No items found in bag", log.Kv("bagId", bagId))
		return 0, 0
	}

	// 计算总概率
	totalPro := int32(0)
	for _, item := range items {
		totalPro += item.RealPro
	}

	if totalPro <= 0 {
		log.Error("Total probability is zero", log.Kv("bagId", bagId))
		return 0, 0
	}

	// 随机选择
	randomValue := rand.Int31n(totalPro)
	currentPro := int32(0)

	for _, item := range items {
		currentPro += item.RealPro
		if randomValue < currentPro {
			return item.ItemId, item.Count
		}
	}

	// 兜底返回第一个道具
	return items[0].ItemId, items[0].Count
}

// HandleTreasureList 处理宝物列表请求
func (t *Treasure) HandleTreasureList() *cs.LCTreasureListResp {
	rsp := &cs.LCTreasureListResp{
		ErrorCode:    int32(error_code.ERROR_OK),
		TreasureList: make([]*public.PBTreasureInfo, 0),
	}

	// 返回玩家所有已激活的宝物
	if t.db.TreasureList != nil {
		for _, treasure := range t.db.TreasureList {
			// 复制宝物信息
			treasureInfo := &public.PBTreasureInfo{
				TreasureId: treasure.TreasureId,
				Level:      treasure.Level,
				Star:       treasure.Star,
				Count:      treasure.Count,
			}
			rsp.TreasureList = append(rsp.TreasureList, treasureInfo)
		}
	}

	log.Info("HandleTreasureList",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("treasure_count", len(rsp.TreasureList)))

	return rsp
}

// HandleTreasureLevelUp 处理宝物升级请求
func (t *Treasure) HandleTreasureLevelUp(treasureId int32) *cs.LCTreasureLevelUpResp {
	rsp := &cs.LCTreasureLevelUpResp{
		ErrorCode:  int32(error_code.ERROR_OK),
		TreasureId: treasureId,
		NewLevel:   0,
	}

	// 1. 检查宝物是否存在
	treasure := t.findTreasure(treasureId)
	if treasure == nil {
		log.Warn("Treasure not found for level up", log.Kv("treasureId", treasureId))
		rsp.ErrorCode = int32(error_code.ERROR_TREASURE_NOT_FOUND)
		return rsp
	}

	// 2. 检查宝物配置
	treasureConfig := table.GetTable().TableTreasure.GetById(treasureId)
	if treasureConfig == nil {
		log.Error("Treasure config not found", log.Kv("treasureId", treasureId))
		rsp.ErrorCode = int32(error_code.ERROR_PARAMS)
		return rsp
	}

	// 3. 检查是否已达到最大等级
	if treasure.Level >= treasureConfig.MaxLv {
		log.Warn("Treasure already at max level",
			log.Kv("treasureId", treasureId),
			log.Kv("currentLevel", treasure.Level),
			log.Kv("maxLevel", treasureConfig.MaxLv))
		rsp.ErrorCode = int32(error_code.ERROR_TREASURE_MAX_LEVEL)
		return rsp
	}

	// 4. 查找升级配置
	var levelUpConfig *table_data.TableTreasureLv
	table.GetTable().TableTreasureLv.Foreach(func(config *table_data.TableTreasureLv) bool {
		if config.ID == treasureId && config.Lv == treasure.Level+1 {
			levelUpConfig = config
			return true // 找到后停止遍历
		}
		return false
	})

	if levelUpConfig == nil {
		log.Error("Level up config not found",
			log.Kv("treasureId", treasureId),
			log.Kv("nextLevel", treasure.Level+1))
		rsp.ErrorCode = int32(error_code.ERROR_PARAMS)
		return rsp
	}

	// 5. 检查消耗材料是否足够
	if levelUpConfig.Cost != nil {
		for _, cost := range levelUpConfig.Cost {
			if len(cost) >= 2 {
				itemId := cost[0]
				needCount := cost[1]

				// 检查道具数量
				if !t.player.Item().IsCanDelItem(itemId, needCount) {
					log.Warn("Not enough materials for level up",
						log.Kv("treasureId", treasureId),
						log.Kv("itemId", itemId),
						log.Kv("needCount", needCount),
						log.Kv("hasCount", t.player.Item().GetItemNumById(itemId)))
					rsp.ErrorCode = int32(error_code.ERROR_TREASURE_LEVELUP_MATERIAL_NOT_ENOUGH)
					return rsp
				}
			}
		}
	}

	// 6. 消耗材料
	if levelUpConfig.Cost != nil {
		for _, cost := range levelUpConfig.Cost {
			if len(cost) >= 2 {
				itemId := cost[0]
				needCount := cost[1]

				if !t.player.Item().DelItem(itemId, needCount, game_def.DEL_ITEM_TREASURE_LEVELUP) { // 宝物升级消耗
					log.Error("Failed to consume materials",
						log.Kv("treasureId", treasureId),
						log.Kv("itemId", itemId),
						log.Kv("needCount", needCount))
					rsp.ErrorCode = int32(error_code.ERROR_OPT_FAILE)
					return rsp
				}
			}
		}
	}

	// 7. 升级成功
	treasure.Level++
	rsp.ErrorCode = int32(error_code.ERROR_OK)
	rsp.NewLevel = treasure.Level

	log.Info("Treasure level up success",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("treasureId", treasureId),
		log.Kv("newLevel", treasure.Level))

	return rsp
}

// HandleTreasureStarUp 处理宝物升星请求
func (t *Treasure) HandleTreasureStarUp(treasureId int32) *cs.LCTreasureStarUpResp {
	rsp := &cs.LCTreasureStarUpResp{
		ErrorCode:  int32(error_code.ERROR_OK),
		TreasureId: treasureId,
		NewStar:    0,
		NewCount:   0,
	}

	// 1. 检查宝物是否存在
	treasure := t.findTreasure(treasureId)
	if treasure == nil {
		log.Warn("Treasure not found for star up", log.Kv("treasureId", treasureId))
		rsp.ErrorCode = int32(error_code.ERROR_TREASURE_NOT_FOUND)
		return rsp
	}

	// 2. 检查宝物配置
	treasureConfig := table.GetTable().TableTreasure.GetById(treasureId)
	if treasureConfig == nil {
		log.Error("Treasure config not found", log.Kv("treasureId", treasureId))
		rsp.ErrorCode = int32(error_code.ERROR_PARAMS)
		return rsp
	}

	// 3. 检查是否已达到最大星级
	if treasure.Star >= treasureConfig.MaxStar {
		log.Warn("Treasure already at max star",
			log.Kv("treasureId", treasureId),
			log.Kv("currentStar", treasure.Star),
			log.Kv("maxStar", treasureConfig.MaxStar))
		rsp.ErrorCode = int32(error_code.ERROR_TREASURE_MAX_STAR)
		return rsp
	}

	// 4. 查找升星配置
	var starUpConfig *table_data.TableTreasureStar
	table.GetTable().TableTreasureStar.Foreach(func(config *table_data.TableTreasureStar) bool {
		if config.ID == treasureId && config.Star == treasure.Star+1 {
			starUpConfig = config
			return true // 找到后停止遍历
		}
		return false
	})

	if starUpConfig == nil {
		log.Error("Star up config not found",
			log.Kv("treasureId", treasureId),
			log.Kv("nextStar", treasure.Star+1))
		rsp.ErrorCode = int32(error_code.ERROR_PARAMS)
		return rsp
	}

	// 5. 检查同名宝物数量是否足够
	if treasure.Count < starUpConfig.Cost1 {
		log.Warn("Not enough same treasure for star up",
			log.Kv("treasureId", treasureId),
			log.Kv("needCount", starUpConfig.Cost1),
			log.Kv("hasCount", treasure.Count))
		rsp.ErrorCode = int32(error_code.ERROR_TREASURE_STARUP_SAME_TREASURE_NOT_ENOUGH)
		return rsp
	}

	// 6. 检查消耗材料是否足够
	if starUpConfig.Cost2 != nil {
		for _, cost := range starUpConfig.Cost2 {
			if len(cost) >= 2 {
				itemId := cost[0]
				needCount := cost[1]

				// 检查道具数量
				if !t.player.Item().IsCanDelItem(itemId, needCount) {
					log.Warn("Not enough materials for star up",
						log.Kv("treasureId", treasureId),
						log.Kv("itemId", itemId),
						log.Kv("needCount", needCount),
						log.Kv("hasCount", t.player.Item().GetItemNumById(itemId)))
					rsp.ErrorCode = int32(error_code.ERROR_TREASURE_STARUP_MATERIAL_NOT_ENOUGH)
					return rsp
				}
			}
		}
	}

	// 7. 消耗同名宝物
	treasure.Count -= starUpConfig.Cost1

	// 8. 消耗材料
	if starUpConfig.Cost2 != nil {
		for _, cost := range starUpConfig.Cost2 {
			if len(cost) >= 2 {
				itemId := cost[0]
				needCount := cost[1]

				if !t.player.Item().DelItem(itemId, needCount, game_def.DEL_ITEM_TREASURE_STARUP) { // 宝物升星消耗
					log.Error("Failed to consume materials",
						log.Kv("treasureId", treasureId),
						log.Kv("itemId", itemId),
						log.Kv("needCount", needCount))
					rsp.ErrorCode = int32(error_code.ERROR_OPT_FAILE)
					return rsp
				}
			}
		}
	}

	// 9. 升星成功
	treasure.Star++
	rsp.ErrorCode = int32(error_code.ERROR_OK)
	rsp.NewStar = treasure.Star
	rsp.NewCount = treasure.Count

	log.Info("Treasure star up success",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("treasureId", treasureId),
		log.Kv("newStar", treasure.Star),
		log.Kv("remainCount", treasure.Count))

	return rsp
}

// HandleTreasureGacha 处理宝物抽取请求
func (t *Treasure) HandleTreasureGacha(req *cs.CLTreasureGachaReq) *cs.LCTreasureGachaResp {
	rsp := &cs.LCTreasureGachaResp{
		ErrorCode: int32(error_code.ERROR_OK),
		Items:     make([]*public.PBDropItemDataInfo, 0),
	}

	// 1. 检查抽卡配置
	gachaConfig := table.GetTable().TableTreasureGacha.GetById(req.GachaId)
	if gachaConfig == nil {
		log.Error("Gacha config not found", log.Kv("gachaId", req.GachaId))
		rsp.ErrorCode = int32(error_code.ERROR_TREASURE_GACHA_CONFIG_NOT_FOUND)
		return rsp
	}

	// 2. 检查消耗（根据CostType处理不同的消耗类型）
	switch req.CostType {
	case public.TreasureGachaCostType_COST_TYPE_AD: // 广告抽取
		// 检查广告次数限制
		maxAdTimes := table.GetTreasureGachaAdTimes()
		currentUsedTimes := t.db.DailyAdGachaCounts

		if currentUsedTimes+req.DrawCount > maxAdTimes {
			log.Warn("Ad gacha times limit reached",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("currentUsed", currentUsedTimes),
				log.Kv("requestDraw", req.DrawCount),
				log.Kv("maxTimes", maxAdTimes))
			rsp.ErrorCode = int32(error_code.ERROR_TREASURE_GACHA_AD_TIMES_NOT_ENOUGH)
			return rsp
		}
	case public.TreasureGachaCostType_COST_TYPE_ITEM: // 道具抽取
		if gachaConfig.Cost != nil && len(gachaConfig.Cost) >= 2 {
			itemId := gachaConfig.Cost[0]
			needCount := gachaConfig.Cost[1] * req.DrawCount // 根据抽取次数计算消耗

			// 检查道具数量
			if !t.player.Item().IsCanDelItem(itemId, needCount) {
				log.Warn("Not enough items for gacha",
					log.Kv("gachaId", req.GachaId),
					log.Kv("itemId", itemId),
					log.Kv("needCount", needCount),
					log.Kv("hasCount", t.player.Item().GetItemNumById(itemId)))
				rsp.ErrorCode = int32(error_code.ERROR_TREASURE_GACHA_COST_NOT_ENOUGH)
				return rsp
			}
		}
	default:
		log.Warn("Invalid gacha cost type", log.Kv("costType", req.CostType))
		rsp.ErrorCode = int32(error_code.ERROR_TREASURE_GACHA_COST_TYPE_INVALID)
		return rsp
	}

	// 3. 初始化保底计数
	if t.db.GachaPityCounts == nil {
		t.db.GachaPityCounts = make(map[int32]int32)
	}

	// 4. 执行多次抽取
	for i := int32(0); i < req.DrawCount; i++ {
		// 判断是否触发保底
		currentPityCount := t.db.GachaPityCounts[req.GachaId]
		isSpecialBag := (currentPityCount + 1) >= gachaConfig.Count

		// 选择卡包
		var selectedBagId int32
		if isSpecialBag && gachaConfig.BagSpecial != nil {
			// 使用保底卡包
			selectedBagId = t.selectBagByWeight(gachaConfig.BagSpecial)
			// 重置保底计数
			t.db.GachaPityCounts[req.GachaId] = 0
			log.Info("Pity triggered",
				log.Kv("player_id", t.player.Uid()),
				log.Kv("gachaId", req.GachaId),
				log.Kv("selectedBagId", selectedBagId))
		} else {
			// 使用普通卡包
			selectedBagId = t.selectBagByWeight(gachaConfig.Bag)
			// 增加保底计数
			t.db.GachaPityCounts[req.GachaId] = currentPityCount + 1
		}

		if selectedBagId == 0 {
			log.Error("Failed to select bag", log.Kv("gachaId", req.GachaId))
			rsp.ErrorCode = int32(error_code.ERROR_OPT_FAILE)
			return rsp
		}

		// 从卡包中抽取道具
		itemId, itemCount := t.selectItemFromBag(selectedBagId)
		if itemId == 0 || itemCount == 0 {
			log.Error("Failed to select item from bag",
				log.Kv("gachaId", req.GachaId),
				log.Kv("bagId", selectedBagId))
			rsp.ErrorCode = int32(error_code.ERROR_OPT_FAILE)
			return rsp
		}

		// 发放奖励
		treasureConfig := table.GetTable().TableTreasure.GetById(itemId)
		if treasureConfig != nil {
			// 是宝物，使用宝物系统添加
			t.addTreasureCount(itemId, itemCount)
		} else {
			// 是普通道具，使用道具模块添加
			t.player.Item().AddItem(itemId, itemCount, game_def.ADD_ITEM_TREASURE_GACHA_ITEM) // 宝物抽取获得道具
		}

		// 添加到返回结果
		dropItem := &public.PBDropItemDataInfo{
			ItemId:     itemId,
			ItemCount:  itemCount,
			ItemType:   public.AwardItemType_AwardItemType_Item, // 默认为道具类型
			HighEffect: treasureConfig != nil,                   // 宝物显示高级特效
		}
		rsp.Items = append(rsp.Items, dropItem)
	}

	// 5. 消耗资源
	if req.CostType == public.TreasureGachaCostType_COST_TYPE_ITEM && gachaConfig.Cost != nil && len(gachaConfig.Cost) >= 2 {
		costItemId := gachaConfig.Cost[0]
		costCount := gachaConfig.Cost[1] * req.DrawCount

		if !t.player.Item().DelItem(costItemId, costCount, game_def.DEL_ITEM_TREASURE_GACHA_COST) { // 宝物抽取消耗
			log.Error("Failed to consume gacha cost",
				log.Kv("gachaId", req.GachaId),
				log.Kv("itemId", costItemId),
				log.Kv("needCount", costCount))
			rsp.ErrorCode = int32(error_code.ERROR_OPT_FAILE)
			return rsp
		}
	} else if req.CostType == public.TreasureGachaCostType_COST_TYPE_AD {
		// 广告抽取，增加使用次数
		t.db.DailyAdGachaCounts += req.DrawCount
	}

	// 6. 返回结果
	rsp.ErrorCode = int32(error_code.ERROR_OK)

	log.Info("Treasure gacha success",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("gachaId", req.GachaId),
		log.Kv("costType", req.CostType),
		log.Kv("drawCount", req.DrawCount),
		log.Kv("itemCount", len(rsp.Items)),
		log.Kv("pityCount", t.db.GachaPityCounts[req.GachaId]))

	return rsp
}
