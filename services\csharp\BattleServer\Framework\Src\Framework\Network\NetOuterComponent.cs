﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-17
//*********************************************************

using System;
using System.IO;
using System.Net;

namespace Aurora.Framework
{
    public class NetOuterComponent : BaseComponent,IAwake,IDestroy
    {
        public Action<Session> m_SessionCreatedCallback;

        public static NetOuterComponent Create(IPEndPoint listen = null)
        {
            //TODO:以后可根据具体网络配置，如TCP、KCP、WS等
            NetOuterComponent comp = ReferencePool.Acquire<NetOuterComponent>();
            if(listen != null)
            {
				NetworkListenerComponent listener = comp.AddComponent<NetworkListenerComponent>(listen);
				listener.m_AcceptCallback += comp.OnAccept;
			}

            return comp;
        }
    }

    [ComponentSystem(typeof(NetOuterComponent))]
    public static class NetOuterComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(NetOuterComponent self)
        {
            Log.Debug("NetTCPComponentSystem OnAwake");

        }

        [MethodDestroy]
        public static void Destroy(NetOuterComponent self)
        {
        }

		public static void OnAccept(this NetOuterComponent self, TChannel channel)
		{
            if (self == null) return;
			Session session = self.AddChild<Session>();
			EntitySystem.Awake(session, channel);

            if(self.m_SessionCreatedCallback != null)
            {
                self.m_SessionCreatedCallback.Invoke(session);
            }
        }

		public static Session Create(this NetOuterComponent self, IPEndPoint ipAdress, Action channelConnectCallback = null, Action<Session> channelErrorCallback = null)
		{
            if (self == null) return null;
			TChannel channel = new TChannel(ipAdress);
			Session session = self.AddChild<Session>();
			EntitySystem.Awake(session, channel);

            if (self.m_SessionCreatedCallback != null)
            {
                self.m_SessionCreatedCallback.Invoke(session);
            }
            if (channelConnectCallback != null)
            {
                session.m_ChannelConnectCallback = channelConnectCallback;
            }
            if(channelErrorCallback != null)
            {
                session.m_ReadErrorCallback += channelErrorCallback;
            }

            return session;
		}
    }
}
