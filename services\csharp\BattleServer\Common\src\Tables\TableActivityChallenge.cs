#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityChallenge
	{

		public static readonly string TName="ActivityChallenge.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 对应功能 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 等级 
		/// </summary> 
		public int Lv {get; set;}
		/// <summary> 
		/// 下一级经验 
		/// </summary> 
		public int Exp {get; set;}
		/// <summary> 
		/// 奖励 
		/// </summary> 
		public int[][] Drop {get; set;}
		#endregion

		public static TableActivityChallenge GetData(int ID)
		{
			return TableManager.ActivityChallengeData.Get(ID);
		}

		public static List<TableActivityChallenge> GetAllData()
		{
			return TableManager.ActivityChallengeData.GetAll();
		}

	}
	public sealed partial class TableActivityChallengeData
	{
		private Dictionary<int, TableActivityChallenge> dict = new Dictionary<int, TableActivityChallenge>();
		private List<TableActivityChallenge> dataList = new List<TableActivityChallenge>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityChallenge.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityChallenge>>(jsonContent);
			foreach (TableActivityChallenge config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityChallenge Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityChallenge item))
				return item;
			return null;
		}

		public List<TableActivityChallenge> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
