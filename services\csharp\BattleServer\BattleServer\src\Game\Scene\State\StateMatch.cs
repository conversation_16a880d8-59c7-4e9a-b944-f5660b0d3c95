﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Aurora.Framework;

namespace BattleServer.Game
{
    public class StateMatch : State
    {
        public StateMatch(StateComponent stateComponent) : base(stateComponent)
        {
        }

        public override void OnEnter()
        {
            Log.Debug("[StateMatch] OnEnter");

            var scene = _stateComponent.GetScene();
            scene.Match();

            _stateComponent.ChangeState(StateType.Buffer);
        }

        public override void OnUpdate(float deltaTime)
        {
        }
    }
}
