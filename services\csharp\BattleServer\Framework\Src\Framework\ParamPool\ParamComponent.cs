﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-2-20
//*********************************************************

using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace Aurora.Framework
{

    public class ParamComponent : BaseComponent
    {
        public ParamPool Pool { get; set; }
        public PoolDefine _PoolDef;
        public bool m_HasDirty = true;
        public BitArray m_DirtyBits;
        public int m_ParentPoolType = -1;
        public bool CheckDirty(int index)
        {
            if (_PoolDef == null)
                return false;
            if (index > _PoolDef.MaxIndex)
                return false;
            return m_DirtyBits[index];
        }

        public void ClearDirty()
        {
            if (_PoolDef == null)
                return;
            m_DirtyBits.SetAll(false);
            m_HasDirty = false;
            SubEntitiesComponent subEntitiesComp = GetComponent<SubEntitiesComponent>();
            if(subEntitiesComp != null)
            {
                subEntitiesComp.ClearDirty();
            }
        }
        public void MarkDirty(int index)
        {
            if (m_DirtyBits == null)
                return;
            m_DirtyBits[index] = true;
            m_HasDirty = true;
            PoolEntity poolEntity = Parent as PoolEntity;
            if(poolEntity != null)
            {
                poolEntity.MarkDirty();
            }
        }
        
        public void MarkAllDirty()
        {
            if (_PoolDef == null)
                return;
            if (m_DirtyBits == null)
                return;
            m_DirtyBits.SetAll(true);
            m_HasDirty = true;
            PoolEntity poolEntity = Parent as PoolEntity;
            if (poolEntity != null)
            {
                poolEntity.MarkDirty();
            }
        }
        public SubEntityKey GenSubEntityId(int parentIndex, EParamIncrementType parentDataType, long parentElemIndex)
        {
            SubEntityKey res = new SubEntityKey(parentIndex, parentDataType, parentElemIndex);
            return res;
        }
        public int GetParentIndex(SubEntityKey subEntityKey)
        {
            return subEntityKey.m_ParentIndex;
        }
        public int GetParentDataType(SubEntityKey subEntityKey)
        {
            return subEntityKey.m_ParentDataType;
        }
        public long GetParentElemIndex(SubEntityKey subEntityKey)
        {
            return subEntityKey.m_ParentElemIndex;
        }
        public virtual void OnParamChanged(int index)
        {

        }
        public virtual void InitDB() { }
        public virtual void AfterInitDB() { }
        public virtual void InitSubDB() { }
        public virtual void InitSubDBClient(List<SubEntityKey> subEntityKeys) { }
        public virtual void CleanUpDB() { }
        public virtual void SyncSubCompStruct(List<SubCompStruct> rSubCompStructList)
        {
            SubEntitiesComponent subEntitiesDic = this.GetComponent<SubEntitiesComponent>();
            if (null == subEntitiesDic)
                return;
            if (null != rSubCompStructList)
            {
                SubCompStruct subStruct = new SubCompStruct();
                subStruct.PoolType = this._PoolDef.PoolType;
                subStruct.SubCompSubKeys = subEntitiesDic.SubEntities.Keys.ToList();
                rSubCompStructList.Add(subStruct);
            }
        }
        public virtual void SyncInit2Self(List<PoolData> rPoolList) { }
        public virtual void SyncInit2Other(List<PoolData> rPoolList) { }
        public virtual void SyncParam(List<ParamData> rParamList, List<SubParamData> rSubParamList, uint paramFalg, uint syncFlag) { }
        public virtual void SyncSubParam(List<SubParamData> rSubParamList, uint paramFalg, uint syncFlag) { }
        public virtual void SyncInitClient(PoolData poolData, bool bSelf) { }
        public virtual void SyncParamClient(ParamData rParam) { }
        public virtual void SyncSubParamClient(SubParamData rSubParam) { }
        public virtual void SyncParamListClient(int poolIndex,PoolListSyncData rParam) { }
        public virtual void SyncParamDicClient(int poolIndex,PoolDicSyncData rParam) { }
        public virtual void SyncParamQueueClient(int poolIndex, PoolDicSyncData rParam) { }
        public virtual void SyncParam2PlayerInfoServer(BaseComponent baseComp) { }

        public virtual ParamPool CreateComponentPool()
        {
            return null;
        }

        public override void PreClear()
        {
            //Pool.ParamChangeHandler -= this.OnHandleParamChanged;
           // Pool = null;
            PoolEntity pE = Parent as PoolEntity;
            if (null != pE)
            {
                pE.DBInitHandler -= this.OnHandleDBInit;
                pE.AutoSyncSubCompStruct -= this.OnHandleSyncSubCompStruct;
                pE.AutoSyncInit2Self -= this.OnHandleSyncInit2Self;
                pE.AutoSyncInit2Other -= this.OnHandleSyncInit2Other;
                pE.AutoSyncParam -= this.OnHandleSyncParam;
                pE.AutoSyncSubParam -= this.OnHandleSyncSubParam;
                pE.AutoSyncParam2PlayerInfo -= this.OnHandleSyncParam2PlayerInfo;

            }
            CleanUpDB();
        }
    }

    public static class ParamComponentSystem
    {
        public static void RegisterParam(this ParamComponent self)
        {
            if (self == null) return;
            if (self.Parent == null)
                return;
            PoolEntity pE = self.Parent as PoolEntity;
            if (null != pE)
            {
                pE.DBInitHandler += self.OnHandleDBInit;
                pE.AutoSyncSubCompStruct += self.OnHandleSyncSubCompStruct;
                pE.AutoSyncInit2Self += self.OnHandleSyncInit2Self;
                pE.AutoSyncInit2Other += self.OnHandleSyncInit2Other;
                pE.AutoSyncParam += self.OnHandleSyncParam;
                pE.AutoSyncSubParam += self.OnHandleSyncSubParam;
                pE.AutoSyncParam2PlayerInfo += self.OnHandleSyncParam2PlayerInfo;
            }
            /*
            IPoolOwner poolOwner = self.Parent as IPoolOwner;
            if (poolOwner == null)
                return;

            self.Pool = self.CreateComponentPool();
            if (null == self.Pool)
            {
                Log.Error($"ParamComponentSystem RegisterParam CreateComponentPool failed! IPoolOwner PoolType[{poolOwner.PoolType}], DataID[{poolOwner.DataID}]");
                return;
            }
            poolOwner.AddChildPool(self.Pool);
            self.Pool.ParamChangeHandler += self.OnHandleParamChanged;
            */
            
            
        }

        public static void OnHandleParamChanged(this ParamComponent self, int index)
        {
            if (self == null) return;
            self.OnParamChanged(index);
        }

        public static void OnHandleDBInit(this ParamComponent self)
        {
            if (self == null) return;
            self.InitDB();
            self.AfterInitDB();
        }

        
        public static void OnHandleSyncInit2Self(this ParamComponent self, List<PoolData> rPoolList)
        {
            if (self == null || self._PoolDef == null) return;
            if (self._PoolDef.IsSyncClientSelf == false)
            {
                return;
            }
            self.SyncInit2Self(rPoolList); 
        }

        public static void OnHandleSyncInit2Other(this ParamComponent self, List<PoolData> rPoolList)
        {
            if (self == null || self._PoolDef == null) return;
            if(self._PoolDef.IsSyncClientOther == false)
            {
                return;
            }
            
            self.SyncInit2Other(rPoolList);
        }


        public static void OnHandleSyncSubCompStruct(this ParamComponent self,List<SubCompStruct> rSubCompStructList)
        {
            if (self == null) return;
            self.SyncSubCompStruct(rSubCompStructList);
        }
        
        public static void OnHandleSyncParam(this ParamComponent self, List<ParamData> rParamList, List<SubParamData> rSubParamList, uint paramFalg, uint syncFlag)
        {
            if (self == null) return;
            self.SyncParam(rParamList, rSubParamList,paramFalg, syncFlag);
        }

        public static void OnHandleSyncSubParam(this ParamComponent self, List<SubParamData> rSubParamList, uint paramFalg, uint syncFlag)
        {
            if (self == null) return;
            self.SyncSubParam(rSubParamList, paramFalg, syncFlag);
        }
        /*
        public static void OnHandleSyncParamClient(this ParamComponent self, ParamData rParam)
        {
            self.SyncParamClient(rParam);
        }*/
        public static void OnHandleSyncParam2PlayerInfo(this ParamComponent self, BaseComponent baseComp)
        {
            if (self == null) return;
            self.SyncParam2PlayerInfoServer(baseComp);
        }

    }
}