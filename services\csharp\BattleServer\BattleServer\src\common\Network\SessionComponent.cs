﻿
using Aurora.Framework;

public class SessionComponent : BaseComponent
{
    public Session m_Session;
}


public static class SessionComponentSystem
{
    //[MethodAwake]
    //public static void OnAwake(SessionComponent self)
    //{
    //    self.m_Session = self.AddChild<Session>();
    //    //self.m_Session.m_MessageAddress.CopyFrom(address);
    //}

    public static void OnAwake1(SessionComponent self, MessageAddress address)
    {
        self.m_Session = new Session();
        self.m_Session.m_MessageAddress.CopyFrom(address);
    }

    public static void OnDestroy(SessionComponent self)
    {
        self.m_Session = null;
    }
}
