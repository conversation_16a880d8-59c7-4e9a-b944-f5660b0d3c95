﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-15
//*********************************************************

using System;
using System.IO;
using System.Net;

namespace Aurora.Framework
{
    //public enum ServiceType
    //{
    //    Outer,
    //    Inner,
    //}

    public abstract class AService : IReference
    {
        public ThreadSyncContext ThreadSyncContext;

        public abstract void Update();
        public abstract void Remove(long id);
        public abstract void Release();
        public abstract bool IsReleased();
        public abstract void Send(long channelId, Packet pkt);
        public abstract void Create(long id, IPEndPoint ipEndPoint);
        protected void OnAccept(long channelId, IPEndPoint ipEndPoint)
        {
            this.AcceptCallback.Invoke(channelId, ipEndPoint);
        }

        public void OnRead(long channelId, Packet pkt)
        {
            this.ReadCallback.Invoke(channelId, pkt);
        }

        public void OnError(long channelId)
        {
            this.Remove(channelId);

            this.ErrorCallback?.Invoke(channelId);
        }
        public void RemoveChannel(long channelId)
        {
            this.Remove(channelId);
        }

        public Action<long, IPEndPoint> AcceptCallback;
        public Action<long> ErrorCallback;
        public Action<long, Packet> ReadCallback;

        public void Clear()
        {
            
        }
    }
}
