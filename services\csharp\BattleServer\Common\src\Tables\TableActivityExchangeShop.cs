#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityExchangeShop
	{

		public static readonly string TName="ActivityExchangeShop.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 兑换商店组ID 
		/// </summary> 
		public int GroupId {get; set;}
		/// <summary> 
		/// 商品 
		/// </summary> 
		public int[][] Drop {get; set;}
		/// <summary> 
		/// 兑换次数 
		/// </summary> 
		public int ExchangeNumber {get; set;}
		/// <summary> 
		/// 货币类型 
		/// </summary> 
		public int CurrencyType {get; set;}
		/// <summary> 
		/// 价格 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 显示顺序 
		/// </summary> 
		public int Order {get; set;}
		#endregion

		public static TableActivityExchangeShop GetData(int ID)
		{
			return TableManager.ActivityExchangeShopData.Get(ID);
		}

		public static List<TableActivityExchangeShop> GetAllData()
		{
			return TableManager.ActivityExchangeShopData.GetAll();
		}

	}
	public sealed partial class TableActivityExchangeShopData
	{
		private Dictionary<int, TableActivityExchangeShop> dict = new Dictionary<int, TableActivityExchangeShop>();
		private List<TableActivityExchangeShop> dataList = new List<TableActivityExchangeShop>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityExchangeShop.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityExchangeShop>>(jsonContent);
			foreach (TableActivityExchangeShop config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityExchangeShop Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityExchangeShop item))
				return item;
			return null;
		}

		public List<TableActivityExchangeShop> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
