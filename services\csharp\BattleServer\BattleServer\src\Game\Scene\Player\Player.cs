﻿using Aurora.Framework;
using BattleServer.Framework;
using BattleServer.Nats;
using BattleServer.Service;
using Game.Core;
using System;
using System.Collections.Generic;
using System.Net.NetworkInformation;

namespace BattleServer.Game
{
    public class BattlePlayer
    {
        public int Rank { get; set; } = 0; // 玩家排名，默认0
        private bool IsEnter;
        private int BufferID;
        private bool IsReady;

        public bool IsRobot { get; set; }

        public PBBattlePlayerInfo? Info { get; set; }
        public PBBattleTeamInfo TeamInfo { get; set; }

        private List<int> OptionsBuffer = new List<int>();

        private List<Hero> NewHeroList = new List<Hero>();
        private Dictionary<int, Hero> HeroList = new Dictionary<int, Hero>();
        
        // 每个阵营对应的列表
        private Dictionary<int, List<Hero>> CampHero = new Dictionary<int, List<Hero>>();
        private List<Hero> LastBattleHeroList = new List<Hero> { }; // 上一场战斗的英雄列表
        // 棋盘上的英雄列表，未开始前的
        private Dictionary<int, Hero> LowerBoardHero = new Dictionary<int, Hero>();

        private List<Hero> CacheHeroList { get; set; } = new List<Hero> { };

        public BattleChess? battle = null;

        private int mStartCellId = 0;
        private int mEndCellId = 0;

        // 准备阶段拥有的羁绊效果
        private List<int> mSynergySkills = new List<int>();

        public BattlePlayer(PBBattlePlayerInfo info, PBBattleTeamInfo teamInfo)
        {
            IsEnter = false;
            IsReady = false;
            BufferID = 0;
            Info = info;
            TeamInfo = teamInfo;
            Info.Hp = 3;
        }

        public bool IsEnterBattle()
        {
            return IsEnter;
        }

        public int GetBufferId()
        {
            return BufferID;
        }

        public bool IsSelectBuffer()
        {
            return BufferID > 0;
        }

        public bool IsReadyToBattle()
        {
            return IsReady;
        }

        public void RandomBuffers()
        {
            Log.Debug($"[player] {Info.Uid} RandomBuffers Hp {Info.Hp}");
            Random random = new Random();

            TablePlayMode playMode = TablePlayMode.GetData(1);
            for (int i = 0; i < playMode.BuffList.Length; i++)
            {
                OptionsBuffer.Add(playMode.BuffList[i]);
                if (i >= 2)
                    break;
            }
        }

        public List<int> GetOptionsBuffer()
        {
            return OptionsBuffer;
        }

        public void InitCell(int startId, int endId)
        {
            if (startId < 1 || startId > 60 || endId < 1 || endId > 60)
            {
                Log.Error($"player {Info.Uid} init cell from {startId} to {endId} error");
            }
            mStartCellId = startId;
            mEndCellId = endId;

            Log.Debug($"player {Info.Uid} init cell from {mStartCellId} to {mEndCellId}");

            FlipBoard();
        }

        private void FlipBoard()
        {
            // 棋盘翻转,如过玩家在原来是在下方棋盘，现在在上方棋盘，则需要将所有英雄位置翻转
            // 如果玩家在上方棋盘，现在在下方棋盘，则需要将所有英雄位置翻转
            foreach (var hero in HeroList.Values)
            {
                if (hero.Position <=30 && IsUpperBoard())
                {
                    // 如果英雄在下方棋盘，现在在上方棋盘，则翻转位置
                    hero.Position = FlipToUpper(hero.Position);
                }
                else if (hero.Position > 30 && IsLowerBoard())
                {
                    // 如果英雄在上方棋盘，现在在下方棋盘，则翻转位置
                    hero.Position = FlipToLower(hero.Position);
                }
                //Log.Debug($"[player] {Info.Uid} FlipBoard hero position {hero.Position} StartCellId {mStartCellId} EndCellId {mEndCellId}");
            }

            foreach(var hero in LastBattleHeroList)
            {
                if (hero.Position <= 30 && IsUpperBoard())
                {
                    // 如果英雄在下方棋盘，现在在上方棋盘，则翻转位置
                    hero.Position = FlipToUpper(hero.Position);
                }
                else if (hero.Position > 30 && IsLowerBoard())
                {
                    // 如果英雄在上方棋盘，现在在下方棋盘，则翻转位置
                    hero.Position = FlipToLower(hero.Position);
                }
                //Log.Debug($"[player] {Info.Uid} FlipBoard last battle hero position {hero.Position} StartCellId {mStartCellId} EndCellId {mEndCellId}");
            }
        }

        public void GeneratedHeros()
        {
            if (IsDead())
            {
                Log.Error($"[player] {Info.Uid} GeneratedHeros but player is dead");
                return;
            }
            Log.Debug($"[player] {Info.Uid} GeneratedHeros Hp {Info.Hp}");

            Scene scene = battle.GetScene();
            int round = scene.GetRound();

            RoundAddHero addHero = TablePlayMode.GetRoundAddHero(1, round);
            if (addHero == null)
            {
                Log.Error($"[player] {Info.Uid} GeneratedHeros but addHero is null round {round}");
                return;
            }

            TablePlayMode playMode = TablePlayMode.GetData(1);
            List<int> heroList = new List<int>();
            //TeamInfo.Heros
            //for (int i = 0; i < TeamInfo.Heros.Length; i++)
            for (int i=0; i<playMode.HeroList.Length; i++)
            {
                heroList.Add(playMode.HeroList.ElementAt(i));
            }
            List<int> randList = RandomGenerator.RandomListRange(heroList, addHero.Num);
            for (int i=0; i<randList.Count; i++)
            {
                Hero hero = HeroFactory.CreateHero(randList.ElementAt(i), addHero.Level, this);
                NewAddHero(hero);
            }
        }

        private int GetEmptyPosition(int laner)
        {
            int[] positions = BestPosition.GetBestPosition(laner);
            for (int j = 0; j < positions.Length; j++)
            {
                int position = positions[j];
                if (IsPositionEmpty(position))
                {
                    return position;
                }
            }

            return 0;
        }

        private void AddCampHero(Hero hero)
        {
            TableHero heroData = TableHero.GetData(hero.GetResID());

            List<Hero> list;
            CampHero.TryGetValue(heroData.Camp, out list);
            if (list == null)
            {
                list = new List<Hero>();
                list.Add(hero);
                CampHero.Add(heroData.Camp, list);
            }
            else
            {
                list.Add(hero);
            }
        }

        // 获得新英雄/h后期整理
        private void NewAddHero(Hero hero)
        {
            if (LowerBoardHero.Count >= 30)
            {
                CacheHeroList.Add(hero);
            }
            else
            {
                int position = GetEmptyPosition(hero.Res.HeroPosition);
                if (IsUpperBoard())
                {
                    hero.Position = FlipToUpper(position);
                }
                else
                {
                    hero.Position = position;
                }
                if (hero.Position < mStartCellId || hero.Position > mEndCellId)
                {
                    Log.Error($"[player] {Info.Uid} GeneratedHeros hero {hero.GetResID()} position {hero.Position} error start {mStartCellId} end {mEndCellId}");
                }
                Log.Debug($"[player] {Info.Uid} GeneratedHeros hero {hero.GetResID()} position {hero.Position}");
                NewHeroList.Add(hero);
                HeroList.Add(hero.Position, hero);
                SetBoardHero(position, hero);
                AddCampHero(hero);
            }
        }

        private int GetCampHeroCount(int camp)
        {
            List<Hero> list;
            CampHero.TryGetValue(camp, out list);
            if (list == null)
            {
                return 0;
            }

            return list.Count;
        }

        private List<Hero> GetCampHeros(int camp)
        {
            List<Hero> list;
            CampHero.TryGetValue(camp, out list);
            if (list == null)
            {
                return null;
            }

            return list;
        }

        ////
        //public void UpdateNewHeroToBoard()
        //{
        //    Log.Debug($"[player] {Info.Uid} UpdateNewHeroToBoard Hp {Info.Hp}");
        //    if (NewHeroList.Count <= 0)
        //    {
        //        Log.Error($"[player] {Info.Uid} NewHeroList is empty");
        //        return;
        //    }
        //    foreach (var hero in NewHeroList)
        //    {
        //        //AddHero(hero.Position, hero);
        //        if (HeroList.ContainsKey(hero.Position))
        //        {
        //            Log.Error($"[player] {Info.Uid} UpdateNewHeroToBoard cellId {hero.Position} already has a hero");
        //        }
        //        else
        //        {
        //            HeroList.Add(hero.Position, hero);
        //        }
        //    }
        //    NewHeroList.Clear();
        //}

        private Hero GetHero(int cellID)
        {
            Hero hero = null;
            HeroList.TryGetValue(cellID, out hero);
            if (hero == null)
            {
                return null;
            }

            return hero;
        }

        private void SetCellHero(int cellId, Hero hero)
        {
            Log.Debug($"[player] {Info.Uid} SetCellHero cellId {cellId} hero {hero.GetResID()}");
            if (cellId < 1 || cellId > 60)
            {
                Log.Error($"[player] {Info.Uid} SetCellHero cellId {cellId} is out of range from {1} to {60}");
                return;
            }
            
            Log.Debug($"[player] {Info.Uid} SetCellHero cellId {cellId} success");

            if (cellId >= 1 && cellId <= 30)
            {
                Log.Debug($"[player] {Info.Uid} SetCellHero cellId {cellId} in LowerBoardHero");
                SetBoardHero(cellId, hero);
            }
            else if (cellId >= 31 && cellId <= 60)
            {
                Log.Debug($"[player] {Info.Uid} SetCellHero cellId {cellId} in UpperBoardHero, flip to lower board");
                int lowerCellId = FlipToLower(cellId);
                SetBoardHero(lowerCellId, hero);
            }
        }

        private void DeleteHero(int cellId)
        {
            Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId}");
            if (cellId < 1 || cellId > 60)
            {
                Log.Error($"[player] {Info.Uid} DeleteHero cellId {cellId} is out of range from {1} to {60}");
                return;
            }

            if (HeroList.ContainsKey(cellId))
            {
                HeroList.Remove(cellId);
                Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId} success");

                if (cellId >= 1 && cellId <= 30)
                {
                    Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId} in LowerBoardHero");
                    DeleteBoardHero(cellId);
                }
                else if (cellId >= 31 && cellId <= 60)
                {
                    Log.Debug($"[player] {Info.Uid} DeleteHero cellId {cellId} in UpperBoardHero, flip to lower board");
                    int lowerCellId = FlipToLower(cellId);
                    DeleteBoardHero(lowerCellId);
                }
            }
            else
            {
                Log.Error($"[player] {Info.Uid} DeleteHero cellId {cellId} not found in LowerBoardHero");
            }
        }

        private void SetBoardHero(int cellID, Hero hero)
        {
            if (cellID < 1 || cellID > 30)
            {
                Log.Error($"[player] {Info.Uid} SetBoardHero cellID {cellID} is out of range from {1} to {30}");
                return;
            }
            if (hero == null)
            {
                Log.Error($"[player] {Info.Uid} SetBoardHero hero is null");
                return;
            }
            if (LowerBoardHero.ContainsKey(cellID))
            {
                Log.Error($"[player] {Info.Uid} SetBoardHero cellID {cellID} already has a hero");
            }
            else
            {
                LowerBoardHero.Add(cellID, hero);
                Log.Debug($"[player] {Info.Uid} SetBoardHero cellID {cellID} hero {hero.GetResID()} success");
            }
        }

        private void DeleteBoardHero(int cellID)
        {
            if (cellID < 1 || cellID > 30)
            {
                Log.Error($"[player] {Info.Uid} DeleteBoardHero cellID {cellID} is out of range from {1} to {30}");
                return;
            }
            if (LowerBoardHero.ContainsKey(cellID))
            {
                LowerBoardHero.Remove(cellID);
                Log.Debug($"[player] {Info.Uid} DeleteBoardHero cellID {cellID} success");
            }
            else
            {
                Log.Error($"[player] {Info.Uid} DeleteBoardHero cellID {cellID} not found in LowerBoardHero");
            }
        }

        private bool IsPositionEmpty(int position)
        {
            if (position < 1 || position > 30)
            {
                Log.Error($"[player] {Info.Uid} IsPositionEmpty position {position} is out of range from {0} to {30}");
                return false;
            }

            bool empty = LowerBoardHero.ContainsKey(position);
            return !empty;
        }

        public void ClearBattle()
        {
            Log.Debug($"[player] {Info.Uid} ClearBattle Hp {Info.Hp}");
            IsEnter = false;
            IsReady = false;
            BufferID = 0;
            OptionsBuffer.Clear();
            battle = null;
            mStartCellId = 0;
            mEndCellId = 0;
            NewHeroList.Clear();
            Rank = 0;
        }

        public bool IsDead()
        {
            return Info.Hp <= 0;
        }

        public int GetHp() { return Info.Hp; }


        // 进入战斗场景
        public void OnEnterBattle()
        {
            Log.Debug($"[player] {Info.Uid} OnEnterBattle Hp {Info.Hp}");
            IsEnter = true;
            IsReady = false;
            BufferID = 0;
        }

        public void OnSelectBuffer(int bufferId)
        {
            Log.Debug($"[player] {Info.Uid} OnSelectBuffer {bufferId} Hp {Info.Hp}");
            BufferID = bufferId;

            // 选完buff后，需要直接生成英雄，以便在消息中通知客户端
            GeneratedHeros();
        }

        public void OnMoveHero(List<PBMoveOperation> moveOps)
        {
            Log.Debug($"[player] {Info.Uid} OnMoveHero moveOps count {moveOps.Count} Hp {Info.Hp}");
            if (IsRobot) return;
            if (IsDead()) return;

            if (moveOps.Count <= 0)
            {
                Log.Error($"[player] {Info.Uid} OnMoveHero moveOps count is zero");
                return;
            }
            foreach (var moveOp in moveOps)
            {
                if (moveOp.FromGridId < mStartCellId || moveOp.FromGridId > mEndCellId || moveOp.ToGridId < mStartCellId || moveOp.ToGridId > mEndCellId)
                {
                    Log.Error($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId} error self id from {mStartCellId} to {mEndCellId}");
                    continue;
                }
                if (moveOp.ToGridId == moveOp.FromGridId)
                {
                    Log.Debug($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId} is same, skip");
                    continue;
                }

                if (GetHero(moveOp.FromGridId) == null)
                {
                    Log.Error($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId} error no hero in cell");
                    continue;
                }

                Log.Debug($"[player] {Info.Uid} OnMoveHero move from {moveOp.FromGridId} to {moveOp.ToGridId}");
                Hero? fromHero = GetHero(moveOp.FromGridId);
                Hero? toHero = GetHero(moveOp.ToGridId);
                //先删除原位置的英雄
                DeleteHero(moveOp.FromGridId);
                if (toHero != null)
                {
                    DeleteHero(moveOp.ToGridId);
                }

                //再添加到新位置
                fromHero.Position = moveOp.ToGridId;
                HeroList.Add(moveOp.ToGridId, fromHero);
                SetCellHero(moveOp.ToGridId, fromHero);

                if (toHero != null)
                {
                    toHero.Position = moveOp.FromGridId; // 如果目标位置有英雄，则交换位置
                    //再添加到新位置
                    HeroList.Add(moveOp.FromGridId, toHero);
                    SetCellHero(moveOp.FromGridId, toHero); 
                }
            }
        }
        public List<Hero> OnMergeHero(int FromGridId, int ToGridId)
        {
            Log.Debug($"[player] {Info.Uid} OnMergeHero from {FromGridId} to {ToGridId}");
            if (IsRobot) return null;
            if (IsDead()) return null;
            if (FromGridId == ToGridId)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero from {FromGridId} to {ToGridId} is same, skip");
                return null;
            }

            if (FromGridId < mStartCellId || FromGridId > mEndCellId || ToGridId < mStartCellId || ToGridId > mEndCellId)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero from {FromGridId} to {ToGridId} error self id from {mStartCellId} to {mEndCellId}");
                return null;
            }

            var srcHero = GetHero(FromGridId);
            var destHero = GetHero(ToGridId);
            if (srcHero == null)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero from {FromGridId} error no hero in cell");
                return null;
            }
            if (destHero == null)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero to {ToGridId} error no hero in cell");
                return null;
            }

            if (srcHero.StarLevel != destHero.StarLevel)
            {
                Log.Error($"[player] {Info.Uid} OnMergeHero src hero {srcHero.GetResID()} level {srcHero.StarLevel} != dest hero {destHero.GetResID()} level {destHero.StarLevel}");
                return null;
            }

            if (srcHero.GetResID() != destHero.GetResID())
            {
                if (!IsHasFreeMergeSkill())
                {
                    Log.Error($"[player] {Info.Uid} OnMergeHero src hero {srcHero.GetResID()} != dest hero {destHero.GetResID()} ");
                    return null;
                }     
            }

            NewHeroList.Clear();

            var newHero = GetHero(ToGridId);
            newHero.LevelUp();
            DeleteHero(FromGridId); // 删除源英雄

            UpdateCacheToCell(FromGridId);

            OnSynergySkillEffect(2);

            NewHeroList.Add(newHero);
            //List<Hero> copyList = new List<Hero>([.. NewHeroList]);
            //NewHeroList.Clear();

            return NewHeroList;
        }

        private void UpdateCacheToCell(int cellId)
        {
            if (CacheHeroList.Count <= 0)
                return;
            
            Hero hero = CacheHeroList[0];
            hero.Position = cellId; // 将缓存的英雄位置设置为FromGridId
            CacheHeroList.RemoveAt(0);
            HeroList.Add(cellId, hero);
            NewHeroList.Add(hero);
            SetCellHero(cellId, hero);
        }

        public void OnBattleReady()
        {
            Log.Debug($"[player] {Info.Uid} OnReady Hp {Info.Hp}");

            IsReady = true;
        }

        public void OnRoundBattleEnd(bool win)
        {
            if (IsDead())
            {
                Log.Debug($"[player] {Info.Uid} OnRoundBattleEnd but player is already dead");
                return;
            }

            Log.Debug($"[player] {Info.Uid} OnRoundBattleEnd win {win} hp {Info.Hp}");
            if (!win)
            {
                Info.Hp -= 1;
                Log.Debug($"[player] {Info.Uid} OnBattleFailed hp {Info.Hp}");
                if (Info.Hp <= 0)
                {
                    OnDeath();
                }
            }
        }

        private void OnDeath()
        {
            Log.Debug($"[player] {Info.Uid} OnDeath");
            Info.Hp = 0;
            IsEnter = false;
            IsReady = false;
            BufferID = 0;
            OptionsBuffer.Clear();
            battle.GetScene().OnPlayerDeath(Info.Uid);
            Rank = battle.GetScene().GetPlayers().Count + 1; // 死亡后排名为最后一名

            if (IsRobot)
                return;

            var req = new RoundBattleEndReq();
            req.Uid = Info.Uid;
            req.WinUid = 0;
            req.LoseUid = 0;
            req.IsEnd = true; // 设置整场战斗是否即将结束
            req.TimeoutTimestamp = 10; // 结算确认超时
            NatsClient.GameServiceClient.RoundBattleEnd(req, Info.ServerId).ConfigureAwait(false);

            var endReq = new BattleEndReq();
            endReq.Uid = Info.Uid;
            endReq.BattleId = 0;
            endReq.WinStreak = 0;
            endReq.Rank = 1;
            endReq.Heros.Add(ToPBBattleHeroInfoList());
            NatsClient.GameServiceClient.BattleEnd(endReq, Info.ServerId).ConfigureAwait(false);

            SceneManager.Instance.RemovePlayerScene(Info.Uid);
        }

        public void OnSynergySkill()
        {
            Log.Debug($"[player] {Info.Uid} OnSynergySkill");
            if (IsReady) return;

            List<CampSynergy> skills = TableManager.SynergyData.GetSynergy();
            if (skills.Count == 0) return;

            foreach (var skill in skills)
            {
                int heroCount = GetCampHeroCount(skill.Camp);
                if (heroCount < skill.HeroCount)
                    continue;

                int skillId = 0;
                List<Hero> campHeros = GetCampHeros(skill.Camp);
                int minLevel = GetHeroMinStarLevel(campHeros);
                for (int i = 0; i < skill.LevelSkills.Count; i++)
                {
                    if (minLevel >= skill.LevelSkills.ElementAt(i).Level)
                    {
                        skillId = skill.LevelSkills.ElementAt(i).SkillId;
                    }
                }

                // 如果是自由合成，并且是备战状态的要先计算概率
                TableServerSkill skillServer = TableServerSkill.GetData(skillId);
                if (skillServer.TiggerTiming == 2 && skillServer.LogicType == 2)
                {
                    if (TableServerSkill.RandomWeight(skillId))
                    {
                        mSynergySkills.Add(skillId);
                        return;
                    }
                }
                else
                {
                    mSynergySkills.Add(skillId);
                }  
            }

            Log.Debug($"[Player] {Info.Uid} OnSynergySkill skills {mSynergySkills.ToString()}");

            //计算完毕后，执行具体效果
            OnSynergySkillEffect(1);
        }


        private void OnSynergySkillEffect(int type)
        {
            switch (type)
            {
                case 1: // 进入备战时
                    foreach(var skill in mSynergySkills)
                    {
                        TableServerSkill skillServer = TableServerSkill.GetData(skill);
                        if (skillServer == null) continue;
                        if (skillServer.TiggerTiming != type) continue;

                        OnSkillLogic(skillServer);
                    }
                    break;
                case 2: // 合成时
                    foreach (var skill in mSynergySkills)
                    {
                        TableServerSkill skillServer = TableServerSkill.GetData(skill);
                        if (skillServer == null) continue;
                        if (skillServer.TiggerTiming != type) continue;

                        OnSkillLogic(skillServer);
                    }
                    break;
                default:
                    Log.Error($"[player] {Info.Uid} OnSynergySkillEffect no type {type}");
                    break;
            }
        }

        private void OnSkillLogic(TableServerSkill skillServer)
        {
            switch (skillServer.LogicType)
            {
                case 1: // 获得新英雄
                    NewHero newHeroInfo = TableServerSkill.GetNewHeroSkill(skillServer.ID);
                    List<int> randList = new List<int>();
                    for (int i = 0;i< newHeroInfo.CampList.Count;i++)
                    {
                        if (CampHero.ContainsKey(newHeroInfo.CampList[i]))
                        {
                            randList.Add(newHeroInfo.CampList[i]);
                        }
                    }
                    if (randList.Count <= 0)
                    {
                        Log.Error($"[Player] {Info.Uid} OnSkillLogic new hero skill {skillServer.ID} but camp list empty");
                        return;
                    }
                    int camp = RandomGenerator.RandomArray(randList);
                    if (!TableServerSkill.RandomWeight(skillServer.ID))
                    {
                        Log.Info($"[Player] {Info.Uid} OnSkillLogic have skill {skillServer.ID} but randNum less");
                        return;
                    }

                    List<Hero> campList = GetCampHeros(camp);
                    Hero hero = RandomGenerator.RandomArray(campList);
                    Hero newHero = HeroFactory.CreateHero(hero.ResID, newHeroInfo.StarLevel, this);
                    NewAddHero(newHero);

                    break;
                default:
                    break;
            }
        }

        private bool IsHasFreeMergeSkill()
        {
            foreach (var skill in mSynergySkills)
            {
                TableServerSkill skillServer = TableServerSkill.GetData(skill);
                if (skillServer == null) continue;
                if (skillServer.TiggerTiming != 1) continue;

                if (skillServer.LogicType == 2)
                    return true;
            }

            return false;
        }

        public TableServerSkill GetLevelUpSkill()
        {
            foreach (var skill in mSynergySkills)
            {
                TableServerSkill skillServer = TableServerSkill.GetData(skill);
                if (skillServer == null) continue;

                if (skillServer.LogicType == 3)
                    return skillServer;
            }

            return null;
        }

        private int GetHeroMinStarLevel(List<Hero> heros)
        {
            int minStarLevel = 10;
            foreach (var hero in heros)
            {
                if (hero.StarLevel < minStarLevel)
                {
                    minStarLevel = hero.StarLevel;
                }
            }

            return minStarLevel;
        }

        public List<int> GetSynergySkills()
        {
            return mSynergySkills;
        }

        public PBBattleCampInfo ToPBBattleCampInfo()
        {
            var campInfo = new PBBattleCampInfo();
            campInfo.Player = new PBBattlePlayerInfo
            {
                Uid = Info.Uid,
                Name = $"Player{Info.Uid}",
                Level = 1,
                Throphy = 1000,
                ServerId = Info.ServerId,
                Hp = Info.Hp
            };

            campInfo.BoardInfo.Add(GetHeroBoard());
            return campInfo;
        }

        public List<PBCheckerBoard> GetHeroBoard()
        {
            var list = new List<PBCheckerBoard>();
            foreach (var hero in HeroList.Values)
            {
                var board = new PBCheckerBoard();
                board.GridID = hero.Position;
                board.Hero = hero.ToPB();
                //Log.Debug($"[player] {Info.Uid} GetHeroBoard hero {hero.GetResID()} position {hero.Position} cell from {mStartCellId} to {mEndCellId}");
                list.Add(board);
            }

            return list;
        }

        public List<PBCheckerBoard> GetNewHeroBoard()
        {
            var list = new List<PBCheckerBoard>();
            foreach (var hero in NewHeroList)
            {
                var board = new PBCheckerBoard();
                board.GridID = hero.Position;
                board.Hero = hero.ToPB();
                //Log.Debug($"[player] {Info.Uid} GetNewHeroBoard hero {hero.GetResID()} position {hero.Position} cell from {mStartCellId} to {mEndCellId}");
                list.Add(board);
            }
            return list;
        }

        public void SaveToLastBattleHeroList()
        {
            Log.Debug($"[player] {Info.Uid} SaveToLastBattleHeroList Hp {Info.Hp}");
            LastBattleHeroList.Clear();
            foreach (var hero in HeroList.Values)
            {
                var lastHero = HeroFactory.CreateHero(hero.GetResID(), hero.StarLevel, this);
                lastHero.Position = hero.Position;
                LastBattleHeroList.Add(lastHero);
            }
        }

        public List<PBCheckerBoard> GetLastBattleHeroBoard()
        {
            var list = new List<PBCheckerBoard>();
            foreach (var hero in LastBattleHeroList)
            {
                var board = new PBCheckerBoard();
                board.GridID = hero.Position;
                board.Hero = hero.ToPB();
                //Log.Debug($"[player] {Info.Uid} GetLastBattleHeroBoard hero {hero.GetResID()} position {hero.Position} cell from {mStartCellId} to {mEndCellId}");
                list.Add(board);
            }
            return list;
        }

        public List<PBBattleHeroInfo> ToPBBattleHeroInfoList()
        {
            var list = new List<PBBattleHeroInfo>();
            foreach (var hero in HeroList.Values)
            {
                list.Add(hero.ToPB());
            }
            return list;
        }

        // 如果自己在下方，并且转化的ID也是下方
        private int FlipToUpper(int lowerBoardId)
        {
            if (lowerBoardId <= 30 && IsUpperBoard())
            {
                // ID转坐标：行号 = ceil(ID/6)，列号 = (ID-1)%6 + 1
                int row = (int)Math.Ceiling((double)lowerBoardId / 6);
                int col = ((lowerBoardId - 1) % 6) + 1;

                // 翻转：新行号 = 6 - 原行号
                int newRow = 6 - row;

                // 转换为上方ID：(新行号-1) × 6 + 列号 + 30
                int upperBoardId = (newRow - 1) * 6 + col + 30;
                Log.Info($"[player] {Info.Uid} FlipToUpper lowerBoardId {lowerBoardId} to upperBoardId {upperBoardId}");
                return upperBoardId;
            }
            else
            {
                Log.Error($"[player] {Info.Uid} FlipToUpper lowerBoardId {lowerBoardId} is in lower board");
                return lowerBoardId; // 如果在上方棋盘，则不需要翻转
            } 
        }


        // 如果自己在上方，并且转化的ID也是上方
        private int FlipToLower(int upperBoardId)
        {
            if (upperBoardId >= 31 && IsLowerBoard())
            {
                // ID转坐标：行号 = ceil(ID/6)，列号 = (ID-1)%6 + 1
                int row = (int)Math.Ceiling((double)(upperBoardId - 30) / 6);
                int col = ((upperBoardId - 30 - 1) % 6) + 1;
                // 翻转：新行号 = 6 - 原行号
                int newRow = 6 - row;
                // 转换为下方ID：(新行号-1) × 6 + 列号 + 0
                int lowerBoardId = (newRow - 1) * 6 + col;
                Log.Info($"[player] {Info.Uid} FlipToLower upperBoardId {upperBoardId} to lowerBoardId {lowerBoardId}");
                return lowerBoardId;
            }
            else
            {
                Log.Error($"[player] {Info.Uid} FlipToLower upperBoardId {upperBoardId} is in upper board");
                return upperBoardId;
            } 
        }

        public bool IsLowerBoard()
        {
            // 检查cellId是否在下方棋盘范围内
            return mStartCellId >= 1 && mEndCellId <= 30;
        }

        private bool IsUpperBoard()
        {
            // 检查cellId是否在上方棋盘范围内
            return mStartCellId >= 31 && mEndCellId <= 60;
        }
    }
}
