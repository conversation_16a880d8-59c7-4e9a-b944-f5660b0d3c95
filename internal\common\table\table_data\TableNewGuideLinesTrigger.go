/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableNewGuideLinesTrigger struct {
	// ============= 变量定义 =============
	// 引导步骤ID
	ID int32
	// 启动触发类型：1、领取完主线任务2、完成主线任务3、上一步新手引导完成4、完成副本关卡5、起名之后16、解锁建筑小区块
	StartType int32
	// 参数信息
	StartParameter int32
	// 结束条件类型（服务器数据）：1、领取主线任务2、完成任务3新手结束触发下一个新手4、金币经验主线成功结束5起名结束7、抽卡结束8获得龙蛋9龙蛋孵化结束  10、挖宝  11 开蛋 12、合成 13、第一次上阵 14、解锁区域 15、领取龙岛金币16、解锁建筑小区块
	EndType int32
	// 参数信息（结束条件参数）
	EndParameter int32
	// 引导组首ID，只填写该引导组首Id
	GuildStartId int32
	// 是否在主场景触发1.不在2.在3.花路主线关卡触发引导时填写
	IsInMainScene int32
	// 触发机制是否需要回退
	IsGoBack int32
	// 触发引导时需要保留的界面
	StayUI []string
}




// TableNewGuideLinesTriggerData 表格
type TableNewGuideLinesTriggerData struct {
	file    string
	dataMap map[int32]*TableNewGuideLinesTrigger
	Data    []*TableNewGuideLinesTrigger
	md5     string
}

// load 加载
func (tb *TableNewGuideLinesTriggerData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableNewGuideLinesTrigger{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableNewGuideLinesTrigger, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableNewGuideLinesTrigger)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableNewGuideLinesTrigger, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableNewGuideLinesTriggerData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableNewGuideLinesTrigger{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableNewGuideLinesTrigger, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableNewGuideLinesTrigger)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableNewGuideLinesTriggerData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableNewGuideLinesTriggerData) GetById(id int32) *TableNewGuideLinesTrigger {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableNewGuideLinesTriggerData) GetCloneById(id int32) *TableNewGuideLinesTrigger {
	v := tb.dataMap[id]
	out := &TableNewGuideLinesTrigger{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableNewGuideLinesTriggerData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableNewGuideLinesTriggerData) Foreach(call func(*TableNewGuideLinesTrigger) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableNewGuideLinesTriggerData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableNewGuideLinesTriggerData) Clone() ITable {
	ntb := &TableNewGuideLinesTriggerData{
		file:    tb.file,
		dataMap: make(map[int32]*TableNewGuideLinesTrigger),
		Data:    make([]*TableNewGuideLinesTrigger, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableNewGuideLinesTrigger{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
