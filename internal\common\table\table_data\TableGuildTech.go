/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableGuildTech struct {
	// ============= 变量定义 =============
	// Id
	ID int32
	// 等级上限
	MaxLevel int32
	// 品质
	Quality int32
	// 名字
	Name int32
	// 模型的名字
	ModelId int32
	// 前置科技ID数组
	PreTechIds []int32
	// 后置科技ID数组
	AfterTechIds []int32
	// 升级消耗公会币数值
	LevelUpConfig []int32
	// 拥有属性id
	OwnProIdId int32
	// 拥有属性数值
	OwnProId []int32
	// 拥有属性id1
	OwnProIdId1 int32
	// 拥有属性数值1
	OwnProId1 []int32
	// 道具icon
	Icon string
}




// TableGuildTechData 表格
type TableGuildTechData struct {
	file    string
	dataMap map[int32]*TableGuildTech
	Data    []*TableGuildTech
	md5     string
}

// load 加载
func (tb *TableGuildTechData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableGuildTech{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableGuildTech, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuildTech)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableGuildTech, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableGuildTechData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableGuildTech{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableGuildTech, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuildTech)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableGuildTechData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableGuildTechData) GetById(id int32) *TableGuildTech {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableGuildTechData) GetCloneById(id int32) *TableGuildTech {
	v := tb.dataMap[id]
	out := &TableGuildTech{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableGuildTechData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableGuildTechData) Foreach(call func(*TableGuildTech) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableGuildTechData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableGuildTechData) Clone() ITable {
	ntb := &TableGuildTechData{
		file:    tb.file,
		dataMap: make(map[int32]*TableGuildTech),
		Data:    make([]*TableGuildTech, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableGuildTech{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
