﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Game.Core
{

    public class RoundAddHero
    {
        public int StartRound;
        public int EndRound;
        public int Num;
        public int Level;
    }
    // 不允许出现变量，只能写静态函数
    public sealed partial class TablePlayMode
    {
        public static RoundAddHero GetRoundAddHero(int id, int round)
        {
            return TableManager.PlayModeData.GetRoundAddHero(id, round);
        }
    }


    public sealed partial class TablePlayModeData
    {
        private Dictionary<int, List<RoundAddHero>> roundAddHeroes = new Dictionary<int, List<RoundAddHero>>();

        public void InitHelper()
        {
            foreach(var data in dataList)
            {
                List<RoundAddHero> list = new List<RoundAddHero>();
                for (int i = 0; i < data.AddHero.Length; i++)
                {
                    RoundAddHero roundAddHero = new RoundAddHero();
                    roundAddHero.StartRound = data.AddHero[i][0];
                    roundAddHero.EndRound = data.AddHero[i][1];
                    roundAddHero.Num = data.AddHero[i][2];
                    roundAddHero.Level = data.AddHero[i][3];

                    list.Add(roundAddHero);
                }

                roundAddHeroes[data.ID] = list;
            }
        }

        public RoundAddHero GetRoundAddHero(int id, int round)
        {
            List<RoundAddHero> list;
            roundAddHeroes.TryGetValue(id, out list);
            if (list == null)
            {
                return null;
            }

            for (int i = 0; i < list.Count; i++)
            {
                if (round >= list.ElementAt(i).StartRound && round <= list.ElementAt(i).EndRound)
                {
                    return list[i];
                }
            }

            return null;
        }
    }
}
