/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableModel struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// AssName
	AssName string
	// ABName
	AB_Name string
	// 类型
	Type int32
	// 编辑器下的路径信息
	EditorPath string
	// 模型的半径
	Radius float32
	// 受击动作
	HurtAnims []int32
	// 受击融合动作
	HurtFusionAnim int32
	// 模型的大中小类型（0小、1中、2大） 目前针对冰冻眩晕特效使用
	SizeType int32
	// 子节点Id
	Node int32
	// 特效是否具有物理属性
	Physics int32
	// 缩放比例（缩放模型自身）
	Scale float32
	// 旋转
	Rotation []float32
	// RT相机下的坐标偏移
	Location []float32
	// RT相机的缩放（第一位默认的缩放，第二位无限塔的缩放）
	RTScaleValues []float32
	// 出场的渐变
	StartAlphaTime float32
	// 出场的特效
	StartEffectId int32
	// 出场的音效id
	StartSoundId int32
	// 死亡特效
	DeadEffectId int32
	// 死亡音效1
	DeadSound int32
	// 受击特效
	HurtEffectId int32
	// 受击音效
	HurtSound int32
	// 伤害信息偏移
	HeadInfoDamageHeight float32
	// 血条偏移
	HeadInfoBloodHeight float32
	// 称号的偏移量
	HeadTitleHeightY float32
	// HeadPoint位置
	HeadPointHeightXY []float32
	// BoodyPoint位置偏移
	BodyPointHeightXY []float32
	// 模型特效缩放
	UIModelEffectScale float32
	// 头顶偏移
	HeadOffset float32
	// 脚底偏移
	FootOffset float32
	// 死亡是是否慢镜头
	DeathSlowShot int32
	// 是否显示武器
	IsShowWeapon int32
	// 聊天泡泡偏移量x
	ChatBubbleOffsetX float32
	// 聊天泡泡偏移量y
	ChatBubbleOffsetY float32
	// 聊天泡泡内容
	ChatBubbleIds []int32
	// 聊天泡泡底板类型
	ChatBubbleTypes []int32
}




// TableModelData 表格
type TableModelData struct {
	file    string
	dataMap map[int32]*TableModel
	Data    []*TableModel
	md5     string
}

// load 加载
func (tb *TableModelData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableModel{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableModel, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableModel)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableModel, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableModelData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableModel{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableModel, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableModel)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableModelData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableModelData) GetById(id int32) *TableModel {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableModelData) GetCloneById(id int32) *TableModel {
	v := tb.dataMap[id]
	out := &TableModel{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableModelData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableModelData) Foreach(call func(*TableModel) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableModelData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableModelData) Clone() ITable {
	ntb := &TableModelData{
		file:    tb.file,
		dataMap: make(map[int32]*TableModel),
		Data:    make([]*TableModel, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableModel{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
