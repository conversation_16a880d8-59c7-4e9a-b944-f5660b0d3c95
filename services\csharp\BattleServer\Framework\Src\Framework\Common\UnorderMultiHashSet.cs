/*
 * @description: 二维映射
 * @Auther: Kimsee
 * @Date: 2022-11-23 14:28
 * 
*/ 

using System.Collections.Generic;

namespace Aurora.Framework
{
    public class UnOrderMultiHashSet<T, TK>: Dictionary<T, HashSet<TK>>
    {
        // 重用HashSet
        public new HashSet<TK> this[T t]
        {
            get
            {
                HashSet<TK> set;
                if (!this.TryGetValue(t, out set))
                {
                    set = new HashSet<TK>();
                }
                return set;
            }
        }
        
        public Dictionary<T, HashSet<TK>> GetDictionary()
        {
            return this;
        }
        
        public void Add(T t, TK k)
        {
            HashSet<TK> set;
            this.TryGetValue(t, out set);
            if (set == null)
            {
                set = new HashSet<TK>();
                base[t] = set;
            }
            set.Add(k);
        }

        public bool Remove(T t, TK k)
        {
            HashSet<TK> set;
            this.TryGetValue(t, out set);
            if (set == null)
            {
                return false;
            }
            if (!set.Remove(k))
            {
                return false;
            }
            if (set.Count == 0)
            {
                this.Remove(t);
            }
            return true;
        }

        public bool Contains(T t, TK k)
        {
            HashSet<TK> set;
            this.TryGetValue(t, out set);
            if (set == null)
            {
                return false;
            }
            return set.Contains(k);
        }

        public new int Count
        {
            get
            {
                int count = 0;
                foreach (KeyValuePair<T,HashSet<TK>> kv in this)
                {
                    count += kv.Value.Count;
                }
                return count;
            }
        }
    }
}