#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityDoorGift
	{

		public static readonly string TName="ActivityDoorGift.json";

		#region 属性定义
		/// <summary> 
		/// ID（当前第几期） 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 本期目标描述(语言包） 
		/// </summary> 
		public int DescID {get; set;}
		/// <summary> 
		/// 敲门砖礼包按顺序配置敲门砖商品ID 
		/// </summary> 
		public int[] DoorGifts {get; set;}
		#endregion

		public static TableActivityDoorGift GetData(int ID)
		{
			return TableManager.ActivityDoorGiftData.Get(ID);
		}

		public static List<TableActivityDoorGift> GetAllData()
		{
			return TableManager.ActivityDoorGiftData.GetAll();
		}

	}
	public sealed partial class TableActivityDoorGiftData
	{
		private Dictionary<int, TableActivityDoorGift> dict = new Dictionary<int, TableActivityDoorGift>();
		private List<TableActivityDoorGift> dataList = new List<TableActivityDoorGift>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityDoorGift.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityDoorGift>>(jsonContent);
			foreach (TableActivityDoorGift config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityDoorGift Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityDoorGift item))
				return item;
			return null;
		}

		public List<TableActivityDoorGift> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
