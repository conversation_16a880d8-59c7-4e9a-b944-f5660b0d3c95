﻿using System;
using System.Collections.Generic;

public class SpineCurves
{
    private class KeyFrame
    {
        public float Time;
        public float Value;
        public float InTangent;
        public float OutTangent;
        public float InWeight;
        public float OutWeight;
    }

    private float _beginTime;
    private float _endTime;
    private List<KeyFrame> _keyFrameList = new List<KeyFrame>();
    private List<HermiteCurve> _curveList = new List<HermiteCurve>();

    public SpineCurves()
    {
        Clear();
    }

    public void Clear()
    {
        _beginTime = 0;
        _endTime = 0;
        _keyFrameList.Clear();
        _curveList.Clear();
    }

    public void AddKeyFrame(float time, float value, float inTangent, float outTangent, float inWeight, float outWeight)
    {
        KeyFrame keyFrame = new KeyFrame();
        {
            keyFrame.Time = time;
            keyFrame.Value = value;
            keyFrame.InTangent = inTangent;
            keyFrame.OutTangent = outTangent;
            keyFrame.InWeight = (inWeight > 0) ? inWeight : HermiteCurve.DEFAULT_WEIGHT;
            keyFrame.OutWeight = (outWeight > 0) ? outWeight : HermiteCurve.DEFAULT_WEIGHT;
        }
        _keyFrameList.Add(keyFrame);
    }

    public void BuildCurves()
    {
        if (_keyFrameList.Count > 0)
        {
            _beginTime = _keyFrameList[0].Time;
            _endTime = _keyFrameList[_keyFrameList.Count - 1].Time;
        }
        else
        {
            _beginTime = 0;
            _endTime = 0;
        }

        _curveList.Clear();

        for (int index = 0; index < (_keyFrameList.Count - 1); ++index)
        {
            KeyFrame leftFrame = _keyFrameList[index];
            KeyFrame rightFrame = _keyFrameList[index + 1];
            HermiteCurve curve = new HermiteCurve((rightFrame.Time - leftFrame.Time), leftFrame.Value, rightFrame.Value, leftFrame.OutTangent, rightFrame.InTangent, leftFrame.OutWeight, rightFrame.InWeight);
            _curveList.Add(curve);
        }
    }

    public float Calculate(float time)
    {
        time = Math.Clamp(time, _beginTime, _endTime);

        for (int index = 0; index < (_keyFrameList.Count - 1); ++index)
        {
            KeyFrame leftFrame = _keyFrameList[index];
            KeyFrame rightFrame = _keyFrameList[index + 1];
            if (time <= rightFrame.Time)
            {
                return _curveList[index].Calculate(time - leftFrame.Time);
            }
        }

        return 0;
    }
}
