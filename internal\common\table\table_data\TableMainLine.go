/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableMainLine struct {
	// ============= 变量定义 =============
	// Id
	ID int32
	// 章节
	ChapterID int32
	// 关卡难度（非富文本）
	StagesNamePre2 int32
	// 关卡难度（富文本）
	StagesNamePre int32
	// 关卡号
	StagesName string
	// 关卡名称
	StagesNamePro int32
	// 上一关
	LastId int32
	// 下一关
	NextId int32
	// 关联战斗
	StageID int32
	// 体力
	Stamina int32
	// 展示奖励
	Reward int32
	// 阶段
	StageHealth []int32
	// 阶段奖励
	RewardGroup []int32
	// 进场动画 1 播 0 不播
	Run int32
	// 每分钟挂机金币（单位分钟）
	PlacementGold int32
	// 多少秒产一个图纸
	PlacementExp int32
	// 挂机产出道具时间（单位秒）
	PlacementTime []int32
	// 挂机掉落奖励
	DropGroup []int32
	// 走计算公式类奖励
	DropGroup1 []int32
	// 走正常掉落组奖励
	DropGroup2 []int32
	// 剧情关卡的剧情id
	StoryStageId int32
	// 战前剧情
	PreWarStorylD int32
	// 战后剧情
	AfterWarStorylD int32
	// 战中剧情
	MidWarStorylD int32
	// 关卡展示图片
	Picture string
	// 试用英雄
	SpecifySkin int32
}




// TableMainLineData 表格
type TableMainLineData struct {
	file    string
	dataMap map[int32]*TableMainLine
	Data    []*TableMainLine
	md5     string
}

// load 加载
func (tb *TableMainLineData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableMainLine{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableMainLine, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableMainLine)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableMainLine, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableMainLineData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableMainLine{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableMainLine, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableMainLine)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableMainLineData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableMainLineData) GetById(id int32) *TableMainLine {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableMainLineData) GetCloneById(id int32) *TableMainLine {
	v := tb.dataMap[id]
	out := &TableMainLine{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableMainLineData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableMainLineData) Foreach(call func(*TableMainLine) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableMainLineData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableMainLineData) Clone() ITable {
	ntb := &TableMainLineData{
		file:    tb.file,
		dataMap: make(map[int32]*TableMainLine),
		Data:    make([]*TableMainLine, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableMainLine{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
