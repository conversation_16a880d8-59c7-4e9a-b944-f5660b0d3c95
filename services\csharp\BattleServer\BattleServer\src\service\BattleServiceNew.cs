﻿//using battleserver.framework;
using Game.Core;
using System.Collections.Concurrent;
using Aurora.Framework;
using BattleServer.Game;
using LiteFrame.Helper;
using BattleServer.config;
using Aurora.Framework;

namespace BattleServer.Service;

public partial class BattleService
{
    private SessionComponent mSessionComponet;

    public BattleService(SessionComponent sessioncomponent)
    {
        mSessionComponet = sessioncomponent;
    }

    private Session GetSession()
    {
        return mSessionComponet.m_Session;
    }


    public async Task<CreateBattleResp> Handler_CreateBattle(CreateBattleReq request)
    {
        CreateSceneReq sceneCreateReq = new CreateSceneReq();
        {
            sceneCreateReq.SceneID = 1001;
            sceneCreateReq.players = request.CreateInfo.Players.ToList();
            sceneCreateReq.teams = request.CreateInfo.Teams.ToList();
        }
        Packet packet = Packet.Create(sceneCreateReq);
        ServerPacketHelper.ToSelfServerGlobalMgr(packet);
        CreateSceneResp? response = await GetSession().SendAsync(packet) as CreateSceneResp;

        Log.Info($"response  {Thread.CurrentThread.ManagedThreadId.ToString()}");

        return new CreateBattleResp() { ServerId = long.Parse(ConfigHelper.Config.ServerID.ToString()), BattleId = response.SceneID };
    }
    public CreateBattleResp CreateBattle(CreateBattleReq request)
    {
        var res = Handler_CreateBattle(request).GetAwaiter().GetResult();
        return res;
    }

    public async Task<EnterBattleResp> Handler_EnterBattle(EnterBattleReq request)
    {
        PlayerEnterSceneReq enterReq = new PlayerEnterSceneReq();
        {
            enterReq.Uid = request.Uid;
        }
        Packet packet = Packet.Create(enterReq);
        ServerPacketHelper.ToSelfServerGlobalMgr(packet);
        PlayerEnterSceneResp? response = await GetSession().SendAsync(packet) as PlayerEnterSceneResp;

        Log.Info($"EnterBattleReq response  {Thread.CurrentThread.ManagedThreadId.ToString()}");

        return new EnterBattleResp() { Code = 0 };
    }

    public EnterBattleResp EnterBattle(EnterBattleReq request)
    {
        var res = Handler_EnterBattle(request).GetAwaiter().GetResult();
        return res;
    }
    public async Task<SelectBufferResp> Handler_SelectBuffer(SelectBufferReq request)
    {
        PlayerSelectBufferReq req = new PlayerSelectBufferReq();
        {
            req.Uid = request.Uid;
            req.BufferID = request.BufferID;
        }
        Packet packet = Packet.Create(req);
        ServerPacketHelper.ToSelfServerGlobalMgr(packet);
        PlayerSelectBufferResp? response = await GetSession().SendAsync(packet) as PlayerSelectBufferResp;

        Log.Info($"PlayerSelectBufferReq response  {Thread.CurrentThread.ManagedThreadId.ToString()}");

        var resp = new SelectBufferResp();
        resp.Code = response.Code;
        resp.NewHeroes.Add(response.NewBoards);
        return resp;

        //return new SelectBufferResp() { Code = 0 };
    }

    public SelectBufferResp SelectBuffer(SelectBufferReq request)
    {
        var res = Handler_SelectBuffer(request).GetAwaiter().GetResult();
        return res;
    }

    public async Task<MergeHeroResp> Handler_MergeHero(MergeHeroReq request)
    {
        PlayerMergeHeroReq req = new PlayerMergeHeroReq();
        {
            req.pbMsg = request;
        }
        Packet packet = Packet.Create(req);
        ServerPacketHelper.ToSelfServerGlobalMgr(packet);
        PlayerMergeHeroResp? response = await GetSession().SendAsync(packet) as PlayerMergeHeroResp;

        Log.Info($"EnterBattleReq response  {Thread.CurrentThread.ManagedThreadId.ToString()}");

        return response.pbMsg;
    }

    public MergeHeroResp MergeHero(MergeHeroReq request)
    {
        var res = Handler_MergeHero(request).GetAwaiter().GetResult();
        return res;
    }

    public async Task<ReadyBattleResp> Handler_PlayerReady(ReadyBattleReq request)
    {
        PlayerReadyReq req = new PlayerReadyReq();
        {
            req.Uid = request.Uid;
            req.moveOperation = request.Moves.ToList();
        }
        Packet packet = Packet.Create(req);
        ServerPacketHelper.ToSelfServerGlobalMgr(packet);
        PlayerReadyResp? response = await GetSession().SendAsync(packet) as PlayerReadyResp;

        Log.Info($"EnterBattleReq response  {Thread.CurrentThread.ManagedThreadId.ToString()}");

        return new ReadyBattleResp { Code = -2 };
    }
    public ReadyBattleResp BattleReady(ReadyBattleReq request)
    {
        var res = Handler_PlayerReady(request).GetAwaiter().GetResult();
        return res;
    }

    public async Task<EndBattleResp> Handler_PlayerEndBattle(EndBattleReq request)
    {
        PlayerEndBattleReq req = new PlayerEndBattleReq();
        {
            req.Uid = request.Uid;
            req.Win = request.Win;
        }
        Packet packet = Packet.Create(req);
        ServerPacketHelper.ToSelfServerGlobalMgr(packet);
        PlayerEndBattleResp? response = await GetSession().SendAsync(packet) as PlayerEndBattleResp;

        Log.Info($"EndBattleReq response  {Thread.CurrentThread.ManagedThreadId.ToString()}");

        return new EndBattleResp { Code = 0 };
    }
    public EndBattleResp EndBattle(EndBattleReq request)
    {
        var res = Handler_PlayerEndBattle(request).GetAwaiter().GetResult();
        return res;
    }

    public LeaveBattleResp LeaveBattle(LeaveBattleReq request)
    {
        //PlayerLeaveBattleReq req = new PlayerLeaveBattleReq();
        //{
        //    req.Uid = request.Uid;
        //}
        //Packet packet = Packet.Create(req);
        //ServerPacketHelper.ToSelfServerGlobalMgr(packet);
        //PlayerLeaveBattleResp? response = GetSession().SendAsync(packet).GetAwaiter().GetResult() as PlayerLeaveBattleResp;
        //Log.Info($"LeaveBattleReq response  {Thread.CurrentThread.ManagedThreadId.ToString()}");
        return new LeaveBattleResp { Code = 0 };
    }

    public static void UpdateBattleState(long battleId, BattleState newState)
    {

    }
    public static void CleanupBattle(long battleId)
    {

    }

    public static void RemoveEliminatedPlayerMapping(long playerId)
    {
        
    }
    public static void ValidateAndRepairMappings()
    {
    }

    //public void testmsg()
    //{
    //    createbattlereq request = new createbattlereq();
    //    request.createinfo = new pbcreatebattleinfo();

    //    var player1 = new pbbattleplayerinfo();
    //    player1.uid = 1;
    //    request.createinfo.players.add(player1);
    //    var player2 = new pbbattleplayerinfo();
    //    player2.uid = 2;
    //    request.createinfo.players.add(player2);
    //    var player3 = new pbbattleplayerinfo();
    //    player3.uid = 3;
    //    request.createinfo.players.add(player3);
    //    var player4 = new pbbattleplayerinfo();
    //    player4.uid = 4;
    //    request.createinfo.players.add(player4);


    //    var team = new pbbattleteaminfo();

    //    var hero1 = new pbbattleheroinfo();
    //    hero1.id = 101;
    //    team.heros.add(hero1);
    //    var hero2 = new pbbattleheroinfo();
    //    hero2.id = 102;
    //    team.heros.add(hero2);
    //    var hero3 = new pbbattleheroinfo();
    //    hero3.id = 103;
    //    team.heros.add(hero3);
    //    //team.heros.add(hero);
    //    //team.heros.add(hero);

    //    request.createinfo.teams.add(team);
    //    request.createinfo.teams.add(team);
    //    request.createinfo.teams.add(team);
    //    request.createinfo.teams.add(team);

    //    createbattleresp res = handler_createbattle(request).getawaiter().getresult();
    //    log.error($"testmsg create scene resp id {res.battleid}");


    //    enterbattlereq enterreq = new enterbattlereq();
    //    enterreq.uid = 1;
    //    enterbattleresp enterresp = handler_enterbattle(enterreq).getawaiter().getresult();

    //    log.debug($"testmsg enter scene code {enterresp.code}");
    //}
}
