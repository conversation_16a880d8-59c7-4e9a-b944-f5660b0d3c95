#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMailTemplateConfig
	{

		public static readonly string TName="MailTemplateConfig.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 邮件类型(0仅内容1内容和奖励） 
		/// </summary> 
		public int MailType {get; set;}
		/// <summary> 
		/// 邮件子类型 
		/// </summary> 
		public int MailSubType {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int MailTitle {get; set;}
		/// <summary> 
		/// 内容 
		/// </summary> 
		public int MailCounts {get; set;}
		/// <summary> 
		/// 道具类型（第一个数值，只要是道具，填1就可以了） 
		/// </summary> 
		public int[] ItemType {get; set;}
		/// <summary> 
		/// 道具id 
		/// </summary> 
		public int[] ItemId {get; set;}
		/// <summary> 
		/// 道具数量 
		/// </summary> 
		public int[] ItemNum {get; set;}
		/// <summary> 
		/// 邮件过期时间 
		/// </summary> 
		public int MailValidityDays {get; set;}
		#endregion

		public static TableMailTemplateConfig GetData(int ID)
		{
			return TableManager.MailTemplateConfigData.Get(ID);
		}

		public static List<TableMailTemplateConfig> GetAllData()
		{
			return TableManager.MailTemplateConfigData.GetAll();
		}

	}
	public sealed partial class TableMailTemplateConfigData
	{
		private Dictionary<int, TableMailTemplateConfig> dict = new Dictionary<int, TableMailTemplateConfig>();
		private List<TableMailTemplateConfig> dataList = new List<TableMailTemplateConfig>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMailTemplateConfig.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMailTemplateConfig>>(jsonContent);
			foreach (TableMailTemplateConfig config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMailTemplateConfig Get(int id)
		{
			if (dict.TryGetValue(id, out TableMailTemplateConfig item))
				return item;
			return null;
		}

		public List<TableMailTemplateConfig> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
