// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v4.23.2
// source: MatchService.proto

package natsrpc

import (
	context "context"
)

// MatchServiceClient is the client API for MatchService service.
type MatchServiceClient interface {
	Match(ctx context.Context, req *MatchRequest) (*MatchResponse, error)
	AsyncMatch(ctx context.Context, req *MatchRequest, cb func(*MatchResponse, error))
	CancelMatch(ctx context.Context, req *CancelMatchRequest) (*CancelMatchResponse, error)
	AsyncCancelMatch(ctx context.Context, req *CancelMatchRequest, cb func(*CancelMatchResponse, error))
}

// MatchServiceServer is the server API for MatchService service.
type MatchServiceServer interface {
	Match(context.Context, *MatchRequest) (*MatchResponse, error)
	CancelMatch(context.Context, *CancelMatchRequest) (*CancelMatchResponse, error)
}
