#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableFirstCharge
	{

		public static readonly string TName="FirstCharge.json";

		#region 属性定义
		/// <summary> 
		/// ID（礼包ID） 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 本期目标描述(语言包） 
		/// </summary> 
		public int DescID {get; set;}
		/// <summary> 
		/// 礼包掉落道具（掉落组ID） 
		/// </summary> 
		public int FirstDayDropGroupId {get; set;}
		/// <summary> 
		/// 礼包掉落道具（掉落组ID） 
		/// </summary> 
		public int SecondDayDropGroupId {get; set;}
		/// <summary> 
		/// 礼包掉落道具（掉落组ID） 
		/// </summary> 
		public int ThirdDayDropGroupId {get; set;}
		/// <summary> 
		/// 原价 
		/// </summary> 
		public int OriginalPrice {get; set;}
		/// <summary> 
		/// 现价 
		/// </summary> 
		public int NowPrice {get; set;}
		/// <summary> 
		/// 首充专属id 
		/// </summary> 
		public int MidasId {get; set;}
		#endregion

		public static TableFirstCharge GetData(int ID)
		{
			return TableManager.FirstChargeData.Get(ID);
		}

		public static List<TableFirstCharge> GetAllData()
		{
			return TableManager.FirstChargeData.GetAll();
		}

	}
	public sealed partial class TableFirstChargeData
	{
		private Dictionary<int, TableFirstCharge> dict = new Dictionary<int, TableFirstCharge>();
		private List<TableFirstCharge> dataList = new List<TableFirstCharge>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableFirstCharge.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableFirstCharge>>(jsonContent);
			foreach (TableFirstCharge config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableFirstCharge Get(int id)
		{
			if (dict.TryGetValue(id, out TableFirstCharge item))
				return item;
			return null;
		}

		public List<TableFirstCharge> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
