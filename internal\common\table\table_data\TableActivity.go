/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableActivity struct {
	// ============= 变量定义 =============
	// 活动ID
	ID int32
	// 名称
	Name int32
	// 类型
	Type int32
	// 类型参数
	TypeValue [][]int32
	// 开启条件1
	OpenCondition1 int32
	// 条件1参数
	OpenCondition1Value int32
	// 开启条件2
	OpenCondition2 int32
	// 条件2参数
	OpenCondition2Value int32
	// 持续时间/S
	Duration int32
	// 间隔时间/S
	Interval int32
}




// TableActivityData 表格
type TableActivityData struct {
	file    string
	dataMap map[int32]*TableActivity
	Data    []*TableActivity
	md5     string
}

// load 加载
func (tb *TableActivityData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableActivity{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableActivity, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivity)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableActivity, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableActivityData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableActivity{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableActivity, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivity)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableActivityData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableActivityData) GetById(id int32) *TableActivity {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableActivityData) GetCloneById(id int32) *TableActivity {
	v := tb.dataMap[id]
	out := &TableActivity{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableActivityData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableActivityData) Foreach(call func(*TableActivity) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableActivityData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableActivityData) Clone() ITable {
	ntb := &TableActivityData{
		file:    tb.file,
		dataMap: make(map[int32]*TableActivity),
		Data:    make([]*TableActivity, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableActivity{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
