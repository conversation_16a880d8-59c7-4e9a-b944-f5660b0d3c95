// 协议ID类型为short，-32767 到 32767
//StartMessageID = 5000; // 必须以;分号结束
//MaxMessageID = 5999; // 必须以;分号结束

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.2
// source: LRProtocol.proto

package cs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//Logic服返回返回客户端登录结果
type LR_Test struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"` //1: 成功, 其他错误
}

func (x *LR_Test) Reset() {
	*x = LR_Test{}
	if protoimpl.UnsafeEnabled {
		mi := &file_LRProtocol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LR_Test) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LR_Test) ProtoMessage() {}

func (x *LR_Test) ProtoReflect() protoreflect.Message {
	mi := &file_LRProtocol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LR_Test.ProtoReflect.Descriptor instead.
func (*LR_Test) Descriptor() ([]byte, []int) {
	return file_LRProtocol_proto_rawDescGZIP(), []int{0}
}

func (x *LR_Test) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

var File_LRProtocol_proto protoreflect.FileDescriptor

var file_LRProtocol_proto_rawDesc = []byte{
	0x0a, 0x10, 0x4c, 0x52, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x22,
	0x21, 0x0a, 0x07, 0x4c, 0x52, 0x5f, 0x54, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x25, 0x5a, 0x23, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_LRProtocol_proto_rawDescOnce sync.Once
	file_LRProtocol_proto_rawDescData = file_LRProtocol_proto_rawDesc
)

func file_LRProtocol_proto_rawDescGZIP() []byte {
	file_LRProtocol_proto_rawDescOnce.Do(func() {
		file_LRProtocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_LRProtocol_proto_rawDescData)
	})
	return file_LRProtocol_proto_rawDescData
}

var file_LRProtocol_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_LRProtocol_proto_goTypes = []interface{}{
	(*LR_Test)(nil), // 0: GamePackage.LR_Test
}
var file_LRProtocol_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_LRProtocol_proto_init() }
func file_LRProtocol_proto_init() {
	if File_LRProtocol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_LRProtocol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LR_Test); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_LRProtocol_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_LRProtocol_proto_goTypes,
		DependencyIndexes: file_LRProtocol_proto_depIdxs,
		MessageInfos:      file_LRProtocol_proto_msgTypes,
	}.Build()
	File_LRProtocol_proto = out.File
	file_LRProtocol_proto_rawDesc = nil
	file_LRProtocol_proto_goTypes = nil
	file_LRProtocol_proto_depIdxs = nil
}
