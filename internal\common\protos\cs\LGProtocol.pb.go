// 协议ID类型为short，-32767 到 32767
//StartMessageID = 4000; // 必须以;分号结束
//MaxMessageID = 4999; // 必须以;分号结束

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.2
// source: LGProtocol.proto

package cs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "liteframe/internal/common/protos/public"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//Test
type LG_Test_Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID string `protobuf:"bytes,1,opt,name=platformID,proto3" json:"platformID,omitempty"`
}

func (x *LG_Test_Req) Reset() {
	*x = LG_Test_Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_LGProtocol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LG_Test_Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LG_Test_Req) ProtoMessage() {}

func (x *LG_Test_Req) ProtoReflect() protoreflect.Message {
	mi := &file_LGProtocol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LG_Test_Req.ProtoReflect.Descriptor instead.
func (*LG_Test_Req) Descriptor() ([]byte, []int) {
	return file_LGProtocol_proto_rawDescGZIP(), []int{0}
}

func (x *LG_Test_Req) GetPlatformID() string {
	if x != nil {
		return x.PlatformID
	}
	return ""
}

// 通知Lobby执行JS返回
type LG_GlobalToLobbyRunJS_Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultStr       string `protobuf:"bytes,1,opt,name=resultStr,proto3" json:"resultStr,omitempty"`             //要运行的脚本返回结果
	ScriptExecuteID string `protobuf:"bytes,2,opt,name=scriptExecuteID,proto3" json:"scriptExecuteID,omitempty"` //脚本唯一ID，CMS用于提取结果
}

func (x *LG_GlobalToLobbyRunJS_Req) Reset() {
	*x = LG_GlobalToLobbyRunJS_Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_LGProtocol_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LG_GlobalToLobbyRunJS_Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LG_GlobalToLobbyRunJS_Req) ProtoMessage() {}

func (x *LG_GlobalToLobbyRunJS_Req) ProtoReflect() protoreflect.Message {
	mi := &file_LGProtocol_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LG_GlobalToLobbyRunJS_Req.ProtoReflect.Descriptor instead.
func (*LG_GlobalToLobbyRunJS_Req) Descriptor() ([]byte, []int) {
	return file_LGProtocol_proto_rawDescGZIP(), []int{1}
}

func (x *LG_GlobalToLobbyRunJS_Req) GetResultStr() string {
	if x != nil {
		return x.ResultStr
	}
	return ""
}

func (x *LG_GlobalToLobbyRunJS_Req) GetScriptExecuteID() string {
	if x != nil {
		return x.ScriptExecuteID
	}
	return ""
}

//获取所有活动的状态信息
type LG_GlobalGetAllActivityInfo_Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerId int32 `protobuf:"varint,1,opt,name=serverId,proto3" json:"serverId,omitempty"` //服务器的id
}

func (x *LG_GlobalGetAllActivityInfo_Req) Reset() {
	*x = LG_GlobalGetAllActivityInfo_Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_LGProtocol_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LG_GlobalGetAllActivityInfo_Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LG_GlobalGetAllActivityInfo_Req) ProtoMessage() {}

func (x *LG_GlobalGetAllActivityInfo_Req) ProtoReflect() protoreflect.Message {
	mi := &file_LGProtocol_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LG_GlobalGetAllActivityInfo_Req.ProtoReflect.Descriptor instead.
func (*LG_GlobalGetAllActivityInfo_Req) Descriptor() ([]byte, []int) {
	return file_LGProtocol_proto_rawDescGZIP(), []int{2}
}

func (x *LG_GlobalGetAllActivityInfo_Req) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

//请求挖宝活动房间号
type LG_GlobalActivityDigRoom_Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformId int64 `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"` //参与的玩家ID
}

func (x *LG_GlobalActivityDigRoom_Req) Reset() {
	*x = LG_GlobalActivityDigRoom_Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_LGProtocol_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LG_GlobalActivityDigRoom_Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LG_GlobalActivityDigRoom_Req) ProtoMessage() {}

func (x *LG_GlobalActivityDigRoom_Req) ProtoReflect() protoreflect.Message {
	mi := &file_LGProtocol_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LG_GlobalActivityDigRoom_Req.ProtoReflect.Descriptor instead.
func (*LG_GlobalActivityDigRoom_Req) Descriptor() ([]byte, []int) {
	return file_LGProtocol_proto_rawDescGZIP(), []int{3}
}

func (x *LG_GlobalActivityDigRoom_Req) GetPlatformId() int64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

//请求兑换码兑换
type LG_CDKeyUse_Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformId       int64  `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`             //当前玩家ID
	PlayerCreateTime int32  `protobuf:"varint,2,opt,name=playerCreateTime,proto3" json:"playerCreateTime,omitempty"` //角色创建时间戮，单位：秒
	PlayerLevel      int32  `protobuf:"varint,3,opt,name=playerLevel,proto3" json:"playerLevel,omitempty"`           //当前等级
	Code             string `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`                          //兑换码
	GroupUseCount    int32  `protobuf:"varint,5,opt,name=groupUseCount,proto3" json:"groupUseCount,omitempty"`       //玩家在当前码所在批次中已使用的次数
}

func (x *LG_CDKeyUse_Req) Reset() {
	*x = LG_CDKeyUse_Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_LGProtocol_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LG_CDKeyUse_Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LG_CDKeyUse_Req) ProtoMessage() {}

func (x *LG_CDKeyUse_Req) ProtoReflect() protoreflect.Message {
	mi := &file_LGProtocol_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LG_CDKeyUse_Req.ProtoReflect.Descriptor instead.
func (*LG_CDKeyUse_Req) Descriptor() ([]byte, []int) {
	return file_LGProtocol_proto_rawDescGZIP(), []int{4}
}

func (x *LG_CDKeyUse_Req) GetPlatformId() int64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *LG_CDKeyUse_Req) GetPlayerCreateTime() int32 {
	if x != nil {
		return x.PlayerCreateTime
	}
	return 0
}

func (x *LG_CDKeyUse_Req) GetPlayerLevel() int32 {
	if x != nil {
		return x.PlayerLevel
	}
	return 0
}

func (x *LG_CDKeyUse_Req) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *LG_CDKeyUse_Req) GetGroupUseCount() int32 {
	if x != nil {
		return x.GroupUseCount
	}
	return 0
}

var File_LGProtocol_proto protoreflect.FileDescriptor

var file_LGProtocol_proto_rawDesc = []byte{
	0x0a, 0x10, 0x4c, 0x47, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x1a,
	0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2d, 0x0a, 0x0b, 0x4c, 0x47, 0x5f, 0x54, 0x65, 0x73,
	0x74, 0x5f, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x44, 0x22, 0x63, 0x0a, 0x19, 0x4c, 0x47, 0x5f, 0x47, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x54, 0x6f, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x52, 0x75, 0x6e, 0x4a, 0x53, 0x5f, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x72,
	0x12, 0x28, 0x0a, 0x0f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x44, 0x22, 0x3d, 0x0a, 0x1f, 0x4c, 0x47,
	0x5f, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x22, 0x3e, 0x0a, 0x1c, 0x4c, 0x47, 0x5f,
	0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x69,
	0x67, 0x52, 0x6f, 0x6f, 0x6d, 0x5f, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x0f, 0x4c, 0x47,
	0x5f, 0x43, 0x44, 0x4b, 0x65, 0x79, 0x55, 0x73, 0x65, 0x5f, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a,
	0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x10, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x73, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x25, 0x5a, 0x23, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_LGProtocol_proto_rawDescOnce sync.Once
	file_LGProtocol_proto_rawDescData = file_LGProtocol_proto_rawDesc
)

func file_LGProtocol_proto_rawDescGZIP() []byte {
	file_LGProtocol_proto_rawDescOnce.Do(func() {
		file_LGProtocol_proto_rawDescData = protoimpl.X.CompressGZIP(file_LGProtocol_proto_rawDescData)
	})
	return file_LGProtocol_proto_rawDescData
}

var file_LGProtocol_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_LGProtocol_proto_goTypes = []interface{}{
	(*LG_Test_Req)(nil),                     // 0: GamePackage.LG_Test_Req
	(*LG_GlobalToLobbyRunJS_Req)(nil),       // 1: GamePackage.LG_GlobalToLobbyRunJS_Req
	(*LG_GlobalGetAllActivityInfo_Req)(nil), // 2: GamePackage.LG_GlobalGetAllActivityInfo_Req
	(*LG_GlobalActivityDigRoom_Req)(nil),    // 3: GamePackage.LG_GlobalActivityDigRoom_Req
	(*LG_CDKeyUse_Req)(nil),                 // 4: GamePackage.LG_CDKeyUse_Req
}
var file_LGProtocol_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_LGProtocol_proto_init() }
func file_LGProtocol_proto_init() {
	if File_LGProtocol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_LGProtocol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LG_Test_Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_LGProtocol_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LG_GlobalToLobbyRunJS_Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_LGProtocol_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LG_GlobalGetAllActivityInfo_Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_LGProtocol_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LG_GlobalActivityDigRoom_Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_LGProtocol_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LG_CDKeyUse_Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_LGProtocol_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_LGProtocol_proto_goTypes,
		DependencyIndexes: file_LGProtocol_proto_depIdxs,
		MessageInfos:      file_LGProtocol_proto_msgTypes,
	}.Build()
	File_LGProtocol_proto = out.File
	file_LGProtocol_proto_rawDesc = nil
	file_LGProtocol_proto_goTypes = nil
	file_LGProtocol_proto_depIdxs = nil
}
