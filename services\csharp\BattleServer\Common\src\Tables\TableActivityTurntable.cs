#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityTurntable
	{

		public static readonly string TName="ActivityTurntable.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 价格 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 每日可购买次数 
		/// </summary> 
		public int dailyBuyNumber {get; set;}
		/// <summary> 
		/// 抽取类型 
		/// </summary> 
		public int RandomType {get; set;}
		/// <summary> 
		/// 掉落 
		/// </summary> 
		public int[][] Drop {get; set;}
		#endregion

		public static TableActivityTurntable GetData(int ID)
		{
			return TableManager.ActivityTurntableData.Get(ID);
		}

		public static List<TableActivityTurntable> GetAllData()
		{
			return TableManager.ActivityTurntableData.GetAll();
		}

	}
	public sealed partial class TableActivityTurntableData
	{
		private Dictionary<int, TableActivityTurntable> dict = new Dictionary<int, TableActivityTurntable>();
		private List<TableActivityTurntable> dataList = new List<TableActivityTurntable>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityTurntable.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityTurntable>>(jsonContent);
			foreach (TableActivityTurntable config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityTurntable Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityTurntable item))
				return item;
			return null;
		}

		public List<TableActivityTurntable> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
