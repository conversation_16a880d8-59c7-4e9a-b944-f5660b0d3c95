#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableSevenSignIn
	{

		public static readonly string TName="SevenSignIn.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		#endregion

		public static TableSevenSignIn GetData(int ID)
		{
			return TableManager.SevenSignInData.Get(ID);
		}

		public static List<TableSevenSignIn> GetAllData()
		{
			return TableManager.SevenSignInData.GetAll();
		}

	}
	public sealed partial class TableSevenSignInData
	{
		private Dictionary<int, TableSevenSignIn> dict = new Dictionary<int, TableSevenSignIn>();
		private List<TableSevenSignIn> dataList = new List<TableSevenSignIn>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableSevenSignIn.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableSevenSignIn>>(jsonContent);
			foreach (TableSevenSignIn config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableSevenSignIn Get(int id)
		{
			if (dict.TryGetValue(id, out TableSevenSignIn item))
				return item;
			return null;
		}

		public List<TableSevenSignIn> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
