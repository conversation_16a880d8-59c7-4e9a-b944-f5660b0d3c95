/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableFunctionUnlock struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 入口名称
	Name int32
	// 活动ID(暂时关联UI表，如果不想跳转配置-1)
	ActivityID int32
	// 开启条件（-1.默认；0.等级；1.关卡，2任务，3未开放）
	Conditions int32
	// 参数ID
	StepID int32
	// 公司等级
	company int32
	// 指挥官等级
	CommanderLevel int32
	// 开服时间 秒
	OpenServerTime int32
	// 军衔等级
	MilitaryRankLevel int32
	// 功能图标
	Icon string
	// 特效播放延迟(除100)
	particalDelay int32
	// 特效id对应EffectRes表
	effectID int32
	// 是否展示解锁弹窗
	showTip int32
	// 循环特效id对应EffectRes表
	loopEffectID int32
	// unlock  1生效；-1不生效
	unlock int32
}




// TableFunctionUnlockData 表格
type TableFunctionUnlockData struct {
	file    string
	dataMap map[int32]*TableFunctionUnlock
	Data    []*TableFunctionUnlock
	md5     string
}

// load 加载
func (tb *TableFunctionUnlockData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableFunctionUnlock{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableFunctionUnlock, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableFunctionUnlock)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableFunctionUnlock, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableFunctionUnlockData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableFunctionUnlock{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableFunctionUnlock, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableFunctionUnlock)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableFunctionUnlockData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableFunctionUnlockData) GetById(id int32) *TableFunctionUnlock {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableFunctionUnlockData) GetCloneById(id int32) *TableFunctionUnlock {
	v := tb.dataMap[id]
	out := &TableFunctionUnlock{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableFunctionUnlockData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableFunctionUnlockData) Foreach(call func(*TableFunctionUnlock) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableFunctionUnlockData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableFunctionUnlockData) Clone() ITable {
	ntb := &TableFunctionUnlockData{
		file:    tb.file,
		dataMap: make(map[int32]*TableFunctionUnlock),
		Data:    make([]*TableFunctionUnlock, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableFunctionUnlock{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
