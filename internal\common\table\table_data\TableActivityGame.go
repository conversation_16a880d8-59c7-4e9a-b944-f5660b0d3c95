/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableActivityGame struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 游戏币ID
	GameCoinId int32
	// 道具显示ID1
	PlayDropGroupId int32
	// 道具显示ID2
	PlayDropGroupId2 int32
	// 游戏一次的消耗
	PlayCost int32
	// 时间掉落ID
	TimeDropGroupId []int32
	// 时间掉落间隔
	TimeDropLimit []int32
	// 阶段奖励
	StageRewardExp []int32
	// 阶段奖励
	StageRewardDropGroupId []int32
	// 扩展参数（倍率）
	para1 []int32
	// 扩展参数（倍率概率）
	para2 []int32
	// 道具显示ID1
	FirstPlayDropGroupId int32
	// 道具显示ID2
	FirstPlayDropGroupId2 int32
}




// TableActivityGameData 表格
type TableActivityGameData struct {
	file    string
	dataMap map[int32]*TableActivityGame
	Data    []*TableActivityGame
	md5     string
}

// load 加载
func (tb *TableActivityGameData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableActivityGame{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableActivityGame, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivityGame)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableActivityGame, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableActivityGameData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableActivityGame{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableActivityGame, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivityGame)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableActivityGameData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableActivityGameData) GetById(id int32) *TableActivityGame {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableActivityGameData) GetCloneById(id int32) *TableActivityGame {
	v := tb.dataMap[id]
	out := &TableActivityGame{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableActivityGameData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableActivityGameData) Foreach(call func(*TableActivityGame) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableActivityGameData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableActivityGameData) Clone() ITable {
	ntb := &TableActivityGameData{
		file:    tb.file,
		dataMap: make(map[int32]*TableActivityGame),
		Data:    make([]*TableActivityGame, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableActivityGame{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
