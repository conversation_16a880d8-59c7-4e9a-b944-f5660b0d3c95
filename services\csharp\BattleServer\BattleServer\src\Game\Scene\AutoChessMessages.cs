//using BattleServer.Service;
//using Game.Core;
//using Aurora.Framework;
//using System.Collections.Generic;

//namespace BattleServer.Game.AutoChess
//{
//    /// <summary>
//    /// AutoChess消息类型枚举
//    /// </summary>
//    public enum AutoChessMessageType : ushort
//    {
//        None = 1000, // 从1000开始，避免与现有消息冲突
//        AutoChessCreateBattleReq,
//        AutoChessCreateBattleResp,
//        AutoChessEnterBattleReq,
//        AutoChessEnterBattleResp,
//        AutoChessSelectBuffReq,
//        AutoChessSelectBuffResp,
//        AutoChessMergeHeroReq,
//        AutoChessMergeHeroResp,
//        AutoChessBattleReadyReq,
//        AutoChessBattleReadyResp,
//        AutoChessEndBattleReq,
//        AutoChessEndBattleResp,
//        AutoChessLeaveBattleReq,
//        AutoChessLeaveBattleResp,
//    }

//    /// <summary>
//    /// 创建AutoChess战斗请求
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessCreateBattleReq)]
//    [MessageType(MessageClass.IRequest)]
//    [ResponseType(typeof(AutoChessCreateBattleResp))]
//    public class AutoChessCreateBattleReq : IRequest
//    {
//        public List<PBBattlePlayerInfo> players;
//        public List<PBBattleTeamInfo> teams;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// 创建AutoChess战斗响应
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessCreateBattleResp)]
//    [MessageType(MessageClass.IResponse)]
//    public class AutoChessCreateBattleResp : IResponse
//    {
//        public int Error { get; set; }
//        public string Message { get; set; }
//        public bool Replyed { get; set; }

//        public long BattleId;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess玩家进入战斗请求
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessEnterBattleReq)]
//    [MessageType(MessageClass.IRequest)]
//    [ResponseType(typeof(AutoChessEnterBattleResp))]
//    public class AutoChessEnterBattleReq : IRequest
//    {
//        public ulong Uid;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess玩家进入战斗响应
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessEnterBattleResp)]
//    [MessageType(MessageClass.IResponse)]
//    public class AutoChessEnterBattleResp : IResponse
//    {
//        public int Error { get; set; }
//        public string Message { get; set; }
//        public bool Replyed { get; set; }

//        public int Code;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess选择Buff请求
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessSelectBuffReq)]
//    [MessageType(MessageClass.IRequest)]
//    [ResponseType(typeof(AutoChessSelectBuffResp))]
//    public class AutoChessSelectBuffReq : IRequest
//    {
//        public ulong Uid;
//        public uint BufferID;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess选择Buff响应
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessSelectBuffResp)]
//    [MessageType(MessageClass.IResponse)]
//    public class AutoChessSelectBuffResp : IResponse
//    {
//        public int Error { get; set; }
//        public string Message { get; set; }
//        public bool Replyed { get; set; }

//        public int Code;
//        public List<PBCheckerBoard> NewBoards;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess合成英雄请求
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessMergeHeroReq)]
//    [MessageType(MessageClass.IRequest)]
//    [ResponseType(typeof(AutoChessMergeHeroResp))]
//    public class AutoChessMergeHeroReq : IRequest
//    {
//        public MergeHeroReq pbMsg;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess合成英雄响应
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessMergeHeroResp)]
//    [MessageType(MessageClass.IResponse)]
//    public class AutoChessMergeHeroResp : IResponse
//    {
//        public int Error { get; set; }
//        public string Message { get; set; }
//        public bool Replyed { get; set; }

//        public MergeHeroResp pbMsg;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess玩家准备请求
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessBattleReadyReq)]
//    [MessageType(MessageClass.IRequest)]
//    [ResponseType(typeof(AutoChessBattleReadyResp))]
//    public class AutoChessBattleReadyReq : IRequest
//    {
//        public ulong Uid;
//        public List<PBMoveOperation> Moves;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess玩家准备响应
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessBattleReadyResp)]
//    [MessageType(MessageClass.IResponse)]
//    public class AutoChessBattleReadyResp : IResponse
//    {
//        public int Error { get; set; }
//        public string Message { get; set; }
//        public bool Replyed { get; set; }

//        public int Code;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess结束战斗请求
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessEndBattleReq)]
//    [MessageType(MessageClass.IRequest)]
//    [ResponseType(typeof(AutoChessEndBattleResp))]
//    public class AutoChessEndBattleReq : IRequest
//    {
//        public ulong Uid;
//        public bool Win;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess结束战斗响应
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessEndBattleResp)]
//    [MessageType(MessageClass.IResponse)]
//    public class AutoChessEndBattleResp : IResponse
//    {
//        public int Error { get; set; }
//        public string Message { get; set; }
//        public bool Replyed { get; set; }

//        public int Code;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess离开战斗请求
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessLeaveBattleReq)]
//    [MessageType(MessageClass.IRequest)]
//    [ResponseType(typeof(AutoChessLeaveBattleResp))]
//    public class AutoChessLeaveBattleReq : IRequest
//    {
//        public ulong Uid;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }

//    /// <summary>
//    /// AutoChess离开战斗响应
//    /// </summary>
//    [MessageID((ushort)AutoChessMessageType.AutoChessLeaveBattleResp)]
//    [MessageType(MessageClass.IResponse)]
//    public class AutoChessLeaveBattleResp : IResponse
//    {
//        public int Error { get; set; }
//        public string Message { get; set; }
//        public bool Replyed { get; set; }

//        public int Code;

//        public bool Precheck
//        {
//            get { return true; }
//        }
//    }
//}
