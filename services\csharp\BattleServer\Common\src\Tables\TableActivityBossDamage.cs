#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityBossDamage
	{

		public static readonly string TName="ActivityBossDamage.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 功能 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 伤害 
		/// </summary> 
		public int Damage {get; set;}
		/// <summary> 
		/// 奖励 
		/// </summary> 
		public int[][] Drop {get; set;}
		#endregion

		public static TableActivityBossDamage GetData(int ID)
		{
			return TableManager.ActivityBossDamageData.Get(ID);
		}

		public static List<TableActivityBossDamage> GetAllData()
		{
			return TableManager.ActivityBossDamageData.GetAll();
		}

	}
	public sealed partial class TableActivityBossDamageData
	{
		private Dictionary<int, TableActivityBossDamage> dict = new Dictionary<int, TableActivityBossDamage>();
		private List<TableActivityBossDamage> dataList = new List<TableActivityBossDamage>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityBossDamage.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityBossDamage>>(jsonContent);
			foreach (TableActivityBossDamage config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityBossDamage Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityBossDamage item))
				return item;
			return null;
		}

		public List<TableActivityBossDamage> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
