#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableAttrRange
	{

		public static readonly string TName="AttrRange.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 属性类型 
		/// </summary> 
		public int AttrId {get; set;}
		/// <summary> 
		/// 等级下限 
		/// </summary> 
		public int MinLevel {get; set;}
		/// <summary> 
		/// 等级上限 
		/// </summary> 
		public int MaxLevel {get; set;}
		/// <summary> 
		/// 初始消耗系数（放大6位） 
		/// </summary> 
		public int Param1 {get; set;}
		/// <summary> 
		/// 每个等级减少系数（放大6位） 
		/// </summary> 
		public int Param2 {get; set;}
		#endregion

		public static TableAttrRange GetData(int ID)
		{
			return TableManager.AttrRangeData.Get(ID);
		}

		public static List<TableAttrRange> GetAllData()
		{
			return TableManager.AttrRangeData.GetAll();
		}

	}
	public sealed partial class TableAttrRangeData
	{
		private Dictionary<int, TableAttrRange> dict = new Dictionary<int, TableAttrRange>();
		private List<TableAttrRange> dataList = new List<TableAttrRange>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableAttrRange.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableAttrRange>>(jsonContent);
			foreach (TableAttrRange config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableAttrRange Get(int id)
		{
			if (dict.TryGetValue(id, out TableAttrRange item))
				return item;
			return null;
		}

		public List<TableAttrRange> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
