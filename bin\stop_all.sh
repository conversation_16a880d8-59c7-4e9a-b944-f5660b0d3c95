#!/bin/bash

# 设置当前目录
current_dir=$(dirname $(readlink -f $0))
cd ${current_dir}

# 停止webserver
if [ -f webserver.pid ]; then
    pid=$(cat webserver.pid)
    if kill -0 $pid 2>/dev/null; then
        kill $pid
        echo "Stopped webserver (PID: $pid)"
    else
        echo "Webserver not running"
    fi
    rm webserver.pid
fi

# 停止gameserver
if [ -f gameserver.pid ]; then
    pid=$(cat gameserver.pid)
    if kill -0 $pid 2>/dev/null; then
        kill $pid
        echo "Stopped gameserver (PID: $pid)"
    else
        echo "Gameserver not running"
    fi
    rm gameserver.pid
fi

# 停止matchserver
if [ -f matchserver.pid ]; then
    pid=$(cat matchserver.pid)
    if kill -0 $pid 2>/dev/null; then
        kill $pid
        echo "Stopped matchserver (PID: $pid)"
    else
        echo "Matchserver not running"
    fi
    rm matchserver.pid
fi

# 停止payserver
if [ -f payserver.pid ]; then
    pid=$(cat payserver.pid)
    if kill -0 $pid 2>/dev/null; then
        kill $pid
        echo "Stopped payserver (PID: $pid)"
    else
        echo "Payserver not running"
    fi
    rm payserver.pid
fi

# 停止battleserver
if [ -f battleserver.pid ]; then
    pid=$(cat battleserver.pid)
    if kill -0 $pid 2>/dev/null; then
        kill $pid
        echo "Stopped battleserver (PID: $pid)"
    else
        echo "Battleserver not running"
    fi
    rm battleserver.pid
fi

echo "All servers stopped"