/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableGuildShopList struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 商店物品组ID
	Group int32
	// 花费金币类型
	CostType int32
	// 免费次数
	FreeCount int32
	// 广告次数
	ADCount int32
	// 购买次数
	BuyCount int32
	// 限购刷新周期
	RefreshType int32
	// 个人奖励
	DropGroupID int32
	// 价格
	Price int32
	// 解锁条件（先不做）
	Condition []int32
	// 广告id（先不做）
	AdId int32
	// 优惠（客户端展示）
	UpValue int32
}




// TableGuildShopListData 表格
type TableGuildShopListData struct {
	file    string
	dataMap map[int32]*TableGuildShopList
	Data    []*TableGuildShopList
	md5     string
}

// load 加载
func (tb *TableGuildShopListData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableGuildShopList{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableGuildShopList, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuildShopList)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableGuildShopList, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableGuildShopListData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableGuildShopList{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableGuildShopList, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuildShopList)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableGuildShopListData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableGuildShopListData) GetById(id int32) *TableGuildShopList {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableGuildShopListData) GetCloneById(id int32) *TableGuildShopList {
	v := tb.dataMap[id]
	out := &TableGuildShopList{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableGuildShopListData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableGuildShopListData) Foreach(call func(*TableGuildShopList) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableGuildShopListData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableGuildShopListData) Clone() ITable {
	ntb := &TableGuildShopListData{
		file:    tb.file,
		dataMap: make(map[int32]*TableGuildShopList),
		Data:    make([]*TableGuildShopList, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableGuildShopList{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
