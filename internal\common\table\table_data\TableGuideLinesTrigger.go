/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableGuideLinesTrigger struct {
	// ============= 变量定义 =============
	// 引导ID
	ID int32
	// 下一个引导
	NextGroupId int32
	// 条件：前置引导条件
	PreGroupId int32
	// 特定条件1
	RunCondition1 int32
	// 特定条件1参数
	RunParam1 string
	// 特定条件2
	RunCondition2 int32
	// 特定条件2参数
	RunParam2 string
	// 特定条件3
	RunCondition3 int32
	// 特定条件3参数
	RunParam3 string
	// 触发类型
	StartType int32
	// 参数信息
	StartParameter int32
	// 强制结束引导条件类型
	ForceEndType int32
	// 参数信息（结束条件参数）
	ForceEndParams []int32
	// 引导组首ID，只填写该引导组首Id
	GuildStartId int32
	// 是否在主场景触发1.不在2.在3.花路主线关卡触发引导时填写
	IsInMainScene int32
	// 触发机制是否需要回退
	IsGoBack int32
	// 触发引导时需要保留的界面
	StayUI []string
}




// TableGuideLinesTriggerData 表格
type TableGuideLinesTriggerData struct {
	file    string
	dataMap map[int32]*TableGuideLinesTrigger
	Data    []*TableGuideLinesTrigger
	md5     string
}

// load 加载
func (tb *TableGuideLinesTriggerData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableGuideLinesTrigger{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableGuideLinesTrigger, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuideLinesTrigger)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableGuideLinesTrigger, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableGuideLinesTriggerData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableGuideLinesTrigger{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableGuideLinesTrigger, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableGuideLinesTrigger)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableGuideLinesTriggerData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableGuideLinesTriggerData) GetById(id int32) *TableGuideLinesTrigger {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableGuideLinesTriggerData) GetCloneById(id int32) *TableGuideLinesTrigger {
	v := tb.dataMap[id]
	out := &TableGuideLinesTrigger{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableGuideLinesTriggerData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableGuideLinesTriggerData) Foreach(call func(*TableGuideLinesTrigger) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableGuideLinesTriggerData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableGuideLinesTriggerData) Clone() ITable {
	ntb := &TableGuideLinesTriggerData{
		file:    tb.file,
		dataMap: make(map[int32]*TableGuideLinesTrigger),
		Data:    make([]*TableGuideLinesTrigger, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableGuideLinesTrigger{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
