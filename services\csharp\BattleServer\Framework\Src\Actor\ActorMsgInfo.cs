﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-27
//*********************************************************
/*
namespace Aurora.Framework
{
    //Actor消息远程调用信息
    public class ActorMsgInfo
    {
        public long ActorID { get; }
        public Packet Request { get; }

        public ATask<IResponse> Tcs { get; }

        public ActorMsgInfo(Packet pkt)
        {
            ActorID = pkt.ActorID;
            Request = pkt;
            Tcs = new ATask<IResponse>();
        }
    }
}
*/