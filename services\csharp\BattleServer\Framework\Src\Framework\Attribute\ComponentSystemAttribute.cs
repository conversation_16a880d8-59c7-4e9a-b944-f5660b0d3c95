﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************

using System;

namespace Aurora.Framework
{
    //ComponentSystem类的标签
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public sealed class ComponentSystemAttribute : AuroraAttribute
    {
        public Type CompType { get; }
        public ComponentSystemAttribute(Type compType)
        {
            this.CompType = compType;
        }
    }


    //ComponentSystem的函数标签，用于在合适的时机调用
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public class ComponentSystemMethodAttribute : AuroraMethodAttribute
    {

    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodAwakeAttribute : ComponentSystemMethodAttribute
    {
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodAwake1Attribute : ComponentSystemMethodAttribute
    {
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodAwake2Attribute : ComponentSystemMethodAttribute
    {
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodDestroyAttribute : ComponentSystemMethodAttribute
    {
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodFrequentlyUpdateAttribute : ComponentSystemMethodAttribute
    {
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodUpdateAttribute : ComponentSystemMethodAttribute
    {
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodLateUpdateAttribute : ComponentSystemMethodAttribute
    {
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MethodSlowlyUpdateAttribute : ComponentSystemMethodAttribute
    {
    }
}
