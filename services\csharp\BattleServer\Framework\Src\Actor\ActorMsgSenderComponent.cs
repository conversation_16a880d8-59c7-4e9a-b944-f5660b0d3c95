﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-27
//*********************************************************

using System;
using System.Collections.Generic;

namespace Aurora.Framework
{
    /*
    public class ActorMsgSenderComponent:BaseComponent,IAwake
    {
        public readonly Dictionary<int, ActorMsgInfo> ActorRequestCallback = new Dictionary<int, ActorMsgInfo>();
        public int RpcID = 0;
        public static ActorMsgSenderComponent Instance { get; set; }
    }

    [ComponentSystem(typeof(ActorMsgSenderComponent))]
    public static class ActorMsgSenderComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(ActorMsgSenderComponent self)
        {
            ActorMsgSenderComponent.Instance = self;
        }
        //处理内网发送回来的Response消息
        private static void Handle(ActorMsgInfo self, IResponse response)
        {
            if(response.Error == FError.ERR_ActorTimeout)
            {
                string msgName = self.Request.MsgID.ToString();
                Type msgType = MessageIDComponent.Instance.GetMsgType(self.Request.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                self.Tcs.SetException(new FrameworkException($"RPC异常:Actor消息超时,消息Type：{msgName}"));
                return;
            }
            self.Tcs.SetResult(response);
        }
        //收到Actor包，触发异步回调，内网的Actor包指的是进程，通过配置的进程ID取得
        public static void HandleActorResponse(this ActorMsgSenderComponent self, Packet pkt)
        {
            if(pkt.MsgClass != MessageClass.IResponse)
            {
                string msgName = pkt.MsgID.ToString();
                Type msgType = MessageIDComponent.Instance.GetMsgType(pkt.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                Log.Error($"处理Actor回复消息异常: Type=[{msgName}] 不是 IResponse 类型!");
                return;
            }
            ActorMsgInfo msgInfo = null;
            if(!self.ActorRequestCallback.TryGetValue(pkt.RpcID, out msgInfo))
            {
                return;
            }
            self.ActorRequestCallback.Remove(pkt.RpcID);
            IResponse response = pkt.GetMessage() as IResponse;
            Handle(msgInfo, response);
            ReferencePool.Release(pkt);
        }

        //发送进程间的Actor消息，没有返回
        public static void Send(this ActorMsgSenderComponent self, long actorID, IMessage message)
        {
            //找到对应的进程，发送
            ProcessActorID processActorID = new ProcessActorID(actorID);
            Session session = NetInnerComponent.Instance.GetSession(processActorID.Process);
            if(session == null)
            {
                throw new FrameworkException("Send error! Session is Null!");
            }
            //将消息包装成Packet，发送
            Packet pkt = ReferencePool.Acquire<Packet>();
            pkt.ActorID = actorID;
            pkt.MsgType = MessageType.PB;
            pkt.SetMessage(message);

            session.Send(pkt);
        }

        public static void Send(this ActorMsgSenderComponent self,Packet pkt)
        {
            if(pkt.ActorID == 0)
            {
                string msgName = pkt.MsgID.ToString();
                Type msgType = MessageIDComponent.Instance.GetMsgType(pkt.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                throw new FrameworkException($"Actor消息发送错误：AcotrID is 0, Type=[{msgName}]");
            }
            //找到对应的进程，发送
            ProcessActorID processActorID = new ProcessActorID(pkt.ActorID);
            Session session = NetInnerComponent.Instance.GetSession(processActorID.Process);
            pkt.ActorID = processActorID.ActorID;
            session.Send(pkt);

        }
        //发送异步Actor操作消息
        public static async ATask<IResponse> Request(this ActorMsgSenderComponent self, long actorID,IMessage request)
        {
            ushort msgID = MessageIDComponent.Instance.GetMsgID(request.GetType());
            if (actorID == 0)
            {
                throw new FrameworkException($"Actor消息请求错误,AcotrID is 0, Type=[{request.GetType().Name}]");
            }
            //将消息包装成Packet，发送
            Packet pkt = ReferencePool.Acquire<Packet>();
            pkt.ActorID = actorID;
            pkt.RpcID = ++self.RpcID;
            pkt.MsgType = MessageType.PB;
            pkt.SetMessage(request);

            return await self._Request(pkt);
        }

        //转发异步Actor操作消息
        public static async ATask<IResponse> Request(this ActorMsgSenderComponent self,Packet pkt)
        {
            if (pkt.ActorID == 0)
            {
                string msgName = pkt.MsgID.ToString();
                Type msgType = MessageIDComponent.Instance.GetMsgType(pkt.MsgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                throw new FrameworkException($"Actor消息请求错误,AcotrID is 0, Type=[{msgName}]");
            }
            pkt.RpcID = ++self.RpcID;
            return await self._Request(pkt);
        }

        private static async ATask<IResponse> _Request(this ActorMsgSenderComponent self, Packet pkt)
        {
            ActorMsgInfo msgInfo = new ActorMsgInfo(pkt);
            self.ActorRequestCallback.Add(pkt.RpcID, msgInfo);
            self.Send(pkt);
            //等待异步回调回来返回值
            IResponse response =  await msgInfo.Tcs;
            return response;
        }
    }*/
}
