#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableNewGuideLinesInfo
	{

		public static readonly string TName="NewGuideLinesInfo.json";

		#region 属性定义
		/// <summary> 
		/// 引导步骤ID，ID不是连续的，每一段连续的ID则是一个引导组 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 下一步新手的id    （-1自动结束    0不一定结束，取决于trigger表EndParameter列结束条件 
		/// </summary> 
		public int NextId {get; set;}
		/// <summary> 
		/// 点击按钮（填写路径）1、点击全屏继续。3、路径有则为gameObject绝对路径 
		/// </summary> 
		public int ButtonPathType {get; set;}
		/// <summary> 
		/// 点击按钮 
		/// </summary> 
		public string ButtonPath {get; set;}
		/// <summary> 
		/// 打开界面 
		/// </summary> 
		public int[] UIOpen {get; set;}
		/// <summary> 
		/// 校验ID 
		/// </summary> 
		public int CheckFunId {get; set;}
		/// <summary> 
		/// 断线回退引导步骤ID（-1为直接完成当前引导组） 
		/// </summary> 
		public int GoBackId {get; set;}
		/// <summary> 
		/// 是否显示半透蒙版，-1不显示 1显示  2、透明蒙版 
		/// </summary> 
		public int Masking {get; set;}
		/// <summary> 
		/// 滑动列表   1不可滑动    -1无 
		/// </summary> 
		public int ScrollView {get; set;}
		/// <summary> 
		/// 查找延迟(除以100)    
		/// </summary> 
		public int FindDelay {get; set;}
		/// <summary> 
		/// 是否有光圈（-1没有，1显示手 2、没有手，挖空的3、拖动合成 4 拖动上阵 5、第一次拖动龙上阵） 
		/// </summary> 
		public int IsHighlight {get; set;}
		/// <summary> 
		/// 手方向（1.右下2.左下3.左上4.右上） 
		/// </summary> 
		public int HandDir {get; set;}
		/// <summary> 
		/// 光圈缩放比例（真实值是除以100的小数） 
		/// </summary> 
		public int HighlightScaleX {get; set;}
		/// <summary> 
		/// 光圈缩放比例（真实值是除以100的小数） 
		/// </summary> 
		public int HighlightScaleY {get; set;}
		/// <summary> 
		/// 偏移X( 比例偏移  除以10000） 例如填写2500 为4分之一 
		/// </summary> 
		public int OffsetX {get; set;}
		/// <summary> 
		/// 偏移Y( 比例偏移  除以10000） 
		/// </summary> 
		public int OffsetY {get; set;}
		/// <summary> 
		/// 对话id 字典id 
		/// </summary> 
		public int DialogID {get; set;}
		/// <summary> 
		/// 对话位置类型0中间1上面2下面 
		/// </summary> 
		public int DialogPosType {get; set;}
		/// <summary> 
		/// 对话偏移正数向上 负数向下 
		/// </summary> 
		public int DialogOffsetY {get; set;}
		/// <summary> 
		/// 龙岛建筑定位id（DragonBuildAreaId）只有龙岛的引导能用 
		/// </summary> 
		public int AreaId {get; set;}
		/// <summary> 
		/// 战斗是否暂停 
		/// </summary> 
		public int IsBattleStop {get; set;}
		#endregion

		public static TableNewGuideLinesInfo GetData(int ID)
		{
			return TableManager.NewGuideLinesInfoData.Get(ID);
		}

		public static List<TableNewGuideLinesInfo> GetAllData()
		{
			return TableManager.NewGuideLinesInfoData.GetAll();
		}

	}
	public sealed partial class TableNewGuideLinesInfoData
	{
		private Dictionary<int, TableNewGuideLinesInfo> dict = new Dictionary<int, TableNewGuideLinesInfo>();
		private List<TableNewGuideLinesInfo> dataList = new List<TableNewGuideLinesInfo>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableNewGuideLinesInfo.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableNewGuideLinesInfo>>(jsonContent);
			foreach (TableNewGuideLinesInfo config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableNewGuideLinesInfo Get(int id)
		{
			if (dict.TryGetValue(id, out TableNewGuideLinesInfo item))
				return item;
			return null;
		}

		public List<TableNewGuideLinesInfo> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
