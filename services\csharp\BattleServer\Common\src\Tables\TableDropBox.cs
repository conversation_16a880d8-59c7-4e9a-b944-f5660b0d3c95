#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableDropBox
	{

		public static readonly string TName="DropBox.json";

		#region 属性定义
		/// <summary> 
		/// 掉落包ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 掉落规则(每个道具池改为权重模式，从这个池中随机出 指定数量的道具)，后2列同时有效，不配置默认只掉落1个。掉落多个时受后2列影响，后面的每个道具池都是按此规则处理。此列不再使用，固定为0模式。 
		/// </summary> 
		public int DropOpt {get; set;}
		/// <summary> 
		/// 每个道具池最大掉落数量 
		/// </summary> 
		public int DropNum {get; set;}
		/// <summary> 
		/// 每个道具池掉落是否可重复(0不可 1可以) 
		/// </summary> 
		public int IsCanDuplicate {get; set;}
		/// <summary> 
		/// 道具池1，二维数组格式，配置多个元素格式为：[[掉落类型，ID（公式类型或道具ID），掉落权重（万分比），掉落数量基础值，掉落数量参数1(-1：表示受局内等级影响)，掉落数量参数2],[…],[…]]，目前二维数组统一使用string格式，后续导表工具优化后再修改为数组类型。
 
		/// </summary> 
		public string DropItem1 {get; set;}
		/// <summary> 
		/// 掉落道具2 
		/// </summary> 
		public string DropItem2 {get; set;}
		/// <summary> 
		/// 掉落道具3 
		/// </summary> 
		public string DropItem3 {get; set;}
		/// <summary> 
		/// 掉落道具4 
		/// </summary> 
		public string DropItem4 {get; set;}
		/// <summary> 
		/// 掉落道具5 
		/// </summary> 
		public string DropItem5 {get; set;}
		/// <summary> 
		/// 掉落道具6 
		/// </summary> 
		public string DropItem6 {get; set;}
		/// <summary> 
		/// 掉落道具7 
		/// </summary> 
		public string DropItem7 {get; set;}
		/// <summary> 
		/// 掉落道具8 
		/// </summary> 
		public string DropItem8 {get; set;}
		/// <summary> 
		/// 掉落道具9 
		/// </summary> 
		public string DropItem9 {get; set;}
		/// <summary> 
		/// 掉落道具10 
		/// </summary> 
		public string DropItem10 {get; set;}
		#endregion

		public static TableDropBox GetData(int ID)
		{
			return TableManager.DropBoxData.Get(ID);
		}

		public static List<TableDropBox> GetAllData()
		{
			return TableManager.DropBoxData.GetAll();
		}

	}
	public sealed partial class TableDropBoxData
	{
		private Dictionary<int, TableDropBox> dict = new Dictionary<int, TableDropBox>();
		private List<TableDropBox> dataList = new List<TableDropBox>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableDropBox.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableDropBox>>(jsonContent);
			foreach (TableDropBox config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableDropBox Get(int id)
		{
			if (dict.TryGetValue(id, out TableDropBox item))
				return item;
			return null;
		}

		public List<TableDropBox> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
