﻿//*********************************************************
// Framework
// Author:  zhouchen
// Desc:	一个基类，
// Date  :  Fri Jan  5 17:44:37 CST 2024
//*********************************************************

using System.IO;
using System.Net;

namespace Aurora.Framework
{
    public abstract class NetInnerHelper
    {
        //! 获取一个内网连接的IP和端口号
        public abstract bool GetInnerIpAndPort(int nZwid, ushort nServerNum, out string ip, out int port);
    }

}
