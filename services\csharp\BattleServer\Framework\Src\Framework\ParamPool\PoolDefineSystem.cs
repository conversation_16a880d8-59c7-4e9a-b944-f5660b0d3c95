﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-2-20
//*********************************************************

using System;
using System.Buffers.Binary;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Xml;

namespace Aurora.Framework
{
    //配置使用
    public static class eParamFlag
    {
        public const uint Server = 1 << 0;       //服务器之前同步
        public const uint ClientSelf = 1 << 1;   //客户端自己
        public const uint ClientOther = 1 << 2;    //aoi广播
        public const uint Save = 1 << 3;         //扩展使用：存储
        public const uint ClientManual = 1 << 4;         //客户端手动同步
        public const uint PlayerInfoServer = 1 << 5;       //同步给玩家信息服
        public const uint PlayerBaseInfo = 1 << 6;         //生成玩家信息服基础信息组件，并且同步给玩家信息服

    };

    //运行时使用
    public static class eParamSync
    {
        public const uint None = 0;
        public const uint Default = 1 << 0;      //默认，脏更新
        public const uint ClearDirty = 1 << 1;   //是否清除脏标记
    }

    public static class eValueType
    {
        public const int None = 0;
        public const int int8 = 1;
        public const int uint8 = 2;
        public const int int16 = 3;
        public const int uint16 = 4;
        public const int int32 = 5;
        public const int uint32 = 6;
        public const int int64 = 7;
        public const int uint64 = 8;
        public const int f32 = 9;
        public const int f64 = 10;
        public const int Bool = 11;
        public const int char16 = 12;
        public const int char32 = 13;
        public const int char64 = 14;
        public const int char128 = 15;
        public const int char256 = 16;

        public static int GetType(string typeStr)
        {
            switch(typeStr)
            {
                case "int8":
                case "sbyte":
                    return eValueType.int8;
                case "uint8":
                case "byte":
                    return eValueType.uint8;
                case "int16":
                case "short":
                    return eValueType.int16;
                case "uint16":
                case "ushort":
                    return eValueType.uint16;
                case "int32":
                case "int":
                    return eValueType.int32;
                case "uint32":
                case "uint":
                    return eValueType.uint32;
                case "int64":
                case "long":
                    return eValueType.int64;
                case "uint64":
                case "ulong":
                    return eValueType.uint64;
                case "f32":
                case "float":
                    return eValueType.f32;
                case "f64":
                case "double":
                    return eValueType.f64;
                case "bool":
                case "Bool":
                    return eValueType.Bool;
                case "char16":
                    return eValueType.char16;
                case "char32":
                    return eValueType.char32;
                case "char64":
                    return eValueType.char64;
                case "char128":
                    return eValueType.char128;
                case "char256":
                    return eValueType.char256;
                case "string":
                    return eValueType.char64;
                default:
                    return eValueType.None;
            }
        }
        public static string ValueTypeToString(int valueType)
        {
            switch (valueType)
            {
                case eValueType.int8:
                    return "sbyte";
                case eValueType.uint8:
                    return "byte";
                case eValueType.int16:
                    return "short";
                case eValueType.uint16:
                    return "ushort";
                case eValueType.int32:
                    return "int";
                case eValueType.uint32:
                    return "uint";
                case eValueType.int64:
                    return "long";
                case eValueType.uint64:
                    return "ulong";
                case eValueType.f32:
                    return "float";
                case eValueType.f64:
                    return "double";
                case eValueType.Bool:
                    return "bool";
                case eValueType.char16:
                case eValueType.char32:
                case eValueType.char64:
                case eValueType.char128:
                case eValueType.char256:
                    return "string";
                default:
                    return "Boom!Wasted...";
            }
        }
    }



    public class AParam
    {
        public int PoolType;
        public int Index;
        public int ParentPoolType;
        public int ParentIndex;
        public string Name;
        public int ValueType;
        public int Size;
        public int Offset;
        public uint Flag;
        public bool IsList;
        public bool IsDictionary;
        public bool IsQueue;
        public bool IsStruct;
        public bool IsSubComp;
        public string Set;
        public string StructType;//结构的话对应PoolDefine的id
        public PoolDefine ElementDefine;//如果是结构型集合的元素数据结构

        public AParam()
        {
            PoolType = -1;
            Index = -1;
            ParentPoolType = -1;
            ParentIndex = -1;
            Size = 0;
            Offset = 0;
            Flag = 0;
        }

        public bool CheckFlag(uint checkFlag)
        {
            return (Flag & checkFlag) != 0 ? true : false;
        }

        private bool SetCheck(byte[] buff, int paramType)
        {
            if (ValueType != paramType)
            {
                Log.Error($"Param set value error: type- {ValueType} not type-{paramType}, ParamName={Name}");
                return false;
            }
            if (buff.Length < Offset + Size)
            {
                Log.Error($"Param set value error: buff is too short, ParamName={Name}");
                return false;
            }
            return true;
        }

        private bool GetCheck(int paramType)
        {
            if (ValueType != paramType)
            {
                Log.Error($"Param get value error: type- {ValueType} not type-{paramType}, ParamName={Name}");
                return false;
            }
            return true;
        }

        //此方法避免运行时使用，有装箱操作，耗GC
        //为初始化方便使用
        public void SetValueByObject(byte[] buff, object value)
        {
            switch(ValueType)
            {
                case eValueType.int8:
                    SetValue(buff, (sbyte)value);
                    break;
                case eValueType.uint8:
                    SetValue(buff, (byte)value);
                    break;
                case eValueType.int16:
                    SetValue(buff, (short)value);
                    break;
                case eValueType.uint16:
                    SetValue(buff, (ushort)value);
                    break;
                case eValueType.int32:
                    SetValue(buff, (int)value);
                    break;
                case eValueType.uint32:
                    SetValue(buff, (uint)value);
                    break;
                case eValueType.int64:
                    SetValue(buff, (long)value);
                    break;
                case eValueType.uint64:
                    SetValue(buff, (ulong)value);
                    break;
                case eValueType.f32:
                    SetValue(buff, (float)value);
                    break;
                case eValueType.f64:
                    SetValue(buff, (double)value);
                    break;
                case eValueType.Bool:
                    SetValue(buff, (bool)value);
                    break;
                case eValueType.char16:
                case eValueType.char32:
                case eValueType.char64:
                case eValueType.char128:
                case eValueType.char256:
                    SetValue(buff, (string)value);
                    break;
            }
        }

        public object GetValueByObject(byte[] buff)
        {
            switch (ValueType)
            {
                case eValueType.int8:
                    return GetValue(buff, (sbyte)0);
                case eValueType.uint8:
                    return GetValue(buff, (byte)0);
                case eValueType.int16:
                    return GetValue(buff, (short)0);
                case eValueType.uint16:
                    return GetValue(buff, (ushort)0);
                case eValueType.int32:
                    return GetValue(buff, (int)0);
                case eValueType.uint32:
                    return GetValue(buff, (uint)0);
                case eValueType.int64:
                    return GetValue(buff, (long)0);
                case eValueType.uint64:
                    return GetValue(buff, (ulong)0);
                case eValueType.f32:
                    return GetValue(buff, (float)0);
                case eValueType.f64:
                    return GetValue(buff, (double)0);
                case eValueType.Bool:
                    return GetValue(buff, false);
                case eValueType.char16:
                case eValueType.char32:
                case eValueType.char64:
                case eValueType.char128:
                case eValueType.char256:
                    return GetValue(buff, "");

                default:
                    return null;
            }
        }

        public void SetValue(byte[] buff, byte value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.uint8))
                return;

            buff[Offset] = value;
        }
        public void SetValue(byte[] buff, sbyte value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.int8))
                return;
            buff[Offset] = (byte)value;
        }
        public void SetValue(byte[] buff, short value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.int16))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitives.WriteInt16LittleEndian(span, value);
        }
        public void SetValue(byte[] buff, ushort value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.uint16))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitives.WriteUInt16LittleEndian(span, value);
        }
        public void SetValue(byte[] buff, int value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.int32))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitives.WriteInt32LittleEndian(span, value);
        }
        public void SetValue(byte[] buff, uint value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.uint32))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitives.WriteUInt32LittleEndian(span, value);
        }
        public void SetValue(byte[] buff, long value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.int64))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitives.WriteInt64LittleEndian(span, value);
        }
        public void SetValue(byte[] buff, ulong value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.uint64))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitives.WriteUInt64LittleEndian(span, value);
        }
        public void SetValue(byte[] buff, bool value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.Bool))
                return;
            buff[Offset] = value ? (byte)1 : (byte)0;
        }
        public void SetValue(byte[] buff, float value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.f32))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitivesEx.WriteSingleLittleEndian(span, value);
        }
        public void SetValue(byte[] buff, double value)
        {
            if (buff == null) return;
            if (!SetCheck(buff, eValueType.uint64))
                return;
            var span = new Span<byte>(buff, Offset, Size);
            BinaryPrimitivesEx.WriteDoubleLittleEndian(span, value);
        }


        public void SetValue(byte[] buff, string value)
        {
            if (ValueType == eValueType.char16 || ValueType == eValueType.char32 ||
                ValueType == eValueType.char64 || ValueType == eValueType.char128 ||
                ValueType == eValueType.char256)
            {
                byte[] str = System.Text.Encoding.UTF8.GetBytes(value);
                int length = str.Length;
                if (length > Size)
                    Size = length;
                //TODO：这里最好是按字截取，不然会导致截掉半个字解析出错
                Buffer.BlockCopy(str, 0, buff, Offset, Size);
                return;
            }
            else
            {
                Log.Error($"Param set value error: type- {ValueType} not type-string, ParamName={Name}");
                return;
            }
        }

        public byte GetValue(byte[] buff, byte def)
        {
            if (!GetCheck(eValueType.uint8))
            {
                return def;
            }
            if (buff == null) return def;
            return buff[Offset];
        }
        public sbyte GetValue(byte[] buff, sbyte def)
        {
            if (!GetCheck(eValueType.int8))
            {
                return def;
            }
            if (buff == null) return def;
            return (sbyte)buff[Offset];
        }
        public short GetValue(byte[] buff, short def)
        {
            if (!GetCheck(eValueType.int16))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitives.ReadInt16LittleEndian(span);
        }
        public ushort GetValue(byte[] buff, ushort def)
        {
            if (!GetCheck(eValueType.uint16))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitives.ReadUInt16LittleEndian(span);
        }
        public int GetValue(byte[] buff, int def)
        {
            if (!GetCheck(eValueType.int32))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitives.ReadInt32LittleEndian(span);
        }
        public uint GetValue(byte[] buff, uint def)
        {
            if (!GetCheck(eValueType.uint32))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitives.ReadUInt32LittleEndian(span);
        }
        public long GetValue(byte[] buff, long def)
        {
            if (!GetCheck(eValueType.int64))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitives.ReadInt64LittleEndian(span);
        }
        public ulong GetValue(byte[] buff, ulong def)
        {
            if (!GetCheck(eValueType.uint64))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitives.ReadUInt64LittleEndian(span);
        }
        public bool GetValue(byte[] buff, bool def)
        {
            if (!GetCheck(eValueType.Bool))
            {
                return def;
            }
            if (buff == null) return def;
            return (buff[Offset] == 0) ? false : true;
        }
        public float GetValue(byte[] buff, float def)
        {
            if (!GetCheck(eValueType.f32))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitivesEx.ReadSingleLittleEndian(span);
        }
        public double GetValue(byte[] buff, double def)
        {
            if (!GetCheck(eValueType.f64))
            {
                return def;
            }
            var span = new Span<byte>(buff, Offset, Size);
            return BinaryPrimitivesEx.ReadDoubleLittleEndian(span);
        }
        public string GetValue(byte[] buff, string def)
        {
            if (ValueType == eValueType.char16 || ValueType == eValueType.char32 ||
                ValueType == eValueType.char64 || ValueType == eValueType.char128 ||
                ValueType == eValueType.char256)
            {
                return System.Text.Encoding.UTF8.GetString(buff, Offset, Size);
            }
            else
            {
                return def;
            }
        }

        public void Copy2Buff(byte[] buff, byte[] destBuff, int destOffset)
        {
            if (destBuff == null) return;
            if (destBuff.Length < Size)
            {
                Log.Error("AParam copy 2 buff error: dest buff is too short!");
                return;
            }
            Buffer.BlockCopy(buff, Offset, destBuff, destOffset, Size);
        }

        public void CopyFrom(byte[] buff, byte[] oriBuff, int oriOffset)
        {
            if (oriBuff == null) return;
            if (oriBuff.Length < Size)
            {
                Log.Error("AParam copy from buff error: ori buff is too short!");
                return;
            }
            Buffer.BlockCopy(oriBuff, oriOffset, buff, Offset, Size);
        }

        public bool CopyFromIfChanged(byte[] buff, byte[] oriBuff, int oriOffset)
        {
            if (oriBuff == null) return false;
            if (oriBuff.Length < Size)
            {
                Log.Error("AParam copy from buff error: ori buff is too short!");
                return false;
            }
            var span = new Span<byte>(buff, Offset, Size);
            var orispan = new Span<byte>(oriBuff, oriOffset, Size);
            int result = span.SequenceCompareTo(orispan);
            if (result == 0)
                return false;

            Buffer.BlockCopy(oriBuff, oriOffset, buff, Offset, Size);
            return true;
        }
    }


    public class PoolConfigData
    {
        public int DataID { get; private set; }

        private byte[] m_Data;
        public byte[] DataBuff
        {
            get { return m_Data; }
        }

        public int DataSize { get; private set; }
        public PoolConfigData(PoolDefine define,int dataID)
        {
            if (define == null)
                return;
            DataSize = define.PoolSize;
            m_Data = new byte[DataSize];
            DataID = dataID;
        }
    }

    public class PoolDefine
    {
        public string PoolName;
        public int PoolType;
        public int PoolSize;
        public int MaxIndex;
        public bool IsPureStruct;// 是纯粹的结构,只生成protobuffer类型,不生成组件分体类
        public bool IsSubComponent;
        private bool m_bSyncClientOther;   //是否同步给周围
        private bool m_bSyncClientSelf;  //是否同步给自己
        private bool m_bSyncPlayerInfoServer;  //是否同步给玩家信息服

        public bool IsSyncClientOther
        {
            get { return m_bSyncClientOther; }
        }
        public bool IsSyncClientSelf
        {
            get { return m_bSyncClientSelf; }
        }
        public bool IsSyncPlayerInfoServer
        {
            get { return m_bSyncPlayerInfoServer; }
        }
        private Dictionary<int, AParam> m_Index2Params = new Dictionary<int, AParam>();
        private Dictionary<string, AParam> m_Name2Params = new Dictionary<string, AParam>();
        private Dictionary<int, PoolConfigData> m_ConfigDatas = new Dictionary<int, PoolConfigData>();
        public void ReadParam(XmlNodeList xnl)
        {
            if (xnl == null)
                return;

            for (int i = 0; i < xnl.Count; ++i)
            {
                AParam param = new AParam();
                XmlElement xMem = (XmlElement)xnl[i];
                param.PoolType = PoolType;
                param.Index = Convert.ToInt32(xMem.GetAttribute("Index"), CultureInfo.InvariantCulture);
                param.ParentPoolType = Convert.ToInt32(xMem.GetAttribute("ParentPoolType"), CultureInfo.InvariantCulture);
                param.ParentIndex = Convert.ToInt32(xMem.GetAttribute("ParentIndex"), CultureInfo.InvariantCulture);
                param.Name = xMem.GetAttribute("Name");
                param.Offset = Convert.ToInt32(xMem.GetAttribute("Offset"), CultureInfo.InvariantCulture);
                param.Size = Convert.ToInt32(xMem.GetAttribute("Size"), CultureInfo.InvariantCulture);
                param.ValueType = eValueType.GetType(xMem.GetAttribute("ValueType"));
                param.Flag = Convert.ToUInt32(xMem.GetAttribute("Flag"), CultureInfo.InvariantCulture);
                param.IsList = false;
                param.IsDictionary = false;
                param.IsQueue = false;
                string isSet = xMem.GetAttribute("Set");
                param.Set = isSet;
                if (isSet == "IsList")
                    param.IsList = true;
                else if(isSet == "IsDictionary")
                    param.IsDictionary = true;
                else if (isSet == "IsQueue")
                    param.IsQueue = true;
                param.IsStruct = Convert.ToBoolean(xMem.GetAttribute("IsStruct"), CultureInfo.InvariantCulture);
                param.IsSubComp = Convert.ToBoolean(xMem.GetAttribute("IsSubComp"), CultureInfo.InvariantCulture);
                param.StructType = xMem.GetAttribute("StructType");

                if (param.Index > MaxIndex)
                {
                    MaxIndex = param.Index;
                }
                m_Index2Params.Add(param.Index, param);
                m_Name2Params.Add(param.Name, param);

                bool clientOther = param.CheckFlag(eParamFlag.ClientOther);
                bool clientSelf = param.CheckFlag(eParamFlag.ClientSelf);
                bool playerInfoServer = param.CheckFlag(eParamFlag.PlayerInfoServer);

                m_bSyncClientOther |= clientOther;
                m_bSyncClientSelf |= clientSelf;
                m_bSyncPlayerInfoServer |= playerInfoServer;
            }
        }

        public AParam GetParam(int index)
        {
            if (m_Index2Params.TryGetValue(index, out AParam param))
                return param;

            return null;
        }

        public AParam GetParam(string name)
        {
            if (m_Name2Params.TryGetValue(name, out AParam param))
                return param;

            return null;
        }

        public void AddConfigData(PoolConfigData data)
        {
            if (data == null) return;
            m_ConfigDatas.Add(data.DataID, data);
        }

        public PoolConfigData GetConfigData(int dataID)
        {
            if(m_ConfigDatas.TryGetValue(dataID, out PoolConfigData data))
                return data;

            return null;
        }

        public Dictionary<int,AParam> GetAllParams()
        {
            return m_Index2Params;
        }
    }
    public class PoolDefineSystem : Singleton<PoolDefineSystem>
    {
        private Dictionary<int, PoolDefine> m_PoolDefines = new Dictionary<int, PoolDefine>();
        private Dictionary<string, PoolDefine> m_PoolDefinesByName = new Dictionary<string, PoolDefine>();
        /// <summary>
        /// Load方式，参数为XML的路径
        /// </summary>
        /// <param name="path"></param>
        public void LoadFromXML(string path)
        {
            XmlDocument xDoc = new XmlDocument();
            xDoc.Load(path);

            loadXML(xDoc);
        }

        /// <summary>
        /// LoadXml方式，参数为XML的TEXT
        /// </summary>
        /// <param name="xmlText"></param>
        public void LoadFromXMLText(string xmlText)
        {
            XmlDocument xDoc = new XmlDocument();
            xDoc.LoadXml(xmlText);

            loadXML(xDoc);
        }

        private void loadXML(XmlDocument xDoc)
        {
            XmlElement xRoot = (XmlElement)xDoc.SelectSingleNode("ParamDefine");
            if (xRoot == null)
                return;
            XmlNodeList xnl = xRoot.ChildNodes;
            int i = 0;
            m_PoolDefines.Clear();
            m_PoolDefinesByName.Clear();
            for (; i < xnl.Count; ++i)
            {
                PoolDefine pDefine = new PoolDefine();
                XmlElement xDef = (XmlElement)xnl[i];
                pDefine.PoolName = xDef.GetAttribute("Name");
                pDefine.PoolType = Convert.ToInt32(xDef.GetAttribute("ID"), CultureInfo.InvariantCulture);
                pDefine.PoolSize = Convert.ToInt32(xDef.GetAttribute("PoolSize"), CultureInfo.InvariantCulture);
                pDefine.IsPureStruct = Convert.ToBoolean(xDef.GetAttribute("IsPureStruct"), CultureInfo.InvariantCulture);
                pDefine.IsSubComponent = Convert.ToBoolean(xDef.GetAttribute("IsSubComponent"), CultureInfo.InvariantCulture);
                XmlNodeList xParamlist = xDef.ChildNodes;
                pDefine.ReadParam(xParamlist);
                m_PoolDefines.Add(pDefine.PoolType, pDefine);
                m_PoolDefinesByName.Add(pDefine.PoolName, pDefine);
            }
        }

        public PoolDefine GetPoolDefine(int poolType)
        {
            if (m_PoolDefines.TryGetValue(poolType, out PoolDefine poolDefine))
                return poolDefine;
            return null;
        }
        // 不要在运行时使用!!
        public PoolDefine GetPoolDefineByName(string poolName)
        {
            if (m_PoolDefinesByName.TryGetValue(poolName, out PoolDefine poolDefine))
                return poolDefine;
            return null;
        }

        public Dictionary<int,PoolDefine> GetAllDefine()
        {
            return m_PoolDefines;
        }
    }

    public enum EParamIncrementType
    {
        EInvalid =0,
        EList = 1,
        EDictionary =2,
        EQueue = 3,
    }
    
    public interface IParamIncrement
    {
        public EParamIncrementType GetType();
    }

    public class ListParamIncrement<T> : IParamIncrement
    {
        public Action<PoolEntity, ListDicSyncOperateType, int, T> m_callBack = null;

        public ListParamIncrement(Action<PoolEntity, ListDicSyncOperateType,int, T> callBack)
        {
            m_callBack = callBack;
        }

        public void Invoke(PoolEntity poolEntity, ListDicSyncOperateType eOp,int index, T data)
        {
            m_callBack.Invoke(poolEntity, eOp,index, data);
        }

        public new EParamIncrementType GetType()
        {
            return EParamIncrementType.EList;
        }
    }

    public class DicParamIncrement<T> : IParamIncrement
    {
        public Action<PoolEntity, ListDicSyncOperateType,long, T> m_callBack = null;

        public DicParamIncrement(Action<PoolEntity, ListDicSyncOperateType,long, T> callBack)
        {
            m_callBack = callBack;
        }

        public void Invoke(PoolEntity poolEntity, ListDicSyncOperateType eOp,long key, T data)
        {
            m_callBack.Invoke(poolEntity, eOp,key, data);
        }

        public new EParamIncrementType GetType()
        {
            return EParamIncrementType.EDictionary;
        }
    }

    public class QueueParamIncrement<T> : IParamIncrement
    {
        public Action<PoolEntity, ListDicSyncOperateType, T> m_callBack = null;

        public QueueParamIncrement(Action<PoolEntity, ListDicSyncOperateType, T> callBack)
        {
            m_callBack = callBack;
        }

        public void Invoke(PoolEntity poolEntity, ListDicSyncOperateType eOp, T data)
        {
            m_callBack.Invoke(poolEntity, eOp, data);
        }

        public new EParamIncrementType GetType()
        {
            return EParamIncrementType.EQueue;
        }
    }


    public class PoolCallbackSystem : Singleton<PoolCallbackSystem>
    {
        public delegate void OnParamChanged(PoolEntity owner);
        public delegate void OnListenParamChanged(int poolType, int index, PoolEntity owner);

        private Dictionary<long, List<OnParamChanged>> m_AllCallbacks = new Dictionary<long, List<OnParamChanged>>();
        private Dictionary<long, IParamIncrement> m_AllIncrementCallbacks = new Dictionary<long, IParamIncrement>();

        private List<OnListenParamChanged> m_OnBackSysteCall = null;

        private void InitIncrementCallBacks(MethodInfo method)
        {
            //Increment Sync Attribute
            var incrementList = method.GetCustomAttributes<ParamIncrementChangeAttribute>(false);

            foreach (ParamIncrementChangeAttribute item in incrementList)
            {
                try
                {
                    switch (item.type)
                    {
                        case EParamIncrementType.EList:
                        {
                            Type actionGenericType = typeof(Action<,,,>);
                            Type actionType = actionGenericType.MakeGenericType(new Type[]
                                { typeof(PoolEntity), typeof(ListDicSyncOperateType), typeof(int), item.dataType }); //replace
                            Type callbackGenericType = typeof(ListParamIncrement<>);
                            Type callbackType = callbackGenericType.MakeGenericType(new Type[] { item.dataType });
                            var cb = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) });

                            long key = GenKey(item.PoolType, item.Index);
                            if (!m_AllIncrementCallbacks.ContainsKey(key))
                            {
                                m_AllIncrementCallbacks.Add(key, cb as IParamIncrement);
                            }
                        }
                        break;
                        case EParamIncrementType.EDictionary:
                        {
                            Type actionGenericType = typeof(Action<,,,>);
                            Type actionType = actionGenericType.MakeGenericType(new Type[]
                                { typeof(PoolEntity), typeof(ListDicSyncOperateType), typeof(long), item.dataType }); //replace
                            Type callbackGenericType = typeof(DicParamIncrement<>);
                            Type callbackType = callbackGenericType.MakeGenericType(new Type[] { item.dataType });
                            var cb = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) });

                            long key = GenKey(item.PoolType, item.Index);
                            if (!m_AllIncrementCallbacks.ContainsKey(key))
                            {
                                m_AllIncrementCallbacks.Add(key, cb as IParamIncrement);
                            }
                        }
                        break;
                        case EParamIncrementType.EQueue:
                        {
                            Type actionGenericType = typeof(Action<,,,>);
                            Type actionType = actionGenericType.MakeGenericType(new Type[]
                                { typeof(PoolEntity), typeof(ListDicSyncOperateType), typeof(long), item.dataType }); //replace
                            Type callbackGenericType = typeof(QueueParamIncrement<>);
                            Type callbackType = callbackGenericType.MakeGenericType(new Type[] { item.dataType });
                            var cb = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) });

                            long key = GenKey(item.PoolType, item.Index);
                            if (!m_AllIncrementCallbacks.ContainsKey(key))
                            {
                                m_AllIncrementCallbacks.Add(key, cb as IParamIncrement);
                            }

                        }
                        break;
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"item.PoolType ： {item.PoolType} ， item.Index : {item.Index}  " + e.Message);
                }
            }
        }
        
        
        public void ListenBackSysteCall(MethodInfo method)
        {
            if (m_OnBackSysteCall == null)
            {
                m_OnBackSysteCall = new List<OnListenParamChanged>();
            }
            OnListenParamChanged callback = Delegate.CreateDelegate(typeof(OnListenParamChanged), method) as OnListenParamChanged;
            for (int i = 0; i < m_OnBackSysteCall.Count; i++)
            {
                //如果两个委托的目标、方法和调用列表都相等，则即使这两个委托的类型不相同，它们也被视为相等。
                if (m_OnBackSysteCall[i].Equals(callback))
                {
                    return;
                }
            }
            m_OnBackSysteCall.Add(callback);
        }
        private long GenKey(int poolType, int index)
        {
            return ((long)poolType << 32) + index;
        }
        public void InitAssemblyClient()
        {
            Dictionary<Type, List<string>> methods = AssemblyManager.Instance.GetAllTypeMethods<ParamListenChangeAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in methods)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    ListenBackSysteCall(method);
                }
            }
            methods = AssemblyManager.Instance.GetAllTypeMethods<ParamChangeAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in methods)
            {
                var sysAttr = value.Key.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;

                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);

                    var methodAttrs = method.GetCustomAttributes<ParamChangeAttribute>(false);
                    foreach (var attr in methodAttrs)
                    {
                        OnParamChanged callback = Delegate.CreateDelegate(typeof(OnParamChanged), method) as OnParamChanged;
                        long key = GenKey(attr.PoolType, attr.Index);
                        if (!m_AllCallbacks.TryGetValue(key, out List<OnParamChanged> Value))
                        {
                            m_AllCallbacks.Add(key, new List<OnParamChanged>());
                        }
                        m_AllCallbacks[key].Add(callback);
                    }
                }
            }
            methods = AssemblyManager.Instance.GetAllTypeMethods<ParamIncrementChangeAttribute>();
            foreach (KeyValuePair<Type, List<string>> value in methods)
            {
                for (int i = 0; i < value.Value.Count; i++)
                {
                    MethodInfo method = value.Key.GetMethod(value.Value[i]);
                    ParamIncrementChangeAttribute item = method.GetCustomAttribute<ParamIncrementChangeAttribute>();
                    if(item == null) continue;
                    try
                    {
                        switch (item.type)
                        {
                            case EParamIncrementType.EList:
                                {
                                    Type actionGenericType = typeof(Action<,,,>);
                                    Type actionType = actionGenericType.MakeGenericType(new Type[]
                                        { typeof(PoolEntity), typeof(ListDicSyncOperateType), typeof(int), item.dataType }); //replace
                                    Type callbackGenericType = typeof(ListParamIncrement<>);
                                    Type callbackType = callbackGenericType.MakeGenericType(new Type[] { item.dataType });
                                    var cb = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) });

                                    long key = GenKey(item.PoolType, item.Index);
                                    if (!m_AllIncrementCallbacks.ContainsKey(key))
                                    {
                                        m_AllIncrementCallbacks.Add(key, cb as IParamIncrement);
                                    }
                                }
                                break;
                            case EParamIncrementType.EDictionary:
                                {
                                    Type actionGenericType = typeof(Action<,,,>);
                                    Type actionType = actionGenericType.MakeGenericType(new Type[]
                                        { typeof(PoolEntity), typeof(ListDicSyncOperateType), typeof(long), item.dataType }); //replace
                                    Type callbackGenericType = typeof(DicParamIncrement<>);
                                    Type callbackType = callbackGenericType.MakeGenericType(new Type[] { item.dataType });
                                    var cb = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) });

                                    long key = GenKey(item.PoolType, item.Index);
                                    if (!m_AllIncrementCallbacks.ContainsKey(key))
                                    {
                                        m_AllIncrementCallbacks.Add(key, cb as IParamIncrement);
                                    }
                                }
                                break;
                            case EParamIncrementType.EQueue:
                                {
                                    Type actionGenericType = typeof(Action<,,,>);
                                    Type actionType = actionGenericType.MakeGenericType(new Type[]
                                        { typeof(PoolEntity), typeof(ListDicSyncOperateType), typeof(long), item.dataType }); //replace
                                    Type callbackGenericType = typeof(QueueParamIncrement<>);
                                    Type callbackType = callbackGenericType.MakeGenericType(new Type[] { item.dataType });
                                    var cb = Activator.CreateInstance(callbackType, new object[] { Delegate.CreateDelegate(actionType, method) });

                                    long key = GenKey(item.PoolType, item.Index);
                                    if (!m_AllIncrementCallbacks.ContainsKey(key))
                                    {
                                        m_AllIncrementCallbacks.Add(key, cb as IParamIncrement);
                                    }

                                }
                                break;
                        }
                    }
                    catch (Exception e)
                    {
                        Log.Error($"item.PoolType ： {item.PoolType} ， item.Index : {item.Index}  " + e.Message);
                    }
                }
            }
        }
        public void InitAssembly()
        {
          
            List<Type> allTypes = AssemblyManager.Instance.GetAllTypes();
            if (allTypes.Count <= 0)
            {
                throw new FrameworkException("Assembly Init is Empty!");
            }

            foreach (Type type in allTypes)
            {
                var sysAttr = type.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                Type compType = sysAttr.CompType;
                var methods = type.GetMethods();
                foreach (var method in methods)
                {
                    var methodCallEvent = method.GetCustomAttribute<ParamListenChangeAttribute>(false);
                    if (methodCallEvent != null)
                    {
                        ListenBackSysteCall(method);
                    }

                    var methodAttrs = method.GetCustomAttributes<ParamChangeAttribute>(false);
                    foreach (var attr in methodAttrs)
                    {
                        try
                        {
                            OnParamChanged callback = Delegate.CreateDelegate(typeof(OnParamChanged), method) as OnParamChanged;
                            long key = GenKey(attr.PoolType, attr.Index);
                            if (!m_AllCallbacks.TryGetValue(key, out List<OnParamChanged> value))
                            {
                                m_AllCallbacks.Add(key, new List<OnParamChanged>());
                            }

                            m_AllCallbacks[key].Add(callback);
                        }
                        catch (Exception e)
                        {
                            Log.Error($"attr : {attr} , method : {method} \n" + e );
                            throw;
                        }
                    }
                    InitIncrementCallBacks(method);
                }
            }
        }
        public void AddAssembly(Assembly assembly)
        {
            if (assembly == null) return;
            Type[] allTypes = assembly.GetTypes();
            foreach (Type type in allTypes)
            {
                var sysAttr = type.GetCustomAttribute<ComponentSystemAttribute>();
                if (sysAttr == null)
                    continue;
                Type compType = sysAttr.CompType;
                var methods = type.GetMethods();
                foreach (var method in methods)
                {
                    var methodCallEvent = method.GetCustomAttribute<ParamListenChangeAttribute>();
                    if (methodCallEvent != null)
                    {
                        ListenBackSysteCall(method);
                    }
                    var methodAttrs = method.GetCustomAttributes<ParamChangeAttribute>();
                    if (methodAttrs == null)
                    {
                        continue;
                    }
                    foreach (var attr in methodAttrs)
                    {
                        OnParamChanged callback = Delegate.CreateDelegate(typeof(OnParamChanged), method) as OnParamChanged;
                        long key = GenKey(attr.PoolType, attr.Index);
                        if (!m_AllCallbacks.TryGetValue(key, out List<OnParamChanged> value))
                        {
                            m_AllCallbacks.Add(key, new List<OnParamChanged>());
                        }
                        for (int i = 0; i < m_AllCallbacks[key].Count; i++)
                        {
                            //如果两个委托的目标、方法和调用列表都相等，则即使这两个委托的类型不相同，它们也被视为相等。
                            if (m_AllCallbacks[key][i].Equals(callback))
                            {
                                m_AllCallbacks[key].RemoveAt(i);
                                break;
                            }
                        }
                        m_AllCallbacks[key].Add(callback);
                    }

                    InitIncrementCallBacks(method);
                }
            }
        }

        public void Call(int poolType, int index, PoolEntity poolEntity)
        {
            if (poolEntity != null && poolEntity.IsCallBackActive == false)
                return;
            if(m_OnBackSysteCall != null)
            {
                foreach (var callback in m_OnBackSysteCall)
                {
                    try
                    {
                        if (callback != null)
                        {
                            callback.Invoke(poolType,index,poolEntity);
                        }
                    }
                    catch (Exception e)
                    {
                        Log.Exception($"属性更新System回调异常,poolType={poolType},index={index},错误:{e.Message},StackTrace:\n{e.StackTrace}\n");
                    }
                
                }
            }

            long key = GenKey(poolType, index);
            if (!m_AllCallbacks.TryGetValue(key, out List<OnParamChanged> value))
                return;

            foreach(var callback in value)
            {
                try
                {
                    if(callback != null)
                    {
                        callback.Invoke(poolEntity);
                    }
                }
                catch (Exception e)
                {
                    Log.Exception($"属性更新回调异常,poolType={poolType},index={index},错误:{e.Message},StackTrace:\n{e.StackTrace}\n");
                }
             
            }
        }
        public void CallListChanged<T>(int poolType, int poolIndex,PoolEntity poolEntity, ListDicSyncOperateType eOp,int index, T data)
        {
            long cbKey = GenKey(poolType, poolIndex);
            if (!m_AllIncrementCallbacks.TryGetValue(cbKey, out IParamIncrement value))
                return;

            var callback = value as ListParamIncrement<T>;
            if (callback == null)
            {
                Log.Error($"poolType {poolType} poolIndex {poolIndex} callBack == null");
                return;
            }
            callback.Invoke(poolEntity,eOp,index, data);
        }
        public void CallDicChanged<T>(int poolType, int poolIndex,PoolEntity poolEntity, ListDicSyncOperateType eOp,long key, T data)
        {
            long cbKey = GenKey(poolType, poolIndex);
            if (!m_AllIncrementCallbacks.TryGetValue(cbKey, out IParamIncrement value))
                return;
            
            var callback = value as DicParamIncrement<T>;
            if (callback == null)
            {
                Log.Error($"poolType {poolType} poolIndex {poolIndex} callBack == null");
                return;
            }
            
            callback.Invoke(poolEntity,eOp,key, data);
        }
        public void CallQueueChanged<T>(int poolType, int poolIndex, PoolEntity poolEntity, ListDicSyncOperateType eOp, T data)
        {
            long cbKey = GenKey(poolType, poolIndex);
            if (!m_AllIncrementCallbacks.TryGetValue(cbKey, out IParamIncrement value))
                return;

            var callback = value as QueueParamIncrement<T>;
            if (callback == null)
            {
                Log.Error($"poolType {poolType} poolIndex {poolIndex} callBack == null");
                return;
            }

            callback.Invoke(poolEntity, eOp, data);
        }
    }
}