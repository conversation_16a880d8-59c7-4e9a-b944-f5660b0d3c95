#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableQuestionnaire
	{

		public static readonly string TName="Questionnaire.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 问卷名称 
		/// </summary> 
		public int Title {get; set;}
		/// <summary> 
		/// 问卷的URL 
		/// </summary> 
		public string Url {get; set;}
		/// <summary> 
		/// 问卷奖励邮件ID 
		/// </summary> 
		public int MailID {get; set;}
		/// <summary> 
		/// 问卷奖励ID 
		/// </summary> 
		public int DropGroupID {get; set;}
		/// <summary> 
		/// 触发条件类型（0：None 1:主线任务 2:创角时间） 
		/// </summary> 
		public int[] UnlockType {get; set;}
		/// <summary> 
		/// 触发参数 
		/// </summary> 
		public int[] Param {get; set;}
		#endregion

		public static TableQuestionnaire GetData(int ID)
		{
			return TableManager.QuestionnaireData.Get(ID);
		}

		public static List<TableQuestionnaire> GetAllData()
		{
			return TableManager.QuestionnaireData.GetAll();
		}

	}
	public sealed partial class TableQuestionnaireData
	{
		private Dictionary<int, TableQuestionnaire> dict = new Dictionary<int, TableQuestionnaire>();
		private List<TableQuestionnaire> dataList = new List<TableQuestionnaire>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableQuestionnaire.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableQuestionnaire>>(jsonContent);
			foreach (TableQuestionnaire config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableQuestionnaire Get(int id)
		{
			if (dict.TryGetValue(id, out TableQuestionnaire item))
				return item;
			return null;
		}

		public List<TableQuestionnaire> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
