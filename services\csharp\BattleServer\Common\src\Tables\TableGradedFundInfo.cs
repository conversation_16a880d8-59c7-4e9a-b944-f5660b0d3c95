#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGradedFundInfo
	{

		public static readonly string TName="GradedFundInfo.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 等级阶段 
		/// </summary> 
		public int LevelStage {get; set;}
		/// <summary> 
		/// 所属基金阶段 
		/// </summary> 
		public int GradedFundIdx {get; set;}
		/// <summary> 
		/// 免费道具id 
		/// </summary> 
		public int ItemId {get; set;}
		/// <summary> 
		/// 免费道具num 
		/// </summary> 
		public int ItemNum {get; set;}
		/// <summary> 
		/// 超值道具掉落id 
		/// </summary> 
		public int SuperDropGroupId {get; set;}
		#endregion

		public static TableGradedFundInfo GetData(int ID)
		{
			return TableManager.GradedFundInfoData.Get(ID);
		}

		public static List<TableGradedFundInfo> GetAllData()
		{
			return TableManager.GradedFundInfoData.GetAll();
		}

	}
	public sealed partial class TableGradedFundInfoData
	{
		private Dictionary<int, TableGradedFundInfo> dict = new Dictionary<int, TableGradedFundInfo>();
		private List<TableGradedFundInfo> dataList = new List<TableGradedFundInfo>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGradedFundInfo.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGradedFundInfo>>(jsonContent);
			foreach (TableGradedFundInfo config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGradedFundInfo Get(int id)
		{
			if (dict.TryGetValue(id, out TableGradedFundInfo item))
				return item;
			return null;
		}

		public List<TableGradedFundInfo> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
