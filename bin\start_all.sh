#!/bin/bash

# 设置当前目录
current_dir=$(dirname $(readlink -f $0))
cd ${current_dir}

# 启动服务器
nohup ./webserver --config=../configs/webserver.yaml --commconf=../configs/config.yaml > ./wlogs  2>&1 &
echo $! > webserver.pid
echo "Started webserver with PID $(cat webserver.pid)"

nohup ./gameserver --config=../configs/gameserver.yaml --commconf=../configs/config.yaml > ./glogs 2>&1 &
echo $! > gameserver.pid
echo "Started gameserver with PID $(cat gameserver.pid)"

nohup ./matchserver --config=../configs/matchserver.yaml --commconf=../configs/config.yaml > ./mlogs 2>&1 &
echo $! > matchserver.pid
echo "Started matchserver with PID $(cat matchserver.pid)"

nohup ./payserver --config=../configs/payserver.yaml --commconf=../configs/config.yaml > ./plogs 2>&1 &
echo $! > payserver.pid
echo "Started payserver with PID $(cat payserver.pid)"

nohup LD_LIBRARY_PATH=/home/<USER>/BattleServer > /dev/null 2>&1 &
echo $! > battleserver.pid
echo "Started battleserver with PID $(cat battleserver.pid)"

echo "All servers started successfully"