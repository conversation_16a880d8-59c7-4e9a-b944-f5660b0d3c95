/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableActivityCard struct {
	// ============= 变量定义 =============
	// 卡ID
	ID int32
	// 名称
	Name int32
	// 描述1
	Des1 int32
	// 描述2
	Des2 int32
	// 时间类型
	TimeType int32
	// 持续时间
	Duration int32
	// 价格
	Price int32
	// 一次性奖励
	DisposableDrop [][]int32
	// 每日奖励
	DailyDrop [][]int32
	// 多次购买时间是否叠加
	StackType int32
	// 每日X次胜利多得Y奖杯
	Value1 []int32
	// 连胜额外X奖杯
	Value2 int32
	// 阵容槽位提升
	Value3 int32
	// PVP忍术卷轴钻石刷新次数提升
	Value4 int32
	// 每日商店折扣
	Value5 int32
	// 每日商店增加免费刷新次数
	Value6 int32
	// 战斗加速
	Value7 int32
	// 免广告
	Value8 int32
	// 无尽增加次数
	Value9 int32
	// 试炼增加次数
	Value10 int32
}




// TableActivityCardData 表格
type TableActivityCardData struct {
	file    string
	dataMap map[int32]*TableActivityCard
	Data    []*TableActivityCard
	md5     string
}

// load 加载
func (tb *TableActivityCardData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableActivityCard{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableActivityCard, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivityCard)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableActivityCard, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableActivityCardData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableActivityCard{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableActivityCard, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivityCard)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableActivityCardData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableActivityCardData) GetById(id int32) *TableActivityCard {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableActivityCardData) GetCloneById(id int32) *TableActivityCard {
	v := tb.dataMap[id]
	out := &TableActivityCard{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableActivityCardData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableActivityCardData) Foreach(call func(*TableActivityCard) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableActivityCardData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableActivityCardData) Clone() ITable {
	ntb := &TableActivityCardData{
		file:    tb.file,
		dataMap: make(map[int32]*TableActivityCard),
		Data:    make([]*TableActivityCard, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableActivityCard{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
