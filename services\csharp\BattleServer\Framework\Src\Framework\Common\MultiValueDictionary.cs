﻿//*********************************************************
// Framework 
// Author:  Jasen
// Date  :  2022-10-26
//*********************************************************

using System.Collections;
using System.Collections.Generic;

namespace Aurora.Framework
{
    //使用LinkedList实现多值字典表，用于频繁增删
    public class MultiDictionaryList<TKey, TValue> : Dictionary<TKey, LinkedList<TValue>>, IReference
    {

        public MultiDictionaryList()
            : base()
        {
        }

        public void AddFirst(TKey key, TValue value)
        {
            LinkedList<TValue> container = null;
            if (!this.TryGetValue(key, out container))
            {
                container = new LinkedList<TValue>();
                base.Add(key, container);
            }
            if (!container.Contains(value))
            {
                container.AddFirst(value);
            }
        }

        public void AddLast(TKey key, TValue value)
        {
            LinkedList<TValue> container = null;
            if (!this.TryGetValue(key, out container))
            {
                container = new LinkedList<TValue>();
                base.Add(key, container);
            }
            if (!container.Contains(value))
            {
                container.AddLast(value);
            }
        }

        public bool ContainsValue(TKey key, TValue value)
        {
            bool toReturn = false;
            LinkedList<TValue> values = null;
            if (this.TryGetValue(key, out values))
            {
                toReturn = values.Contains(value);
            }
            return toReturn;
        }

        public void Remove(TKey key, TValue value)
        {
            LinkedList<TValue> container = null;
            if (this.TryGetValue(key, out container))
            {
                container.Remove(value);
                if (container.Count <= 0)
                {
                    this.Remove(key);
                }
            }
        }

        public void Merge(MultiDictionaryList<TKey, TValue> toMerge)
        {
            if (toMerge == null)
            {
                return;
            }

            foreach (KeyValuePair<TKey, LinkedList<TValue>> pair in toMerge)
            {
                foreach (TValue value in pair.Value)
                {
                    this.AddLast(pair.Key, value);
                }
            }
        }

        public LinkedList<TValue> GetValues(TKey key)
        {
            LinkedList<TValue> toReturn = null;
            if (!base.TryGetValue(key, out toReturn))
            {
                return null;
            }
            return toReturn;
        }
    }

    //使用HashSet实现多值字典表，用于频繁查找
    public class MultiDictionarySet<TKey, TValue> : Dictionary<TKey, HashSet<TValue>>, IReference
    {

        public MultiDictionarySet()
            : base()
        {
        }

        public void Add(TKey key, TValue value)
        {
            HashSet<TValue> container = null;
            if (!this.TryGetValue(key, out container))
            {
                container = new HashSet<TValue>();
                base.Add(key, container);
            }
            container.Add(value);
        }

        public bool ContainsValue(TKey key, TValue value)
        {
            bool toReturn = false;
            HashSet<TValue> values = null;
            if (this.TryGetValue(key, out values))
            {
                toReturn = values.Contains(value);
            }
            return toReturn;
        }

        public void Remove(TKey key, TValue value)
        {
            HashSet<TValue> container = null;
            if (this.TryGetValue(key, out container))
            {
                container.Remove(value);
                if (container.Count <= 0)
                {
                    this.Remove(key);
                }
            }
        }

        public void Merge(MultiDictionarySet<TKey, TValue> toMerge)
        {
            if (toMerge == null)
            {
                return;
            }

            foreach (KeyValuePair<TKey, HashSet<TValue>> pair in toMerge)
            {
                foreach (TValue value in pair.Value)
                {
                    this.Add(pair.Key, value);
                }
            }
        }

        public HashSet<TValue> GetValues(TKey key)
        {
            HashSet<TValue> toReturn = null;
            if (!base.TryGetValue(key, out toReturn))
            {
                return null;
            }
            return toReturn;
        }
    }
}
