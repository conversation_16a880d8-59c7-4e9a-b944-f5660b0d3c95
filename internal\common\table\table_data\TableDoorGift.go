/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableDoorGift struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 礼包名称
	Name int32
	// 礼包图标
	Icon string
	// 礼包描述
	GiftDesc int32
	// 礼包类型 0.免费1.付费
	GiftType int32
	// 奖励ID
	DropGroupId int32
	// 支付表ID
	MidasItemId int32
}




// TableDoorGiftData 表格
type TableDoorGiftData struct {
	file    string
	dataMap map[int32]*TableDoorGift
	Data    []*TableDoorGift
	md5     string
}

// load 加载
func (tb *TableDoorGiftData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableDoorGift{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableDoorGift, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableDoorGift)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableDoorGift, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableDoorGiftData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableDoorGift{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableDoorGift, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableDoorGift)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableDoorGiftData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableDoorGiftData) GetById(id int32) *TableDoorGift {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableDoorGiftData) GetCloneById(id int32) *TableDoorGift {
	v := tb.dataMap[id]
	out := &TableDoorGift{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableDoorGiftData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableDoorGiftData) Foreach(call func(*TableDoorGift) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableDoorGiftData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableDoorGiftData) Clone() ITable {
	ntb := &TableDoorGiftData{
		file:    tb.file,
		dataMap: make(map[int32]*TableDoorGift),
		Data:    make([]*TableDoorGift, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableDoorGift{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
