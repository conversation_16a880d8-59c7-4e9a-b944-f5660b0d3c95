#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivitySevenDayTask
	{

		public static readonly string TName="ActivitySevenDayTask.json";

		#region 属性定义
		/// <summary> 
		/// 数据id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 每日任务 
		/// </summary> 
		public int[][] DailyTask {get; set;}
		/// <summary> 
		/// 进度宝箱 
		/// </summary> 
		public int CommonBoxReward {get; set;}
		/// <summary> 
		/// 补偿邮件标题 
		/// </summary> 
		public string MailTitleId {get; set;}
		/// <summary> 
		/// 补偿邮件内容 
		/// </summary> 
		public string MailContentId {get; set;}
		#endregion

		public static TableActivitySevenDayTask GetData(int ID)
		{
			return TableManager.ActivitySevenDayTaskData.Get(ID);
		}

		public static List<TableActivitySevenDayTask> GetAllData()
		{
			return TableManager.ActivitySevenDayTaskData.GetAll();
		}

	}
	public sealed partial class TableActivitySevenDayTaskData
	{
		private Dictionary<int, TableActivitySevenDayTask> dict = new Dictionary<int, TableActivitySevenDayTask>();
		private List<TableActivitySevenDayTask> dataList = new List<TableActivitySevenDayTask>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivitySevenDayTask.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivitySevenDayTask>>(jsonContent);
			foreach (TableActivitySevenDayTask config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivitySevenDayTask Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivitySevenDayTask item))
				return item;
			return null;
		}

		public List<TableActivitySevenDayTask> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
