#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableFCMTemplate
	{

		public static readonly string TName="FCMTemplate.json";

		#region 属性定义
		/// <summary> 
		/// 推送id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 推送标题（英文） 
		/// </summary> 
		public string title {get; set;}
		/// <summary> 
		/// 推送内容（英文） 
		/// </summary> 
		public string content {get; set;}
		#endregion

		public static TableFCMTemplate GetData(int ID)
		{
			return TableManager.FCMTemplateData.Get(ID);
		}

		public static List<TableFCMTemplate> GetAllData()
		{
			return TableManager.FCMTemplateData.GetAll();
		}

	}
	public sealed partial class TableFCMTemplateData
	{
		private Dictionary<int, TableFCMTemplate> dict = new Dictionary<int, TableFCMTemplate>();
		private List<TableFCMTemplate> dataList = new List<TableFCMTemplate>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableFCMTemplate.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableFCMTemplate>>(jsonContent);
			foreach (TableFCMTemplate config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableFCMTemplate Get(int id)
		{
			if (dict.TryGetValue(id, out TableFCMTemplate item))
				return item;
			return null;
		}

		public List<TableFCMTemplate> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
