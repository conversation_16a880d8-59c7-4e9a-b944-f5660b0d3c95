﻿
using BattleServer.Game.Core;
using BattleServer.Service;
using BattleServer.Nats;
using Game.Core;
using Aurora.Framework;

namespace BattleServer.Game
{
    public class StateBuffer : State
    {
        private float _waitTime = 0;

        private List<int> BufferRound = new List<int>();
        public StateBuffer(StateComponent stateComponent) : base(stateComponent)
        {

        }

        public override void OnInit()
        {
            base.OnInit();

            TablePlayMode playMode = TablePlayMode.GetData(1);
            for (int i = 0; i < playMode.BuffRound.Length; i++)
            {
                BufferRound.Add(playMode.BuffRound[i]);
            }
        }

        public override void OnEnter()
        {
            Log.Debug("[StateBuffer] OnEnter");

            Scene scene = _stateComponent.GetScene();
            int curRound = scene.GetRound();

            if (!IsBufferRound(curRound))
            {
                _stateComponent.ChangeState(StateType.UpdateHero);
                return;
            }

            foreach (var player in scene.GetPlayers().Values)
            {
                player.RandomBuffers();
                if (player.IsRobot)
                {
                    player.OnSelectBuffer(player.GetOptionsBuffer()[0]);
                }
            }

            // 当有选选完buff回合时，所有玩家都需要选择buff,选完buff 后跟据玩家选择的buff进行英雄更新，然后进入准备阶段
            foreach (var battle in _stateComponent.GetScene().GetBattles())
            {
                battle.OnRoundStart(false);
            }

            TablePlayMode playMode = TablePlayMode.GetData(1);
            _waitTime = playMode.BuffDuration * 1000 + 5;
            //_waitTime = 5 * 1000;
        }

        public override void OnUpdate(float deltaTime)
        {
            if (IsAllPlayerSelectBuffer())
            {
                _stateComponent.ChangeState(StateType.Preparation);
            }
            else
            {
                _waitTime -= deltaTime;
                if (_waitTime <= 0)
                {
                    Log.Debug("[StateBuffer] Wait time out, changing to Preparation state.");
                    foreach (var player in _stateComponent.GetScene().GetPlayers().Values)
                    {
                        if (!player.IsSelectBuffer())
                        {
                            // 如果玩家没有选择buff，强制选择第一个buff
                            var buffers = player.GetOptionsBuffer();
                            _stateComponent.GetScene().OnPlayerSelectBuffer(player.Info.Uid, buffers[0]);
                        }
                    }
                    _stateComponent.ChangeState(StateType.Preparation);
                }
                else
                {
                    //Log.Debug($"[StateBuffer] Waiting for players to select buffers. Remaining time: {_waitTime / 1000} seconds.");
                }
            }
        }

        private bool IsBufferRound(int round)
        {
            for (int i = 0; i < BufferRound.Count; i++)
            {
                if (BufferRound[i] == round)
                {
                    return true;
                }
            }

            return false;
        }

        private bool IsAllPlayerSelectBuffer()
        {
            var players = _stateComponent.GetScene().GetPlayers();
            foreach (var player in players.Values)
            {
                if (!player.IsSelectBuffer())
                {
                    return false;
                }
            }
            return true;
        }
    }
}
