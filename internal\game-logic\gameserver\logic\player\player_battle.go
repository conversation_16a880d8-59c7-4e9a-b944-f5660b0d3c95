package player

import (
	"context"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/pkg/log"
	"strconv"
)

type Battle struct {
	player         *Player
	battleServerId string
	battleId       int64
}

func NewBattle(player *Player) *Battle {
	return &Battle{player: player}
}

// GetBattleId 获取当前战斗ID
func (b *Battle) GetBattleId() int64 {
	return b.battleId
}

// GetBattleServerId 获取当前战斗服务器ID
func (b *Battle) GetBattleServerId() string {
	return b.battleServerId
}

// IsInBattle 检查玩家是否在战斗中
func (b *Battle) IsInBattle() bool {
	return b.battleServerId != "" && b.battleId != 0
}

// ForceClearBattleState 强制清理战斗状态 - 用于修复状态不一致的情况
func (b *Battle) ForceClearBattleState() {
	if b.battleServerId != "" || b.battleId != 0 {
		log.Error("force clearing inconsistent battle state",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId))

		b.battleServerId = ""
		b.battleId = 0

		log.Info("battle state force cleared", log.Kv("uid", b.player.Uid()))
	}
}

// InitDB 初始化模块数据
func (b *Battle) InitDB(db *dbstruct.UserDB) {

}

// OnCrossDay 实现Module接口的跨天方法
func (b *Battle) OnCrossDay(natural bool, nowUnix int64) {

}

func (b *Battle) Match(ctx context.Context, req *cs.CLMatchReq) *cs.LCMatchRsp {
	//return &cs.LCMatchRsp{}

	ret := &cs.LCMatchRsp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检测玩家是否已经在战斗中
	if b.IsInBattle() {
		log.Error("player already in battle, cannot start new match",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId))

		ret.Code = int32(error_code.ERROR_PARAMS)
		return ret
	}

	matchReq := &natsrpc.MatchRequest{
		Player: &public.PBBattlePlayerInfo{
			Uid:      b.player.Uid(),
			Name:     b.player.Name(),
			ServerId: strconv.Itoa(int(global.ServerId)),
			Throphy:  1,
			Kills:    0,
			WinCount: 0,
		},
		Team: &public.PBBattleTeamInfo{
			Heros: []*public.PBBattleHeroInfo{
				{Id: 401, Level: 10, AwakeLevel: 0, StarLevel: 1},
				{Id: 402, Level: 10, AwakeLevel: 0, StarLevel: 1},
				{Id: 403, Level: 10, AwakeLevel: 0, StarLevel: 1},
				{Id: 404, Level: 10, AwakeLevel: 0, StarLevel: 1},
				{Id: 405, Level: 10, AwakeLevel: 0, StarLevel: 1},
				{Id: 406, Level: 10, AwakeLevel: 0, StarLevel: 1},
			},
		},
	}

	//teamInfo := b.player.lineup.getCurrentLineup()
	//for _, value := range teamInfo.HeroIds {
	//	heroInfo := b.player.hero.GetHeroById(value)
	//
	//	pbHero := &public.PBBattleHeroInfo{Id: value, Level: heroInfo.HeroLevel}
	//	matchReq.Team.Heros = append(matchReq.Team.Heros, pbHero)
	//}

	resp, err := matchServiceClient.Match(ctx, matchReq)

	if err != nil {
		ret.Code = resp.Code
		log.Error("match request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	return ret
}

func (b *Battle) RoundBattleStart(ctx context.Context, req *cs.CLRoundBattleStartReq) *cs.LCRoundBattleStartResp {
	ret := &cs.LCRoundBattleStartResp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检查玩家是否在战斗中
	if !b.IsInBattle() {
		log.Error("player not in battle, cannot call EnterBattle",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId),
			log.Kv("battleServerId_empty", b.battleServerId == ""),
			log.Kv("battleId_zero", b.battleId == 0))

		// 如果状态不一致（有battleServerId但没有battleId），强制清理
		if b.battleServerId != "" && b.battleId == 0 {
			log.Error("detected inconsistent battle state, force clearing",
				log.Kv("uid", b.player.Uid()),
				log.Kv("battleServerId", b.battleServerId))
			b.ForceClearBattleState()
		}

		ret.Code = int32(error_code.ERROR_PARAMS)
		return ret
	}

	log.Info("calling EnterBattle RPC",
		log.Kv("uid", b.player.Uid()),
		log.Kv("battleServerId", b.battleServerId))

	matchReq := &natsrpc.EnterBattleReq{
		Uid: b.player.Uid(),
	}
	resp, err := battleServiceClient.EnterBattle(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("EnterBattle RPC failed",
			log.Err(err),
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId))
		return ret
	}

	if resp.Code < 0 {
		ret.Code = resp.Code
	}

	log.Info("EnterBattle RPC success",
		log.Kv("uid", b.player.Uid()),
		log.Kv("battleServerId", b.battleServerId),
		log.Kv("responseCode", resp.Code))

	return ret
}

// cleanupBattleOnLogout 玩家登出时清理战斗资源
func (b *Battle) cleanupBattleOnLogout() {
	log.Info("player logout, performing battle cleanup", log.Kv("uid", b.player.Uid()))
	b.performBattleCleanup()
}

func (b *Battle) SelectBuffer(ctx context.Context, req *cs.CLSelectBufferReq) *cs.LCSelectBufferResp {
	ret := &cs.LCSelectBufferResp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检查玩家是否在战斗中
	if !b.IsInBattle() {
		log.Error("player not in battle, cannot select buffer",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId))
		ret.Code = int32(error_code.ERROR_PARAMS)
		return ret
	}

	matchReq := &natsrpc.SelectBufferReq{
		Uid:      b.player.Uid(),
		BufferID: req.BufferId,
	}
	resp, err := battleServiceClient.SelectBuffer(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("select buffer request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	if resp.Code < 0 {
		ret.Code = resp.Code
	}

	// 传递新生成的英雄信息
	if resp.NewHeroes != nil {
		ret.NewHeroes = resp.NewHeroes
	}

	log.Debug("player select buffer", log.Kv("uid", b.player.Uid()), log.Kv("code", resp.Code),
		log.Kv("bufferId", req.BufferId), log.Kv("newHeroes", len(resp.NewHeroes)))

	return ret
}

func (b *Battle) MergeHero(ctx context.Context, req *cs.CLMergeReq) *cs.LCMergeRsp {
	ret := &cs.LCMergeRsp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检查玩家是否在战斗中
	if !b.IsInBattle() {
		log.Error("player not in battle, cannot merge hero",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId))
		ret.Code = int32(error_code.ERROR_PARAMS)
		return ret
	}

	matchReq := &natsrpc.MergeHeroReq{
		Uid:  b.player.Uid(),
		From: req.From,
		To:   req.To,
	}

	// 传递移动操作列表
	if req.Moves != nil {
		matchReq.Moves = req.Moves
	}

	resp, err := battleServiceClient.MergeHero(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("merge hero request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	if resp.Code < 0 {
		ret.Code = resp.Code
	}

	ret.From = resp.From
	ret.To = resp.To
	if resp.NewHeros != nil {
		ret.NewHeros = resp.NewHeros
	}
	log.Debug("player merge hero", log.Kv("uid", b.player.Uid()), log.Kv("code", resp.Code),
		log.Kv("from", resp.From), log.Kv("to", resp.To), log.Kv("newHero", ret.NewHeros))

	return ret
}

func (b *Battle) BattleReady(ctx context.Context, req *cs.CLReadyReq) *cs.LCReadyRsp {
	ret := &cs.LCReadyRsp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检查玩家是否在战斗中
	if !b.IsInBattle() {
		log.Error("player not in battle, cannot battle ready",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId))
		ret.Code = int32(error_code.ERROR_PARAMS)
		return ret
	}

	matchReq := &natsrpc.ReadyBattleReq{
		Uid: b.player.Uid(),
	}

	// 传递移动操作列表到BattleServer
	if req.Moves != nil {
		matchReq.Moves = req.Moves
		log.Info("BattleReady with moves", log.Kv("uid", b.player.Uid()), log.Kv("moveCount", len(req.Moves)))
	}

	resp, err := battleServiceClient.BattleReady(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("battle ready request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	log.Debug("player battle ready", log.Kv("uid", b.player.Uid()), log.Kv("code", resp.Code))

	return ret
}

func (b *Battle) RoundBattleEnd(ctx context.Context, req *cs.CLRoundBattleEndReq) *cs.LCRoundBattleEndResp {
	ret := &cs.LCRoundBattleEndResp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检查玩家是否在战斗中
	if !b.IsInBattle() {
		log.Error("player not in battle, cannot end round battle",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId))
		ret.Code = int32(error_code.ERROR_PARAMS)
		return ret
	}

	matchReq := &natsrpc.EndBattleReq{
		Uid: b.player.Uid(),
		Win: req.Win,
	}
	resp, err := battleServiceClient.EndBattle(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("round end request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	if resp.Code < 0 {
		ret.Code = resp.Code
	}

	log.Debug("player round battle end",
		log.Kv("uid", b.player.Uid()),
		log.Kv("code", resp.Code))
	return ret
}

// ProcessBattleEnd 处理战斗结束（由BattleServer通过RPC调用）
func (b *Battle) ProcessBattleEnd(req *natsrpc.BattleEndReq) {
	// 调用Trophy模块处理战斗结算
	notify := b.player.Trophy().ProcessBattleEnd(req.Rank, req.WinStreak, req.Heros)

	// 发送结算通知给客户端
	b.player.NotifyBattleEnd(notify)

	// 清理战斗状态
	b.battleServerId = ""
	b.battleId = 0

	log.Info("Battle end processed and notified",
		log.Kv("player_id", b.player.Uid()),
		log.Kv("rank", req.Rank),
		log.Kv("win_streak", req.WinStreak))
}

// ClaimAdReward 领取广告奖励
func (b *Battle) ClaimAdReward(ctx context.Context, req *cs.CLClaimAdRewardReq) *cs.LCClaimAdRewardRsp {
	// 调用Trophy模块处理广告奖励领取
	// TODO 接入广告SDK后,不通过客户端发送消息，直接回调Trophy模块
	return b.player.Trophy().ClaimAdReward(ctx, req)
}

// LeaveBattle 客户端主动离开战斗
func (b *Battle) LeaveBattle(ctx context.Context, req *cs.CLLeaveBattleReq) *cs.LCLeaveBattleRsp {
	ret := &cs.LCLeaveBattleRsp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检查玩家是否在战斗中
	if !b.IsInBattle() {
		log.Info("player not in battle, LeaveBattle request ignored",
			log.Kv("uid", b.player.Uid()))
		// 不在战斗中也返回成功，避免客户端重复请求
		return ret
	}

	log.Info("player actively leaving battle",
		log.Kv("uid", b.player.Uid()),
		log.Kv("battleServerId", b.battleServerId),
		log.Kv("battleId", b.battleId))

	// 调用与离线时相同的清理逻辑
	b.performBattleCleanup()

	log.Info("player leave battle completed", log.Kv("uid", b.player.Uid()))
	return ret
}

// performBattleCleanup 执行战斗清理逻辑（统一离线和主动离开的处理）
func (b *Battle) performBattleCleanup() {
	// 检查玩家是否在战斗中
	if b.battleServerId == "" && b.battleId == 0 {
		log.Debug("player not in battle, no cleanup needed", log.Kv("uid", b.player.Uid()))
		return
	}

	log.Info("cleaning up battle resources",
		log.Kv("uid", b.player.Uid()),
		log.Kv("battleServerId", b.battleServerId),
		log.Kv("battleId", b.battleId))

	// 调用BattleServer的LeaveBattle RPC接口
	ctx := context.Background()
	leaveBattleReq := &natsrpc.LeaveBattleReq{
		Uid: b.player.Uid(),
	}

	resp, err := battleServiceClient.LeaveBattle(ctx, leaveBattleReq, b.battleServerId)
	if err != nil {
		log.Error("LeaveBattle RPC failed",
			log.Err(err),
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId))
		// 即使RPC失败，也继续清理本地状态
	} else if resp.Code != 0 {
		log.Error("LeaveBattle RPC returned error code",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("responseCode", resp.Code))
		// 即使BattleServer返回错误，也继续清理本地状态
	} else {
		log.Info("LeaveBattle RPC completed successfully",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("responseCode", resp.Code))
	}

	// 清理本地状态
	b.battleServerId = ""
	b.battleId = 0

	log.Info("battle cleanup completed", log.Kv("uid", b.player.Uid()))
}
