﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aurora.Framework;

namespace BattleServer.Game
{
    public class StateWait : State
    {
        // 等待时间，单位秒
        private float _waitTime = 100 * 1000;
        public StateWait(StateComponent stateComponent) : base(stateComponent)
        {

        }

        public override void OnEnter()
        {
            Log.Debug("[StateWait] OnEnter");
            Log.Info("[StateWait] OnEnter");
            _waitTime = 10 * 1000;

            foreach (var player in _stateComponent.GetScene().GetPlayers().Values)
            {
                if (player.IsRobot)
                {
                    // 机器人自动进入战斗
                    player.OnEnterBattle();
                }
            }
        }

        public override void OnUpdate(float deltaTime)
        {
            if (IsAllPlayerEnter())
            {
                _stateComponent.ChangeState(StateType.Match);
            }
            else
            {
                _waitTime -= deltaTime;
                if (_waitTime <= 0)
                {
                    Log.Debug("[StateWait] Wait time out, changing to Match state.");
                    foreach (var player in _stateComponent.GetScene().GetPlayers().Values)
                    {
                        if (!player.IsEnterBattle())
                        {
                            // 如果玩家没有进入战斗，强制进入
                            _stateComponent.GetScene().OnPlayerEnter(player.Info.Uid);
                        }
                    }
                    _stateComponent.ChangeState(StateType.Match);
                }
                else
                {
                    //Log.Debug($"[StateWait] Waiting for players to enter battle. Remaining time: {_waitTime / 1000} seconds.");
                }
            }
        }

        private bool IsAllPlayerEnter()
        {
            // 检查所有玩家是否都已进入
            if (_stateComponent.GetScene().GetPlayers().Count < 2)
            {
                Log.Debug("[StateWait] Not enough players to start the match.");
                return false;
            }

            foreach (var player in _stateComponent.GetScene().GetPlayers().Values)
            {
                if (!player.IsEnterBattle())
                {
                    return false;
                }
            }
            Log.Debug("[StateWait] All players are enter battle.");
            return true;
        }
    }
}
