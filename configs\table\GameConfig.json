[{"ID": 1, "WaitTime": 15, "HeartBeatCheckTimer": 10, "OffLineCheckNum": 3, "BackgroudToLoginUI": 30, "WeakNetCheckInterval": 3, "ShowWeakNetworkLoading": 1, "MaxReconnecTimes": 6, "ReconnecIntervalTime": 10, "TryReconnect": 1, "ShowLoadingWaitTime": 3, "ZiDanAngleValue": 5, "PlayerBaseAttrId": 10001, "HurtContinueWhiteTime": 0.2, "HurtGradientTime": 0.1, "HurtWhiteColor": [1, 0, 0, 1], "HurtWhiteColorMaxA": 0.8, "HurtScaleValueF": 0.02, "HookMaxTime": 43200, "DropSceneStayTimeInt": [15, 30], "DropFlyPlayerTimeInt": 8, "DropFlyAroundTimeFloat": 0.45, "DropFlyAroundMinFMaxF": [1.2, 1.8], "PlayerToPointMinDis": 0.5, "EscapeRandomMaxAngle": 132, "SkillMinCDTimeValue": 5, "PlayerBornPositionXYZ": [-0.2, 0.48, -0.32], "PlayerFrontBornPositionXYZ": [-0.2, 0.48, -0.32], "DamageBoardMaxTime": 1.5, "StageBeginID": [1000001, 2000001, 3000001, 4001001, 5001001, 6001001, 7001001, 8001001, 9001001, 10001001, 11001001, 12001001, 13001001, 14001001], "HookExtraAwardRatio": ["4", "0", "0", "0", "0", "0", "0", "0", "0.5", "0.5", "0.5", "0.5"], "DamageDepthPositionZ": 0, "TargetPointPositionDriftX": 2.9, "SceneBloodGreyDelayChangeTime": 0.15, "SceneBloodGreyChangeSpeed": 0.03, "SevenDayTaskProgress": [10, 20, 30, 40, 50, 60, 70], "SevenDayTaskStageReward": [700010, 700011, 700012, 700013, 700014, 700015, 700016], "SevenDayTaskDuration": 30, "DropScenePlayerEffectId": 1045, "DropScenePlayerEffectInterval": 0.5, "MissionFingerVanish": 40102, "NoOperationEntryTime": 300, "FriendRasinReward": [530100, 530101, 530102, 530103, 530104, 530105, 530106, 530107, 530108, 530109], "UserAgreementURL": "https://cy.gaming.com/gamepower/useragreement.html", "StorageLimit": [6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 5, 3, 0], "MonthCardFirstDrop": 921000, "MonthCardDailyDrop": 921001, "PlayerInitHead": 1001, "PlayerSlamNumTimeArgsArr": [1, 0.5, 0.3, 0.2], "ChatBubbleShowTimeAndDisappearTime": [5, 3], "CashCouponThreshold": 5, "PrivateAgreementURL": "https://cy.gaming.com/gamepower/privacy.html", "IsOnlyOneDamage": [900001, 900002, 900004], "StartEndXScale": 2.2, "MonsterTargeEndZ": 4, "DamageFlutterArgs": [70, 4, 5], "DamageFlutWallScale": 0.5, "PowerHaveMaxNum": 30, "PowerAddTimeInterval": 600, "MainStagePowerCost": 5, "TeamUpPowerCost": 10, "RefreshWeatherHour": [3600, 7200, 10800, 14400, 18000, 21600, 25200, 28800, 32400, 36000, 36060, 36180, 36300, 36420, 36540, 36660, 36780, 36900, 37020, 37140, 37260, 37380, 37500, 37620, 37740, 37860, 37980, 38100, 38220, 38340, 38460, 38580, 38700, 38820, 38940, 39060, 39180, 39300, 39420, 39540, 39660, 39780, 39900, 40020, 40140, 40260, 40380, 40500, 40620, 40740, 40860, 40980, 41100, 41220, 41340, 41460, 41580, 41700, 41820, 41940, 42060, 42180, 42300, 42420, 42540, 42660, 42780, 42900, 43020, 43140, 43260, 43380, 43500, 43620, 43740, 43860, 43980, 44100, 44220, 44340, 44460, 44580, 44700, 44820, 44940, 45060, 45180, 45300, 45420, 45540, 45660, 45780, 45900, 46020, 46140, 46260, 46380, 46500, 46620, 46740, 46860, 46980, 47100, 47220, 47340, 47460, 47580, 47700, 47820, 47940, 48060, 48180, 48300, 48420, 48540, 48660, 48780, 48900, 49020, 49140, 49260, 49380, 49480, 49500, 49620, 49740, 49860, 49980, 50100, 50220, 50340, 50460, 50580, 50700, 50820, 50940, 51060, 51180, 51300, 51420, 51540, 51660, 51780, 51900, 52020, 52140, 52260, 52380, 52500, 52620, 52740, 52860, 52980, 53100, 53220, 53340, 53460, 53580, 53700, 53820, 53940, 54060, 54180, 54420, 54660, 54900, 55140, 55380, 55620, 55860, 56100, 56340, 56460, 56580, 56700, 56820, 56940, 57060, 57180, 57300, 57420, 57480, 57540, 57600, 57660, 57720, 57780, 57840, 57900, 57960, 58020, 58080, 58140, 58200, 58260, 58320, 58380, 58440, 58500, 58620, 58740, 58860, 58980, 59100, 59220, 59340, 59460, 59580, 59700, 59820, 59860, 59980, 60060, 60180, 60300, 60420, 60540, 60660, 60780, 60900, 64020, 61140, 61260, 61380, 61500, 61620, 64740, 61860, 61980, 62100, 62220, 62340, 62460, 62580, 62700, 62820, 62940, 63060, 63180, 63300, 63420, 63540, 63660, 63780, 63900, 64020, 64140, 64260, 64380, 64500, 64620, 64740, 64860, 64980, 65100, 65220, 65340, 65460, 65580, 65700, 65820, 65940, 66060, 66180, 66300, 66420, 66540, 66660, 66780, 66900, 67020, 67140, 67260, 67380, 67500, 67620, 67740, 67860, 67980, 68100, 68220, 68340, 68460, 68580, 68700, 68820, 68940, 69060, 69180, 69300, 69420, 69540, 69660, 69780, 69900, 70020, 70140, 70260, 70380, 70500, 70620, 70740, 70860, 70980, 71100, 71220, 71340, 71460, 71580, 71700, 71820, 71940, 72060, 72180, 72300, 72420, 72540, 72660, 72780, 72900, 73020, 73140, 73260, 73380, 73500, 73620, 73740, 73860, 73980, 74100, 74220, 74340, 74460, 74580, 74700, 74820, 74940, 75060, 75180, 75300, 75420, 75540, 75660, 75780, 75900, 76020, 76140, 76260, 76380, 76500, 76620, 76740, 76860, 76980, 77100, 77220, 77340, 77460, 77580, 77700, 77820, 77940, 78060, 78180, 78300, 78420, 78540, 78660, 78780, 78900, 79020, 79140, 79260, 79380, 79500, 82800, 86400], "PowerLimitNum": 999, "AttackSkillNum": 20, "AttackSkillChange": 150, "VanguardEnergyLimit": 1000, "VanguardSkillRecovery": "3,22", "CyberModelID": 150, "CyberBornPositionXYZ": [0.46, 1.4, 0], "AttackNumPlayIdleAni": 0, "MainCameraPosition": [0, 2.34, -2.82], "MainCameraRotation": [19, 0, 0], "MainCameraView": 60, "BattleCameraPosition": [0, 3.1, -4.76], "BattleCameraRotation": [14.5, 0, 0], "BattleCameraView": 40, "MonsterMoveSpeedParm": 45, "AimPositionZ": 60, "FloatingThreshold": [0.9, 0.9], "NotStopSoundTypes": [0, 8, 13, 15, 18, 19, 23, 24, 25, 26, 28, 29, 30, 31, 33, 34, 39], "ButtEnergy": 1, "ClickScreenInterval": 5, "PowerRefreshTime": [8, 14, 20], "PowerStorageLimit": 60, "PowerPeriodValidity": 30, "PowerNum": 15, "OptionalPlayer": [10001, 10002, 10003], "DrawingDropGroupId": 1120601, "DrawingTimes": [1, 2, 3, 5, 10], "DrawingMaxTimes": 3, "EliteWeight": [50, 30, 20, 10], "BOSSWeight": [50, 30, 20, 10], "FightRandomAdvertisementNum": 1, "NameNumInterval": [0, 99999], "NameMaxCharacter": 15, "SignatureMaxCharacter": 100, "PlayerStartNewGuildGroupId": 1001, "GlobalServerMailMaxNum": 300, "PushFullIntervalTimes": [18000, 18000], "CashCouponRatio": 300, "ActivityListSortConfig": [6, 3, 2, 8, 4, 5], "StageDailyAdCount": [2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0], "StageCostItemId": [0, 100002, 100003, 100004, 100005, 0, 100007, 100008, 100009, 0, 0, 111033, 111034, 0], "StageDailyGiveCount": [2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 5, 3, 0], "StageOneADGiveCount": 2, "WorldChatInterval": 5, "FriendInvitedReward": 300001, "GiftRecMax": 30, "GachaWatchMaxCount": 3, "GachaWatchInterTime": 600, "GachaDrawRates": [20, 15, 50, 50], "GachaDrawExp": 1, "GachaDrawCostDiamonds": [0, 500, 1500, 1500], "PetGachaWatchInterTime": 600, "PetGachaDrawExp": 1, "HallowsGachaAdvDrawMaxCount": 2, "HallowsGachaWatchMaxCount": 3, "GachaAdvDrawMaxCount": 50, "HallowsGachaDrawRates": [1, 1, 10, 10], "PlayerDefaultHeadFrame": 1001, "MailMaxNum": 50, "QuestionnaireEmail": 10007, "NewaccountEmail": 10005, "NextDayMailReward": 10006, "RechargeRatio": 300, "DailyTaskArr": [30001, 30002, 30003, 30004, 30005, 30006, 30007, 30008, 30009, 30010, 30011, 30012, 30013], "WeekTaskArr": [20000, 20001, 20002, 20003, 20004, 20005, 20006, 20007, 20008, 20009, 20010], "FriendMaxNumber": 50, "BlacklistMax": 20, "GiftItemNum": [3, 50], "PlayerNameMaxLength": 12, "GiftSendMax": 30, "GMSendMailMaxDayTime": 14, "CreatePlayerDefaultDiamond": 0, "CreatePlayerDefaultCoin": 0, "PlayerInitDress": 200001, "MainStagePassBounds": [5, 15, 20, 30, 40, 50], "MainStagePassAdds": [40, 32, 28, 17, 10, 1], "DailyCollectEmail": [10001, 10004], "WeeklyReceiveEmail": 10002, "GenderConfig": ["UD_Icon_gendersecret", "UD_Icon_gendermale", "UD_Icon_genderfamale"], "ChangeInforCost": 100, "SignatureChangeTime": 60, "GradeFund": 150001, "AllianceNameLen": 6, "AllianceNoticeLen": 20, "AllianceCreateExpend": [2, 1000], "WeekCardGiftId": [140001, 140002, 140003, 140004], "FriendApplyMaxCount": 100, "FriendMaxCount": 30, "FriendBlackMaxCount": 10, "MainRankMatchTime": [5, 10, 15, 20], "MainBattleDailyWinTimes": 10, "MainBattleDailyFailTimes": 3, "MainRankScoreInitial": 0, "TreasureGachaAdTimes": 2, "TreasureGachaProModify": [20000, 9000, 8000, 8000, 8000, 7000, 7000, 7000, 7000, 7000, 6000, 6000, 6000, 6000, 6000, 5000], "HeroLineUpNum": 6, "HeroLineUpId": [401, 402, 403, 404, 405, 406], "HeroLevelMax": 20, "HeroEvoMax": 5}]