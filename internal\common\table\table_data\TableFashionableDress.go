/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableFashionableDress struct {
	// ============= 变量定义 =============
	// Id
	ID int32
	// 类型1.活动 2.晋升称号
	Type int32
	// 品质
	Quality int32
	// 指定日期类型（0：开服时间戳 1：指定日期）
	ShowTimeType int32
	// 指定日期显示（1、指定固定日期2、开服多久开）分钟数或指定日期
	ShowTime string
	// 名字
	Name int32
	// 时装描述
	Des int32
	// 获取途径
	GetStr int32
	// 模型的名字
	ModelId int32
	// 拥有属性id_1
	OwnProIdId_1 int32
	// 拥有属性数值_1
	OwnProId_1 []int32
	// 拥有属性id_2
	OwnProIdId_2 int32
	// 拥有属性数值_2
	OwnProId_2 []int32
	// 星级的拥有属性ID
	StarOwnProIdId []int32
	// 星级的拥有属性数值_2
	StarOwnProId []int32
	// 穿戴后攻击技能
	SkillEx []int32
	// 穿戴后被动技能
	PassiveSkillEx [][]int32
	// 模型普攻是否走默认走到怪前面（0走默认的走到怪的对面，1 按照距离来）
	IsDefaultAiAtk int32
	// 道具icon
	Icon int32
	// 大图标icon1
	Icon1 int32
	// 头像表id
	HeadIconId int32
	// 排列顺序
	SortOrder int32
	// 碎片合成
	ChipCompound []int32
}




// TableFashionableDressData 表格
type TableFashionableDressData struct {
	file    string
	dataMap map[int32]*TableFashionableDress
	Data    []*TableFashionableDress
	md5     string
}

// load 加载
func (tb *TableFashionableDressData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableFashionableDress{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableFashionableDress, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableFashionableDress)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableFashionableDress, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableFashionableDressData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableFashionableDress{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableFashionableDress, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableFashionableDress)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableFashionableDressData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableFashionableDressData) GetById(id int32) *TableFashionableDress {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableFashionableDressData) GetCloneById(id int32) *TableFashionableDress {
	v := tb.dataMap[id]
	out := &TableFashionableDress{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableFashionableDressData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableFashionableDressData) Foreach(call func(*TableFashionableDress) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableFashionableDressData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableFashionableDressData) Clone() ITable {
	ntb := &TableFashionableDressData{
		file:    tb.file,
		dataMap: make(map[int32]*TableFashionableDress),
		Data:    make([]*TableFashionableDress, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableFashionableDress{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
