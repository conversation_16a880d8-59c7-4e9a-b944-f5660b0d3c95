#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuideLinesInfo
	{

		public static readonly string TName="GuideLinesInfo.json";

		#region 属性定义
		/// <summary> 
		/// 引导步骤ID，ID不是连续的，每一段连续的ID则是一个引导组 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 下一步新手的id    （-1自动结束    0不一定结束，取决于trigger表EndParameter列结束条件 
		/// </summary> 
		public int NextId {get; set;}
		/// <summary> 
		/// 动作1 
		/// </summary> 
		public int actionType1 {get; set;}
		/// <summary> 
		/// 动作1的参数 
		/// </summary> 
		public string actionParam1 {get; set;}
		/// <summary> 
		/// 动作1的数据参数:int数据 
		/// </summary> 
		public int[][] ActionParamInt1 {get; set;}
		/// <summary> 
		/// 动作1的数据参数：string数据 
		/// </summary> 
		public string[] ActionParamString1 {get; set;}
		/// <summary> 
		/// 点击按钮路径 
		/// </summary> 
		public string ButtonPath {get; set;}
		/// <summary> 
		/// 断线回退引导步骤ID（-1为直接完成当前引导组） 
		/// </summary> 
		public int GoBackId {get; set;}
		/// <summary> 
		/// 光圈类型（-1没有，1光圈1,2光圈2） 
		/// </summary> 
		public int IsHighlight {get; set;}
		/// <summary> 
		/// 箭头类型（-1没有1，类型1，类型2，类型3，类型4） 
		/// </summary> 
		public int ArrowType {get; set;}
		/// <summary> 
		/// 箭头方向向（1.右下2.左下3.左上4.右上） 
		/// </summary> 
		public int HandDir {get; set;}
		/// <summary> 
		/// 光圈缩放比例（真实值是除以100的小数） 
		/// </summary> 
		public int HighlightScaleX {get; set;}
		/// <summary> 
		/// 光圈缩放比例（真实值是除以100的小数） 
		/// </summary> 
		public int HighlightScaleY {get; set;}
		/// <summary> 
		/// 偏移X( 比例偏移  除以10000） 例如填写2500 为4分之一 
		/// </summary> 
		public int OffsetX {get; set;}
		/// <summary> 
		/// 偏移Y( 比例偏移  除以10000） 
		/// </summary> 
		public int OffsetY {get; set;}
		/// <summary> 
		/// 对话类型（-1没有，1类型1,2类型2） 
		/// </summary> 
		public int DialogType {get; set;}
		/// <summary> 
		/// 对话头像icon 
		/// </summary> 
		public string DialogHeadIcon {get; set;}
		/// <summary> 
		/// 对话id 字典id 
		/// </summary> 
		public int DialogID {get; set;}
		/// <summary> 
		/// 对话位置类型0中间1上面2下面 
		/// </summary> 
		public int DialogPosType {get; set;}
		/// <summary> 
		/// 对话偏移正数向上 负数向下 
		/// </summary> 
		public int DialogOffsetY {get; set;}
		/// <summary> 
		/// 龙岛建筑定位id（DragonBuildAreaId）只有龙岛的引导能用 
		/// </summary> 
		public int AreaId {get; set;}
		/// <summary> 
		/// 点击按钮类型（填写路径）1、点击全屏继续。3、路径有则为gameObject绝对路径 
		/// </summary> 
		public int ButtonPathType {get; set;}
		/// <summary> 
		/// 打开界面 
		/// </summary> 
		public int[] UIOpen {get; set;}
		/// <summary> 
		/// 校验ID 
		/// </summary> 
		public int CheckFunId {get; set;}
		/// <summary> 
		/// 是否显示半透蒙版，-1不显示 1显示  2、透明蒙版 
		/// </summary> 
		public int Masking {get; set;}
		/// <summary> 
		/// 滑动列表   1不可滑动    -1无 
		/// </summary> 
		public int ScrollView {get; set;}
		/// <summary> 
		/// 查找延迟(除以100)    
		/// </summary> 
		public int FindDelay {get; set;}
		/// <summary> 
		/// 战斗是否暂停 
		/// </summary> 
		public int IsBattleStop {get; set;}
		#endregion

		public static TableGuideLinesInfo GetData(int ID)
		{
			return TableManager.GuideLinesInfoData.Get(ID);
		}

		public static List<TableGuideLinesInfo> GetAllData()
		{
			return TableManager.GuideLinesInfoData.GetAll();
		}

	}
	public sealed partial class TableGuideLinesInfoData
	{
		private Dictionary<int, TableGuideLinesInfo> dict = new Dictionary<int, TableGuideLinesInfo>();
		private List<TableGuideLinesInfo> dataList = new List<TableGuideLinesInfo>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuideLinesInfo.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuideLinesInfo>>(jsonContent);
			foreach (TableGuideLinesInfo config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuideLinesInfo Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuideLinesInfo item))
				return item;
			return null;
		}

		public List<TableGuideLinesInfo> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
