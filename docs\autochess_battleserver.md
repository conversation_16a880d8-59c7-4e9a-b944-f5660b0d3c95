# 🎮 4人自走棋BattleServer技术文档

## 📋 目录

- [1. 系统架构概述](#1-系统架构概述)
- [2. 协议交互流程](#2-协议交互流程)
- [3. 英雄属性系统](#3-英雄属性系统)
- [4. 核心组件设计](#4-核心组件设计)
- [5. 时间配置与超时机制](#5-时间配置与超时机制)
- [6. 机器人托管系统](#6-机器人托管系统)
- [7. 房间匹配系统](#7-房间匹配系统)
- [8. 资源管理与清理](#8-资源管理与清理)
- [9. 玩家登出清理机制](#9-玩家登出清理机制)
- [10. 回合结算与消息推送机制](#10-回合结算与消息推送机制)
- [11. 战斗流程控制](#11-战斗流程控制)
- [12. 战斗日志系统](#12-战斗日志系统)
- [13. 性能指标](#13-性能指标)
- [14. 事件驱动架构](#14-事件驱动架构)
- [15. 多线程模型与线程安全](#15-多线程模型与线程安全)
- [16. 总结](#16-总结)

---

## 1. 系统架构概述

### 1.1 架构演进历程

BattleServer经历了从**单体加锁架构**到**无锁消息驱动架构**的重大重构，实现了性能和可维护性的显著提升。

### 1.2 原架构设计（已废弃）

#### 1.2.1 原架构特征
- **单线程模型**：所有组件运行在主线程中
- **加锁同步**：使用`lock`语句处理并发访问
- **状态混乱**：BattleService既处理路由又管理状态
- **紧耦合设计**：组件间直接依赖，难以测试和扩展

#### 1.2.2 原架构问题
- **性能瓶颈**：锁竞争导致性能下降
- **死锁风险**：复杂的锁依赖关系容易产生死锁
- **职责不清**：BattleService承担过多责任（995行代码）
- **难以维护**：状态管理分散，逻辑复杂

### 1.3 新架构设计（当前架构）

#### 1.3.1 架构核心理念
- **完全无锁**：消除所有`lock`语句，通过WorkUnit线程机制保证线程安全
- **职责分离**：路由、状态管理、业务逻辑完全分离
- **消息驱动**：通过Packet消息实现组件间通信
- **线程隔离**：每个AutoChessScene在独立WorkUnit线程中运行

#### 1.3.2 新架构层次结构

```text
┌─────────────────────────────────────────────────────────────┐
│                        主线程                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  BattleService  │  │  SceneManager   │  │AutoChessScene│ │
│  │   (纯路由层)     │  │  (状态管理)     │  │   Handler    │ │
│  │   355行代码      │  │                 │  │ (消息处理)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                        Packet消息传递
                                │
┌─────────────────────────────────────────────────────────────┐
│                    WorkUnit线程1                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                AutoChessScene                           │ │
│  │              (业务逻辑核心)                              │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │BattleStateManager│  │  PlayerManager  │              │ │
│  │  │   (状态机)       │  │  (玩家管理)     │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │BattleInstance    │  │OpponentPair     │              │ │
│  │  │Manager(实例管理) │  │Manager(配对)    │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │  BuffManager    │  │  CheckerBoard   │              │ │
│  │  │  (Buff管理)     │  │  (棋盘管理)     │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  │  ┌─────────────────────────────────────────────────────┐ │
│  │  │            BattleEventBus (事件总线)                │ │
│  │  └─────────────────────────────────────────────────────┘ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 1.3.3 核心组件职责

**主线程组件**：
- **BattleService**：纯路由层，只负责RPC到Packet消息的转换
- **SceneManager**：集中的状态管理和WorkUnit生命周期管理
- **AutoChessSceneHandler**：专门的Packet消息处理器

**WorkUnit线程组件**：
- **AutoChessScene**：业务逻辑核心，在独立线程中运行
- **各管理器组件**：状态机、玩家、实例、配对、Buff、棋盘管理

### 1.4 架构对比分析

#### 1.4.1 线程模型对比

| 方面 | 原架构 | 新架构 |
|------|--------|--------|
| **线程模型** | 单线程 + 锁同步 | 多线程 + 消息传递 |
| **并发处理** | 锁竞争，性能瓶颈 | 线程隔离，无锁竞争 |
| **死锁风险** | 高（复杂锁依赖） | 无（无锁设计） |
| **扩展性** | 差（单线程限制） | 优（独立线程） |

#### 1.4.2 职责分离对比

| 组件 | 原架构职责 | 新架构职责 |
|------|------------|------------|
| **BattleService** | 路由 + 状态管理 + 部分业务逻辑 | 纯路由（RPC→Packet转换） |
| **SceneManager** | 基础场景管理 | 集中状态管理 + WorkUnit管理 |
| **AutoChessScene** | 业务逻辑 + 状态管理混合 | 纯业务逻辑（WorkUnit线程） |

#### 1.4.3 通信机制对比

| 方面 | 原架构 | 新架构 |
|------|--------|--------|
| **组件通信** | 直接方法调用 | Packet消息传递 |
| **线程安全** | 锁机制保证 | 线程隔离保证 |
| **消息处理** | 同步阻塞 | 异步队列处理 |
| **故障隔离** | 单点故障影响全局 | 单个战斗故障不影响其他 |

### 1.5 新架构核心特性

- **完全无锁设计**：消除所有并发问题，提升性能
- **真正的4人自走棋**：支持同时进行的2场1v1战斗
- **独立线程隔离**：每个战斗在独立WorkUnit线程中运行
- **消息驱动通信**：通过Packet消息实现组件间解耦
- **集中状态管理**：SceneManager统一管理所有战斗状态
- **职责完全分离**：路由、状态管理、业务逻辑各司其职
- **配置驱动**：所有参数从PlayMode表读取
- **事件驱动架构**：使用BattleEventBus实现组件间解耦
- **统一坐标系统**：使用绝对GridID系统，简化服务器逻辑
- **延迟清理策略**：支持玩家重连和状态恢复

---

## 2. 协议交互流程

### 2.1 协议映射关系

#### 2.1.1 客户端 ↔ GameServer (CL/LC协议)

| 客户端请求 | GameServer响应 | 功能 | 对应RPC |
|-----------|---------------|------|---------|
| `CLMatchReq` | `LCMatchRsp` | 匹配请求 | → MatchServer |
| `CLRoundBattleStartReq` | `LCRoundBattleStartResp` | 回合开始/确认结算 | → `EnterBattleReq` |
| `CLReadyReq` | `LCReadyRsp` | 准备完成 | → `ReadyBattleReq` |
| `CLRoundBattleEndReq` | `LCRoundBattleEndResp` | 战斗结束 | → `EndBattleReq` |
| `CLSelectBufferReq` | `LCSelectBufferResp` | 选择Buff | → `SelectBufferReq` |
| `CLMergeReq` | `LCMergeRsp` | 英雄移动与合成 | → `MergeHeroReq` |
| `CLLeaveBattleReq` | `LCLeaveBattleRsp` | 主动离开战斗 | → `LeaveBattleReq` |

#### 2.1.2 英雄合成协议详细说明

**协议字段变更**：

- **客户端协议** (`LCMergeRsp`): 返回完整的棋盘格子信息
  - `NewHero` (PBCheckerBoard): 包含格子ID和英雄详细信息
  - 格子信息: `GridID` (int32) - 合成后英雄所在格子
  - 英雄信息: `Hero` (PBBattleHeroInfo) - 包含ID、等级、星级、觉醒等级

- **服务端协议** (`MergeHeroResp`): 返回英雄战斗信息
  - `NewHero` (PBBattleHeroInfo): 英雄的完整战斗属性
  - 包含字段: `Id`, `Level`, `StarLevel`, `AwakeLevel`

**英雄属性说明**：

- **英雄等级** (`Level`): **局外养成属性**，玩家在战斗外通过经验值提升
- **英雄星级** (`StarLevel`): **局内合成属性**，战斗中通过合成相同星级的相同英雄提升（1星→2星→3星）
- **觉醒等级** (`AwakeLevel`): **局外养成属性**，玩家在战斗外通过觉醒材料提升，**默认为0级**

**类型转换处理**：

```go
// GameServer中的类型转换逻辑
if resp.NewHero != nil {
    ret.NewHero = &public.PBCheckerBoard{
        GridID: resp.To, // 使用目标格子ID
        Hero:   resp.NewHero, // BattleServer返回的英雄信息
    }
}
```

#### 2.1.4 GameServer ↔ BattleServer (RPC协议)

| GameServer请求 | BattleServer响应 | 功能 | 触发时机 |
|---------------|-----------------|------|----------|
| `CreateBattleReq` | `CreateBattleResp` | 创建战斗 | 匹配成功后 |
| `EnterBattleReq` | `EnterBattleResp` | 进入战斗 | 客户端确认回合开始 |
| `ReadyBattleReq` | `ReadyBattleResp` | 准备完成 | 客户端准备完成 |
| `EndBattleReq` | `EndBattleResp` | 战斗结束 | 客户端确认战斗结束 |
| `SelectBufferReq` | `SelectBufferResp` | 选择Buff | 客户端选择Buff |
| `MergeHeroReq` | `MergeHeroResp` | 英雄移动与合成 | 客户端移动/合成英雄 |
| `LeaveBattleReq` | `LeaveBattleResp` | 离开战斗 | 玩家主动离开/断线 |
| `BattleStateChangeReq` | `BattleStateChangeResp` | 战斗状态变化（推送） | BattleServer主动推送 |

#### 2.1.5 GameServer ← BattleServer (主动推送)

| BattleServer推送       | GameServer接收           | 转发给客户端               | 触发时机     | 关键字段 |
| ---------------------- | ------------------------ | -------------------------- | ------------ | -------- |
| `RoundStartReq`        | `RoundStart()`           | `LCRoundStartNotify`       | 回合开始     | - |
| `RoundBattleStartReq`  | `RoundBattleStart()`     | `LCRoundBattleStartNotify` | 战斗开始     | - |
| `RoundBattleEndReq`    | `RoundBattleEnd()`       | `LCRoundBattleEndNotify`   | 单场战斗结束 | `isEnd`: 是否为最后回合 |
| `BattleEndReq`         | `BattleEnd()`            | `LCBattleEndNotify`        | 整场游戏结束 | - |
| `BattleStateChangeReq` | `OnBattleStateChanged()` | -                          | 状态变化     | - |

### 2.2 协议字段详细说明

#### 2.2.1 `RoundBattleEndReq` / `LCRoundBattleEndNotify`

```protobuf
message RoundBattleEndReq {
  uint64 uid = 1;     // 玩家ID
  uint64 winUid = 2;  // 获胜者ID
  uint64 loseUid = 3; // 失败者ID
  bool isEnd = 4;     // 是否为该玩家的最后回合（玩家被淘汰或整场比赛结束）
}

message LCRoundBattleEndNotify {
  uint64 winUid = 1;  // 获胜者ID
  uint64 loseUid = 2; // 失败者ID
  bool isEnd = 3;     // 是否为该玩家的最后回合（玩家被淘汰或整场比赛结束）
}
```

**isEnd字段判断逻辑**：

- **对于被淘汰的玩家**：`isEnd = true`（该玩家游戏结束，需要进行个人结算）
- **对于未被淘汰的玩家**：当剩余活跃玩家数量 ≤ 1 时，`isEnd = true`（整场游戏结束）
- **客户端处理**：收到 `isEnd = true` 时，应准备该玩家的结算界面

### 2.3 GameServer ↔ BattleServer (RPC调用)

| GameServer调用 | BattleServer处理 | 功能 |
|---------------|-----------------|------|
| `CreateBattleReq` | `CreateBattle()` | 创建战斗 |
| `EnterBattleReq` | `EnterBattle()` | 进入战斗/确认结算 |
| `ReadyBattleReq` | `UserRead()` | 准备完成 |
| `EndBattleReq` | `EndBattle()` | 战斗结束 |
| `SelectBufferReq` | `SelectBuffer()` | 选择Buff |
| `MergeHeroReq` | `MergeHero()` | 英雄移动与合成 |

### 2.4 完整对战流程

#### 2.4.1 阶段1：匹配和创建战斗

1. 客户端 → GameServer: `CLMatchReq`
2. GameServer → MatchServer: `MatchRequest`
3. MatchServer匹配4名玩家 → BattleServer: `CreateBattleReq`
4. BattleServer创建AutoChessScene，调用`InitBattleWithoutStart()`，状态: StateNone
5. MatchServer → GameServer: `MatchResultRequest` (通知匹配成功，**不含血量**)
6. **GameServer计算血量**：读取PlayMode表，根据最高奖杯数计算统一血量
7. GameServer → 客户端: `LCMatchSuccessNotify` (**包含正确血量**)

#### 2.4.2 阶段2：第一回合开始

8. 客户端 → GameServer: `CLRoundBattleStartReq` (4名玩家)
9. GameServer → BattleServer: `EnterBattleReq` (4次调用)
10. BattleServer收到所有玩家进入 → 调用`StartBattleStateMachine()`
11. 状态转换: StateNone → StateRoundStart (触发`OnBattleStateChanged`事件)
12. 事件处理: 调用`HandleRoundStart()` → 对手配对 → 创建战斗实例
13. **关键修复**: 如果是Buff回合，先调用`GenerateBuffOptionsForAllPlayers()`生成Buff选项
14. BattleServer → GameServer: `RoundStartReq` (推送给4名玩家，包含个性化Buff选项)
15. GameServer → 客户端: `LCRoundStartNotify` (棋盘信息+Buff选择)

#### 2.4.3 阶段3：准备阶段

16. 状态转换: StateRoundStart → StatePreparation (触发`HandlePreparationPhase()`)
17. 如果是Buff回合：Buff选项已在StateRoundStart阶段生成，机器人自动选择Buff
17. 客户端选择Buff → GameServer: CLSelectBufferReq
18. GameServer → BattleServer: SelectBufferReq → 调用`SelectBuff()`
19. Buff选择成功后自动生成新英雄并返回给客户端
20. 客户端操作英雄 → GameServer: CLMergeReq
21. GameServer → BattleServer: MergeHeroReq → 调用`MergeHero()`（包含坐标转换）
22. 客户端准备完成 → GameServer: CLReadyReq
23. GameServer → BattleServer: ReadyBattleReq → 调用`SetPlayerReady()`
24. BattleServer检测所有玩家准备完毕 → 触发`AllPlayersReadyEvent`
25. 状态转换: StatePreparation → StateBattleStarting

#### **阶段4：战斗开始**

26. 状态转换: StateBattleStarting → StateBattleInProgress
27. 事件处理: 应用Buff效果 → 推送战斗开始通知
28. BattleServer → GameServer: RoundBattleStartReq (推送给4名玩家)
29. GameServer → 客户端: LCRoundBattleStartNotify (双方阵容信息)
30. 机器人自动处理：机器人vs机器人立即结算，机器人vs真人等待真人操作

#### **阶段5：战斗结束与结算**

31. 客户端战斗完成 → GameServer: CLRoundBattleEndReq (胜负结果)
32. GameServer → BattleServer: EndBattleReq → 调用`HandleBattleEnd()`
33. 真人vs机器人：机器人自动发送相反结果
34. **分阶段结算机制**：
    - **单实例完成**：记录结果，等待其他实例完成
    - **所有实例完成**：调用`_battleStateManager.EndBattle()`，进入统一结算
35. 状态转换: StateBattleInProgress → StateRoundSettlement
36. **统一结算处理**：
    - 扣血并检查淘汰状态
    - **被淘汰玩家**：立即发送`RoundBattleEndReq`（`isEnd=true`）+ `BattleEndReq`
    - **存活玩家**：等待所有实例处理完成后发送`RoundBattleEndReq`（`isEnd=准确值`）
37. GameServer → 客户端: LCRoundBattleEndNotify (胜负结果，isEnd字段准确)

#### **阶段6：回合确认和下一回合**

38. **游戏结束检查**：如果只剩1名存活玩家
    - 冠军玩家收到`RoundBattleEndReq`（`isEnd=true`）
    - 所有剩余玩家收到`BattleEndReq`（最终排名）
    - 状态转换: StateRoundSettlement → StateGameOver
39. **游戏继续**：如果还有多名存活玩家
    - 客户端看完结算 → GameServer: CLRoundBattleStartReq (确认结算)
    - GameServer → BattleServer: EnterBattleReq → 调用`HandleRoundConfirmation()`
    - 真人确认时，所有机器人自动确认
    - BattleServer收到所有玩家确认 → 状态转换: StateRoundSettlement → StateRoundStart
40. 重复步骤12-39，直到游戏结束

#### 2.4.7 阶段7：游戏结束

35. 只剩1名玩家 → 状态转换: StateEliminationCheck → StateGameOver
36. BattleServer → GameServer: `BattleEndReq` (推送给所有玩家)
37. GameServer → 客户端: `LCBattleEndNotify` (最终排名)
38. **延迟清理机制**：BattleServer延迟5秒后清理资源，确保GameServer有足够时间处理通知

#### 2.4.8 游戏结束处理

游戏结束时采用延迟清理机制，确保GameServer有足够时间处理通知：

```csharp
// 延迟清理实现
private void ScheduleDelayedCleanup()
{
    Log.Info($"[{_logName}] Scheduling delayed cleanup in 5 seconds");
    var cleanupTimer = new System.Threading.Timer((_) =>
    {
        BattleService.CleanupBattle(BattleId);
    }, null, TimeSpan.FromSeconds(5), Timeout.InfiniteTimeSpan);
}
```

---

## 3. 英雄属性系统

### 3.1 核心概念区分

#### 3.1.1 阵容 (Lineup) vs 棋盘单位 (Board Units)

**阵容 (Lineup)**：

- **定义**：玩家拥有的英雄池，在`CreateBattle`时传入
- **特点**：
  - 每个英雄ID唯一，不重复
  - 包含Level和AwakeLevel（局外养成属性）
  - **不包含StarLevel**（星级是棋盘概念）
  - **不包含位置信息**（位置是棋盘概念）
- **数据结构**：`List<PBBattleHeroInfo>`（无重复ID）

**棋盘单位 (Board Units)**：

- **定义**：每回合从阵容中随机生成到棋盘上的英雄实例
- **特点**：
  - 可以有相同英雄ID的多个实例
  - 继承阵容中的Level和AwakeLevel
  - **新增StarLevel**（初始1星，可通过合成提升）
  - **有位置信息**（PBCheckerBoard.GridID）
- **数据结构**：`List<PBCheckerBoard>`（可重复ID，有位置）

### 3.2 属性分类

#### 3.2.1 局外养成属性（战斗前确定，战斗中不变）

- **英雄等级** (`Level`):
  - 玩家在战斗外通过消耗经验值提升
  - 影响英雄的基础属性（攻击力、生命值等）
  - **数据来源**：从玩家阵容中读取
  - **战斗中行为**：保持不变，所有同ID英雄共享相同等级

- **觉醒等级** (`AwakeLevel`):
  - 玩家在战斗外通过消耗觉醒材料提升
  - 解锁英雄的特殊技能或大幅提升属性
  - **默认为0级**（未觉醒状态）
  - **数据来源**：从玩家阵容中读取
  - **战斗中行为**：保持不变，所有同ID英雄共享相同觉醒等级

#### 3.2.2 局内战斗属性（战斗中动态变化）

- **英雄星级** (`StarLevel`):
  - **初始值**: 所有英雄生成到棋盘时均为1星
  - **提升方式**: 战斗中通过合成相同星级的相同英雄提升
  - **合成规则**: 两个相同英雄ID且相同星级的英雄合成 → 星级+1
  - **星级上限**: 由PlayMode配置表的StarLimit字段决定（默认为3星）
  - **属性影响**: 星级越高，英雄战斗力越强
  - **独立性**: 每个棋盘单位有独立的星级

### **数据流转机制**

#### **阶段1：CreateBattle - 阵容传入**
```
GameServer → BattleServer: 玩家阵容数据
- 数据格式: List<PBBattleHeroInfo>
- 每个英雄ID唯一（如：[剑士Lv.5觉醒0, 法师Lv.3觉醒1, 弓手Lv.7觉醒2]）
- 包含真实的Level和AwakeLevel
- 不包含StarLevel和位置信息
```

#### **阶段2：英雄生成 - 阵容到棋盘**
```
每回合开始: 从阵容随机生成棋盘单位
- 随机选择阵容中的英雄ID（可重复选择）
- 继承阵容中的Level和AwakeLevel
- StarLevel初始化为1星
- 分配棋盘位置（GridID）
- 结果: 可能生成多个相同ID的英雄实例
```

#### **阶段3：英雄合成 - 棋盘单位合成**
```csharp
// 英雄合成的核心判断逻辑
bool CanMerge(Entity source, Entity target)
{
    return source.ConfigId == target.ConfigId &&      // 相同英雄ID
           source.StarLevel == target.StarLevel &&    // 相同星级
           target.StarLevel < MaxStarLevel;           // 未达到星级上限
}
```

**合成结果**：
- **源英雄**: 被消耗，从棋盘移除
- **目标英雄**: 星级+1，保持在原位置
- **属性继承**: Level和AwakeLevel从阵容获取（保持不变），只有StarLevel提升

#### **合成示例**
```
阵容中: 剑士(Lv.5, 觉醒0)
棋盘上: 1星剑士(Lv.5, 觉醒0, 位置A) + 1星剑士(Lv.5, 觉醒0, 位置B)
合成后: 2星剑士(Lv.5, 觉醒0, 位置A) // Level和AwakeLevel来自阵容，StarLevel提升
```

### **实现架构**

#### **PlayerManager - 阵容数据管理**
```csharp
public class PlayerManager
{
    // 存储玩家完整阵容信息
    private Dictionary<long, List<PBBattleHeroInfo>> playerHeroInfos;

    // 根据英雄ID获取阵容中的英雄信息（Level和AwakeLevel）
    public PBBattleHeroInfo GetLineupHeroInfo(long playerId, int heroId)
    {
        var heroInfos = GetPlayerHeroInfos(playerId);
        return heroInfos.FirstOrDefault(h => h.Id == heroId);
    }
}
```

#### **棋盘单位创建 - 继承阵容属性**
```csharp
// 从阵容获取局外养成信息
var lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, entity.ConfigId);

new PBBattleHeroInfo
{
    Id = entity.ConfigId,
    Level = lineupHeroInfo?.Level ?? 1,        // 阵容中的真实等级
    StarLevel = 1,                             // 新生成英雄都是1星
    AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0 // 阵容中的真实觉醒等级
}
```

#### **英雄合成机制**
- **属性继承**：合成时从阵容获取Level和AwakeLevel，保持星级提升
- **数据一致性**：局外养成属性始终从阵容获取，局内战斗属性独立管理
- **位置管理**：通过GridID区分相同ID的不同棋盘单位

------

## 4. 核心组件设计

### 4.1 BattleService (RPC接口层)

- **职责**：处理来自GameServer的RPC请求
- **关键特性**：
  - 线程安全的战斗管理（ConcurrentDictionary）
  - 玩家进入状态跟踪机制
  - 超时处理和机器人托管
  - 战斗生命周期管理

### 4.2 AutoChessScene (4人自走棋核心)

- **职责**：管理完整的4人自走棋战斗流程
- **关键特性**：
  - **事件驱动架构**：使用BattleEventBus实现组件间通信
  - **依赖注入模式**：构造函数注入各个管理器组件
  - **状态机驱动**：通过BattleStateManager控制8个核心状态
  - **统一坐标系统**：使用绝对GridID系统，简化服务器逻辑
  - **战斗实例管理**：通过BattleInstanceManager统一管理2个并行1v1战斗
  - **玩家数据管理**：通过PlayerManager管理阵容、血量、状态等
  - **Buff系统集成**：完整的Buff选择、应用和管理流程
  - **机器人托管**：自动处理机器人的所有操作（准备、选择、战斗）

### 4.3 BattleStateManager (状态机)

- **职责**：管理8个核心战斗状态的转换
- **状态流程**：
  ```text
  StateNone → StateRoundStart → StatePreparation →
  StateBattleStarting → StateBattleInProgress →
  StateRoundSettlement → StateEliminationCheck → StateGameOver
  ```
- **关键特性**：
  - **事件驱动**：状态变化通过BattleEventBus发布事件
  - **超时机制**：每个状态都有对应的超时处理
  - **回合计数**：跟踪当前回合数和Buff选择回合

### 4.4 BattleInstanceManager (战斗实例管理)

- **职责**：管理4人自走棋中的2个并行1v1战斗
- **关键特性**：
  - **动态实例创建**：根据对手配对创建战斗实例
  - **独立棋盘管理**：每个实例有独立的CheckerBoard
  - **玩家映射管理**：维护玩家ID到战斗实例的映射关系
  - **英雄合成处理**：支持移动操作和合成操作的坐标转换
  - **战斗完成同步**：等待所有实例完成后进入结算
  - **PlayerIds分配一致性**：确保相同玩家在所有回合都有相同的PlayerIds顺序
- **依赖关系**：
  - **依赖PlayerManager**：获取玩家阵容中的英雄属性（Level、AwakeLevel）
  - **区域分配管理**：根据战斗实例位置动态分配GridID区域

#### **配对数据结构优化**

**问题背景**：
- 原有Dictionary<long, long>结构存在遍历顺序不确定性
- 同一对玩家在不同回合可能有不同的PlayerIds顺序
- 导致GridID区域分配不一致，引起数据恢复错误

**解决方案**：
- **数据结构改进**：从Dictionary改为List<(long player1, long player2)>
- **配对语义保持**：直接使用配对生成的原始顺序
- **消除排序逻辑**：移除不必要的ID排序，保持配对的业务语义

**核心改进**：
1. **OpponentPairManager**：返回有序的配对列表而非Dictionary
2. **PlayerManager**：接受配对列表并自动处理双向对手关系
3. **BattleInstanceManager**：直接使用配对顺序创建PlayerIds
4. **配对顺序语义**：
   - 第一回合：随机排序决定PlayerIds
   - 后续回合：血量排序决定PlayerIds（血量高的在PlayerIds[0]）

**修复效果**：
- **确定性分配**：List遍历顺序完全确定
- **语义清晰**：PlayerIds[0]和PlayerIds[1]有明确的配对含义
- **代码简化**：移除复杂的去重和排序逻辑

#### **坐标系统设计**

**新架构设计**：统一绝对GridID系统
- **数组下标0的玩家**：使用GridID 1-30（第1-5行）
- **数组下标1的玩家**：使用GridID 31-60（第6-10行）
- **服务器处理**：统一使用绝对GridID（1-60），无需坐标转换
- **数据推送**：按照战斗实例PlayerIds顺序返回，确保推送顺序一致性
- **客户端处理**：根据数组索引和自己的位置进行视角转换

**核心原则**：
1. **服务器简化**：BattleServer只处理绝对GridID，移除所有坐标转换逻辑
2. **数据一致性**：所有推送数据按照战斗实例中玩家顺序排列
3. **客户端负责**：客户端根据自己在数组中的位置进行视角转换
4. **区域分配**：基于玩家在战斗实例中的位置（PlayerIds[0]使用1-30，PlayerIds[1]使用31-60）

**GridID分配规则**：
```
数组下标0玩家区域：GridID 1-30  (第1-5行，每行6个格子)
数组下标1玩家区域：GridID 31-60 (第6-10行，每行6个格子)

格子编号规则：从下往上，从左往右递增
行1: 1,  2,  3,  4,  5,  6
行2: 7,  8,  9,  10, 11, 12
...
行5: 25, 26, 27, 28, 29, 30
行6: 31, 32, 33, 34, 35, 36
...
行10: 55, 56, 57, 58, 59, 60
```

**重要说明**：
- **配对语义驱动**：PlayerIds分配基于配对生成的业务逻辑
- **第一回合**：随机配对顺序决定PlayerIds分配
- **后续回合**：血量排序配对决定PlayerIds分配（血量高的在PlayerIds[0]）
- **区域切换**：当玩家在不同回合的配对位置发生变化时触发
- **智能恢复**：系统自动检测区域变化，使用镜像映射保持棋子的精确相对位置

#### **数据推送规则**

**统一的数据顺序**：
- **RoundStart和RoundBattleStart**：都按照战斗实例中PlayerIds的固定顺序推送
- **数组结构**：`[PlayerIds[0]的玩家数据, PlayerIds[1]的玩家数据]`
- **GridID分配**：数组下标0的玩家使用1-30，数组下标1的玩家使用31-60

**LCRoundStartNotify（回合开始推送）**：

**首回合规则**：
```json
{
  "PlayerBoards": [
    {"Uid": 自己的玩家ID, "BoardInfo": [自己新生成的英雄数据]},
    {"Uid": 对手的玩家ID, "BoardInfo": []} // 只有UID，无棋盘数据
  ]
}
```

**有Buff选择的回合**：
```json
{
  "PlayerBoards": [
    {"Uid": PlayerIds[0]的玩家ID, "BoardInfo": [上一回合开战时的英雄数据]},
    {"Uid": PlayerIds[1]的玩家ID, "BoardInfo": [上一回合开战时的英雄数据]}
  ]
}
```

**无Buff选择的后续回合**：
```json
{
  "PlayerBoards": [
    {"Uid": 自己的玩家ID, "BoardInfo": [自己新生成的英雄数据]},
    {"Uid": 对手的玩家ID, "BoardInfo": [对手上一回合开战时的英雄数据]}
  ]
}
```

**LCRoundBattleStartNotify（战斗开始推送）**：
```json
{
  "Team": [
    {"Player": PlayerIds[0]的玩家信息, "BoardInfo": [GridID 1-30的英雄数据]},
    {"Player": PlayerIds[1]的玩家信息, "BoardInfo": [GridID 31-60的英雄数据]}
  ]
}
```

**RoundStart推送规则详解**：

1. **首回合策略**：
   - 目的：防止玩家在准备阶段看到对手的英雄配置
   - 推送内容：自己的新英雄 + 对手的UID（无棋盘数据）
   - 效果：玩家只能看到自己的英雄，知道对手是谁但看不到对手阵容

2. **Buff选择回合策略**：
   - 目的：让玩家参考上一回合的战斗结果来选择Buff
   - 推送内容：双方上一回合开战时的棋盘数据（相同数据）
   - 效果：双方都能看到上一回合的完整对战阵容

3. **普通后续回合策略**：
   - 目的：显示自己的新英雄，同时让玩家了解对手的历史阵容
   - 推送内容：自己的新英雄 + 对手上一回合开战时的数据
   - 效果：看到自己的新配置，参考对手的历史阵容进行布阵

**客户端处理逻辑**：
- 客户端根据自己的UID在数组中的位置确定视角
- 如果自己是数组[0]，则[0]是自己，[1]是对手
- 如果自己是数组[1]，则[1]是自己，[0]是对手

**跨回合一致性**：
- 推送数据的数组顺序始终保持一致（按战斗实例PlayerIds顺序）
- 但同一玩家在不同回合可能出现在不同的数组位置（取决于对手配对）
- 系统会自动处理玩家棋子的区域切换，确保数据正确性

**重要：上一回合数据的区域转换**：
- 当推送上一回合开战时的棋盘数据时，系统会自动检测玩家的区域变化
- 如果玩家从PlayerIds[0]切换到PlayerIds[1]（或反之），会自动进行镜像转换
- 转换公式：`targetRow = 11 - sourceRow`, `targetCol = 7 - sourceCol`
- 确保推送的GridID与玩家当前应该使用的区域一致

#### **区域分配详细机制**

**ShouldUseMyArea方法逻辑**：
```csharp
private bool ShouldUseMyArea(long playerId)
{
    var instance = _instanceManager.GetInstanceByPlayerId(playerId);
    int playerIndex = instance.PlayerIds.IndexOf(playerId);
    return playerIndex == 0; // 第一个玩家使用1-30区域，第二个玩家使用31-60区域
}
```

**跨回合区域切换处理**：

#### **智能镜像映射恢复逻辑**

**核心原理**：
- 两个GridID区域（1-30和31-60）是完美的镜像关系
- 当玩家需要切换区域时，使用镜像映射保持棋子的相对位置

**镜像映射公式**：
- **坐标转换**：`targetRow = 11 - sourceRow`, `targetCol = 7 - sourceCol`
- **GridID映射**：`newGridId = 61 - oldGridId`
- **映射关系**：GridID 1↔60, 2↔59, 6↔55, 30↔31

**恢复逻辑判断**：
1. **区域判断**：比较玩家当前回合和上一回合的GridID区域
2. **映射选择**：
   - 区域未变化 → 直接映射（保持原坐标）
   - 区域发生变化 → 镜像映射（使用镜像公式）
3. **精确恢复**：每个棋子恢复到对应的精确位置，保持星级

**典型场景**：
- **直接映射**：玩家在相同PlayerIds位置，棋子位置不变
- **镜像映射**：玩家从PlayerIds[0]切换到PlayerIds[1]，棋子镜像到对称位置
- **配对变化**：根据血量排序和配对逻辑，玩家可能在不同回合使用不同区域

**关键特性**：
- **精确位置恢复**：每个棋子都恢复到精确的对应位置
- **完美镜像对称**：保持完美的空间对称关系
- **星级保持**：合成进度在区域切换时完全保持
- **战术布局保持**：棋子的相对排列和战术布局完全保持
- **智能判断**：只有在区域真正发生变化时才进行镜像转换

### **5. PlayerManager (玩家管理)**

- **职责**：管理4名玩家的数据和状态
- **核心功能**：
  - **阵容数据管理**：存储玩家完整英雄阵容信息（Level、AwakeLevel）
  - **英雄信息查询**：提供`GetLineupHeroInfo()`方法查询阵容中英雄属性
  - **玩家状态管理**：血量管理、准备状态、对手关系、淘汰状态
  - **棋盘数据管理**：管理玩家当前回合和上一回合的棋盘数据
  - **服务器ID管理**：存储玩家所在的GameServer ID用于消息路由
  - **基本信息管理**：玩家名称、等级、奖杯数等基本信息

#### **棋盘数据管理机制**

**PlayerBoardEntity数据结构**：
```csharp
public class PlayerBoardEntity
{
    public int ConfigId { get; set; }    // 英雄配置ID
    public int StarLevel { get; set; }   // 星级（合成进度）
    public int GridX { get; set; }       // 服务器行坐标
    public int GridY { get; set; }       // 服务器列坐标
}
```

**数据管理方法**：
- `SavePlayerBoardData()`: 保存当前回合棋盘数据
- `SavePlayerPreviousRoundBoardData()`: 保存上一回合结束时的棋盘数据
- `GetPlayerBoardData()`: 获取当前回合棋盘数据
- `GetPlayerPreviousRoundBoardData()`: 获取上一回合棋盘数据
- `GetLineupHeroInfo()`: 根据英雄ID获取阵容中的英雄信息
- `GetPlayerServerId()`: 获取玩家所在的GameServer ID

**关键特性**：
- **对手变动兼容**：棋盘数据跟随玩家，不受对手变动影响
- **回合间保持**：确保玩家的棋子在回合间正确保持位置和星级
- **历史数据支持**：支持对手查看上一回合的棋盘数据
- **跨服支持**：存储玩家服务器ID，支持跨服消息路由

### **6. CheckerBoard (棋盘管理)**

- **职责**：管理单个战斗实例的棋盘状态
- **核心功能**：
  - **实体生命周期管理**：创建、放置、移动、合成、移除实体
  - **格子状态管理**：10x6格子的占用状态和实体引用
  - **临时位管理**：支持英雄的临时存放位置
  - **坐标验证**：验证位置的有效性和边界检查
  - **实体查询**：根据玩家ID、位置等条件查询实体
- **关键方法**：
  - `CreateAndPlaceEntity()`: 创建并放置实体到指定位置
  - `MoveEntity()`: 移动实体到新位置
  - `TryMergeEntities()`: 尝试合成两个实体
  - `GetPlayerEntities()`: 获取指定玩家的所有实体
  - `CleanupOrphanedEntities()`: 清理孤立的实体引用

### **7. BuffManager (Buff管理)**

- **职责**：管理战斗中的Buff系统
- **核心功能**：
  - **Buff选项生成**：为玩家生成个性化的Buff选择
  - **Buff选择处理**：处理玩家的Buff选择操作
  - **Buff效果应用**：在战斗开始时应用选中的Buff
  - **随机选择支持**：超时时自动随机选择Buff

### **8. OpponentPairManager (对手配对)**

- **职责**：实现策划文档的对手配对规则
- **功能**：随机配对、血量排序、避重机制、淘汰处理

#### **淘汰机制处理**

根据策划文档要求，实现不同淘汰人数下的配对策略：

**0人淘汰**：正常4人配对
- 第1回合：随机配对
- 后续回合：按血量排序配对，避免连续对战同一对手

**1人淘汰**：保持4人匹配队列，被淘汰者填充匹配位置
```csharp
// 活跃玩家：[A(3血), B(2血), C(1血)]
// 淘汰玩家：[D(0血)]
// 配对结果：A vs B (正常对战), C vs D (C与AI代替的D进行正常战斗)
```

**2人淘汰**：移出匹配队列，剩余2人对战
```csharp
// 活跃玩家：[A(2血), B(1血)]
// 配对结果：A vs B (最终决战)
```

**3人淘汰**：游戏结束
```csharp
// 活跃玩家：[A(1血)]
// 结果：A获胜，游戏结束
```

#### **结算逻辑优化**

针对不同配对类型的结算处理：

**真人 vs 被淘汰AI**：
- 只等待真人玩家的EndBattleReq
- 直接相信真人的结算结果
- 被淘汰AI不发送随机结果，避免冲突

**机器人 vs 被淘汰AI**：
- 立即进行随机结算
- 双方都由AI控制，快速完成战斗

**真人 vs 正常机器人**：
- 等待真人结算后，机器人自动发送相反结果
- 保持原有的结算验证逻辑

**超时处理**：
- 所有情况都使用随机结算，保持逻辑一致性
- 不给真人玩家特殊保护，确保公平性

---

## 5. 时间配置与超时机制

### **配置来源**

从`PlayMode.json`配置文件读取：
```json
{
  "PreDuration": 60,    // 准备阶段时长(秒)
  "BattleDuration": 60,   // 战斗阶段时长(秒)
  "BuffDuration": 20,   // Buff选择时长(秒)
  "SpeedUpTime": 10,    // 战斗加速开始时间(秒)
  "BuffRound": [2,4,6],   // 提供Buff的回合数
  "PlayerHP": [[0,999,3],[1000,100000,4]] // 奖杯段位对应血量
}
```

### **超时类型总览**

#### **战斗前超时**
| 超时类型 | 时间 | 触发条件 | 处理逻辑 | 机器人差异 |
|---------|------|----------|----------|-----------|
| **匹配超时** | 10秒 | 匹配服务器等待 | 自动匹配机器人 | 机器人立即填充 |
| **进入战斗超时** | 10秒 | 等待玩家EnterBattle | 强制所有玩家进入 | 机器人立即进入，不等超时 |

#### **战斗中超时**
| 超时类型 | 配置时间 | 实际时间 | 触发条件 | 处理逻辑 | 机器人差异 |
|---------|----------|----------|----------|----------|-----------|
| **Buff选择超时** | 20秒 | 25秒 | BuffDuration计时器 | 强制随机选择buff | 机器人立即选择 |
| **准备阶段超时** | 60秒 | 65秒 | PreDuration计时器 | 强制所有玩家准备 | 机器人立即准备 |
| **战斗阶段超时** | 60秒 | 65秒 | BattleDuration计时器 | 强制结束战斗 | 机器人vs机器人立即结算 |
| **战斗加速** | 10秒后 | 10秒后 | 战斗进行中计时 | 客户端3倍速播放 | 无差异 |
| **结算确认超时** | 5秒 | 5秒 | RoundSettlement计时器 | 强制确认结算 | 机器人立即确认 |

### **5秒缓冲时间机制**

为了同步服务器与客户端的动画时间，所有主要阶段都添加了5秒缓冲时间：

- **PreDuration**: 配置60秒 → 实际65秒（60 + 5秒缓冲）
- **BattleDuration**: 配置60秒 → 实际65秒（60 + 5秒缓冲）
- **BuffDuration**: 配置20秒 → 实际25秒（20 + 5秒缓冲）

这确保客户端收到通知后有足够时间播放动画，服务器不会过早触发超时操作。

### **双重超时机制**

系统实现了两层超时处理架构：

```
准备阶段 (65秒)
├── Buff选择超时 (25秒) → BuffSelectionTimeoutEvent
└── 准备阶段超时 (65秒) → BattleTimeoutEvent
```

#### **事件驱动处理**
- **Buff选择独立超时**: `BuffSelectionTimeoutEvent` → 强制随机选择buff
- **阶段整体超时**: `BattleTimeoutEvent` → 根据状态分别处理

### **各阶段超时详细分析**

#### **StatePreparation (准备阶段)**
**时间**: 65秒 (60秒配置 + 5秒缓冲)

**内部时序**:
1. **0-25秒**: Buff选择时间 (如果是buff回合)
2. **25-65秒**: 自由操作时间 (英雄合成、移动、准备)

**超时处理**:
- **25秒**: BuffSelectionTimeoutEvent → 强制未选择buff的玩家随机选择
- **65秒**: BattleTimeoutEvent → 强制未准备的玩家准备完毕

**机器人行为**:
- 进入准备阶段立即选择buff (如果有)
- 立即准备完毕，不等待超时

#### **StateBattleInProgress (战斗阶段)**
**时间**: 65秒 (60秒配置 + 5秒缓冲)

**内部时序**:
1. **0-10秒**: 正常战斗速度
2. **10秒后**: 战斗加速 (BattleAcceleratingEvent)
3. **65秒**: 强制超时

**超时处理**:
- **10秒**: BattleAcceleratingEvent → 客户端3倍速播放
- **65秒**: BattleTimeoutEvent → 强制结束所有未完成战斗

**机器人行为**:
- 机器人vs机器人: 立即随机结算
- 机器人vs真人: 等待真人操作或系统超时

#### **StateRoundSettlement (结算确认阶段)**
**时间**: 5秒 (合并了原来的StateEliminationCheck)

**超时处理**:
- **5秒**: BattleTimeoutEvent → 强制所有玩家确认结算

**机器人行为**:
- 立即确认结算，不等待超时

**状态合并说明**:
- 原来分为StateRoundSettlement + StateEliminationCheck
- 现在合并为StateRoundSettlement(5秒)，简化状态机
- 客户端有5秒时间展示结算动画，然后发送确认

---

## 6. 机器人托管系统

### **机器人识别**

- **真实玩家**：UID不以9开头
- **机器人**：UID以9开头

### **机器人与真人差异总结**

#### **操作时机差异**
| 阶段 | 真人 | 机器人 |
|------|------|--------|
| Buff选择 | 手动选择，可能超时 | 立即自动选择第一个 |
| 准备确认 | 手动点击准备 | 立即自动准备 |
| 战斗操作 | 手动发送结果 | vs机器人立即结算，vs真人等待 |
| 结算确认 | 手动确认 | 立即自动确认 |

#### **超时处理差异**
| 超时类型 | 真人处理 | 机器人处理 |
|----------|----------|-----------|
| Buff选择超时 | 强制随机选择 | 已提前选择，无影响 |
| 准备阶段超时 | 强制设为准备状态 | 已提前准备，无影响 |
| 战斗阶段超时 | 强制随机结算 | 已提前结算或等待真人 |
| 结算确认超时 | 强制确认 | 已提前确认，无影响 |

### **自动操作时机**

1. **StatePreparation进入时**：
   - 机器人立即选择Buff（如果是Buff回合）并生成英雄
   - 机器人自动准备（`AutoReadyBots()`）
2. **StateBattleInProgress进入时**：
   - 机器人vs机器人：立即随机结算
   - 机器人vs真人：等待真人操作，依赖系统超时机制
3. **StateEliminationCheck进入时**：机器人立即确认结算
4. **真人操作触发时**：真人确认时，所有机器人自动跟随确认
5. **Buff选择超时时**：机器人和真人都会被强制随机选择buff并生成英雄

### **机器人英雄生成时序**

```csharp
private void AutoSelectBuffForBots()
{
    foreach (var playerId in botPlayerIds)
    {
        var buffOptions = _buffManager.GetPlayerBuffOptions(playerId);
        if (buffOptions.Count > 0)
        {
            bool success = _buffManager.PlayerSelectBuff(playerId, selectedBuff);
            if (success)
            {
                // 机器人选择buff后立即生成英雄
                var newHeroes = GenerateHeroesForPlayer(playerId);
                Log.Info($"Generated {newHeroes.Count} new heroes for bot player {playerId}");
            }
        }
    }
}
```

这确保机器人在buff选择回合能正确显示新生成的英雄。

### **战斗结算规则**

#### **1. 真人 vs 机器人**：

```csharp
// 在HandleBattleEnd中自动处理
if (!playerId.ToString().StartsWith("9") && opponentId.ToString().StartsWith("9"))
{
    // 真实玩家发送了结果，机器人自动发送相反结果
    if (!_instanceEndBattleRequests[instanceId].Contains(opponentId))
    {
        _instanceEndBattleRequests[instanceId].Add(opponentId);
        Log.Info($"Auto EndBattle for bot {opponentId} vs real player {playerId}, bot result: {!win}");
    }
}
```

- **结果**：根据真实玩家的战斗结果决定
- **目的**：确保战斗结果的真实性和公平性

#### **2. 机器人 vs 机器人**：

```csharp
// 在AutoHandleBotBattles中立即处理
if (opponentId.ToString().StartsWith("9"))
{
    bool bot1Wins = new Random().Next(2) == 0;
    HandleBattleEnd((ulong)playerId, bot1Wins);
    HandleBattleEnd((ulong)opponentId, !bot1Wins);
}
```

- **结果**：**随机胜负**（50%概率）
- **目的**：保持游戏平衡性，立即结算避免等待

#### **3. 超时强制结算**：

// 超时强制结算：根据减员数量或随机判定

if (player1Id == realPlayerId || player2Id == realPlayerId)

{

  // 有真实玩家参与的战斗超时，随机判定

  winnerId = playerIds[new Random().Next(2)];

}

else

{

  // 两个机器人对战，随机选择获胜者

  winnerId = playerIds[new Random().Next(2)];

}

---

## 7. MatchServer房间匹配系统

### 7.1 房间匹配架构

#### 7.1.1 核心设计理念
- **房间优先**：所有玩家通过房间匹配系统进行匹配，不再使用单人队列
- **动态范围扩展**：基于等待时间动态扩大匹配范围，提高匹配成功率
- **混合战斗支持**：支持真人玩家与机器人的组合战斗
- **超时保障**：房间超时后自动填充机器人确保游戏流畅进行

#### 7.1.2 房间数据结构
```go
type BattleRoom struct {
    RoomId       string        // 房间ID（雪花算法生成）
    CreatorId    uint64        // 创建者ID
    CreatorScore int32         // 创建者奖杯数（房间分数基准）
    Players      []*PlayerInfo // 房间内玩家列表
    CreateTime   int64         // 房间创建时间
    Status       RoomStatus    // 房间状态（匹配中/已满员/战斗中）
    MaxPlayers   int           // 最大玩家数（固定4人）
}
```

### 7.2 匹配流程

#### 7.2.1 匹配入口与类型分流
1. **玩家请求匹配** → `RequestMatch()`
2. **根据奖杯数获取匹配类型** → `getMatchType(trophy)`
3. **按匹配类型分流处理**：
   - **测试类型（0）** → `handleDirectRobotMatch()` 延时3秒后创建机器人战斗
   - **PVE类型（1）** → `handleDirectRobotMatch()` 延时3秒后创建机器人战斗
   - **PVP类型（2,3,4）** → 进入房间匹配流程

#### 7.2.2 真实玩家房间匹配流程
1. **尝试房间匹配** → `tryRoomMatch()`
   - 查找适合的房间 → `findSuitableRoom()`
   - 加入现有房间 OR 创建新房间
2. **持续主动匹配** → `tryActiveRoomMatching()` (仅PVP类型)
   - 根据等待时间动态扩大浮动范围
   - 主动寻找可合并的房间
   - 实现房间间玩家流动
3. **房间满员** → 立即开始战斗
4. **房间超时** → 直接填充机器人开始战斗

#### 7.2.2 浮动范围匹配机制
- **配置来源**：使用 `MainRank.Area` 配置：`[[-10,10],[-20,20],[-40,40]]`
- **动态扩展**：基于等待时间动态扩展范围
- **时间配置**：通过 `table.GetMainRankMatchTime()` 获取时间数组（如：`[5,10,15,20]`）
- **范围选择**：根据等待时间选择对应的浮动范围级别

### 7.3 BattleServer对手匹配机制（战斗内配对）

#### 7.3.1 第一回合 - 随机配对
- **配对逻辑**：随机排序后按顺序配对
- **PlayerIds分配**：随机排序中较前的玩家在PlayerIds[0]
- **配对结果**：生成有序的配对列表 `List<(player1, player2)>`

#### 7.3.2 后续回合 - 血量排序配对
- **排序规则**：按血量降序排列（血量高的排在前面）
- **配对逻辑**：No.1 vs No.2, No.3 vs No.4
- **PlayerIds分配**：血量高的玩家在PlayerIds[0]，血量低的在PlayerIds[1]
- **稳定性保证**：血量相同时按玩家ID排序保持稳定性

#### 7.3.3 避重机制
- **重复检测**：检查是否与上回合对手重复
- **配对调整**：尝试与其他玩家配对避免重复
- **兜底策略**：如果无法避免则保持原配对
### 7.4 匹配时间配置

#### 7.4.1 时间配置获取
```go
// 从配置表获取匹配时间数组
matchTimes := table.GetMainRankMatchTime() // 返回 [5,10,15,20] 等时间数组
```

#### 7.4.2 浮动范围选择逻辑
```go
func (m *NormalMatch) getFloatingRange(trophy int32, waitTime int64) []int32 {
    // 从MainRank表获取Area配置
    mainRankTable := table.GetTable().TableMainRank
    var area [][]int32
    var bestScoreRank int32 = -1

    // 找到对应的段位配置
    mainRankTable.Foreach(func(rank *table_data.TableMainRank) bool {
        if trophy >= rank.ScoreRank && rank.ScoreRank > bestScoreRank {
            area = rank.Area
            bestScoreRank = rank.ScoreRank
        }
        return false // 继续遍历
    })

    // 获取匹配时间配置数组 [5,10,15,20]
    matchTimes := table.GetMainRankMatchTime()

    // 根据等待时间确定范围级别
    timeIndex := 0
    for i, timeLimit := range matchTimes {
        if waitTime >= int64(timeLimit) {
            timeIndex = i + 1
        }
    }

    // 确保索引不越界
    if timeIndex >= len(area) {
        timeIndex = len(area) - 1
    }

    return area[timeIndex] // 返回对应级别的浮动范围
}
```

### 7.5 BattleServer配对数据结构（战斗内）

#### 7.5.1 数据流转
1. **OpponentPairManager**：生成有序配对列表 `List<(player1, player2)>`
2. **PlayerManager**：设置双向对手关系
3. **BattleInstanceManager**：按配对顺序创建PlayerIds
4. **AutoChessScene**：使用配对信息进行战斗管理

#### 7.5.2 淘汰处理策略
- **0人淘汰**：正常4人配对
- **1人淘汰**：保持4人队列，被淘汰者填充匹配位置
- **2人淘汰**：移出匹配队列，剩余2人对战
- **3人淘汰**：游戏结束

### 7.6 配对示例

#### 7.6.1 房间匹配示例
**玩家A匹配流程**：
- 奖杯数：1500，等待时间：0秒
- 浮动范围：[-10,10]，匹配范围：1490-1510
- 查找结果：无适合房间 → 创建新房间

**玩家B匹配流程**：
- 奖杯数：1505，等待时间：8秒
- 浮动范围：[-10,10]，匹配范围：1495-1515
- 查找结果：找到玩家A的房间 → 加入房间

#### 7.6.2 战斗内配对流程
**第1回合**：随机配对
- 随机排序：[玩家A, 玩家B, 玩家C, 玩家D]
- 配对结果：(A, B), (C, D)
- PlayerIds分配：A在[0], B在[1]; C在[0], D在[1]

**第2回合**：血量排序配对
- 血量排序：[玩家C(3血), 玩家A(2血), 玩家D(2血), 玩家B(1血)]
- 配对结果：(C, A), (D, B)
- PlayerIds分配：C在[0], A在[1]; D在[0], B在[1]

#### 7.6.3 淘汰处理示例
**第4回合**：1人淘汰后的配对
- 活跃玩家：[A(3血), B(2血), C(1血)]，淘汰玩家：[D(0血)]
- 配对结果：(A, B), (C, D)
- 战斗类型：A vs B (正常对战), C vs D (真人 vs 已淘汰玩家)

**第5回合**：2人淘汰后的配对
- 活跃玩家：[A(3血), B(1血)]，淘汰玩家：[C(0血), D(0血)]
- 配对结果：(A, B)
- 战斗类型：A vs B (最终决战)

### 7.7 技术实现细节

#### 7.7.1 房间ID生成
- **雪花算法**：使用 `uuid.Generate()` 生成唯一房间ID
- **格式**：`fmt.Sprintf("room_%d", uuid.Generate())`
- **示例**：`room_1234567890123456789`
- **初始化**：在matchserver启动时调用 `uuid.Init(nodeId)` 初始化生成器

#### 7.7.2 房间索引管理
```go
type NormalMatch struct {
    rooms        map[string]*BattleRoom          // 房间ID -> 房间实例
    roomsByScore map[int32][]*BattleRoom        // 奖杯数 -> 房间列表（用于快速查找）
}
```

#### 7.7.3 匹配类型处理
- **测试类型（trophy=0）**：10秒超时，填充3个机器人
- **PVE类型**：10秒超时，填充机器人
- **实时PVP类型**：60秒超时，优先匹配真实玩家
- **复合类型**：60秒超时，支持混合战斗

#### 7.7.4 超时处理机制
**处理流程**：
```
Run() -> processRoomMatching() -> checkRoomTimeouts() -> handleRoomTimeout()
```

**差异化超时处理**：
- **测试类型（0）**：直接填充机器人
- **PVE类型（1）**：填充机器人
- **异步PVP类型（2）**：填充机器人
- **实时PVP类型（3）**：优先尝试真实玩家房间合并，失败后填充机器人
- **复合类型（4）**：类似实时PVP处理

**房间合并逻辑**：
- 查找其他等待中的真实玩家房间
- 检查分数差异（允许±100分差异）
- 将玩家从候选房间移动到目标房间
- 自动清理空房间

#### 7.7.5 机器人生成逻辑
- **UID生成**：90000000000 + 随机偏移量（避免重复）
- **属性复制**：复制真实玩家的基础属性
- **名称标识**：添加"AI_"前缀标识机器人
- **深拷贝**：确保不影响原始玩家数据

### 7.8 性能优化与算法复杂度

#### 7.8.1 数据结构优化
- **房间ID类型**：从`string`优化为`uint64`，提高map查找性能
- **双重索引**：`rooms`主索引 + `roomsByScore`分数索引，加速查找

#### 7.8.2 算法时间复杂度分析

**房间查找** (`findSuitableRoom`):
- **当前实现**：O(S × R)，其中S为分数范围，R为该分数下的房间数
- **最坏情况**：O(n)，其中n为总房间数
- **优化建议**：可考虑使用跳表或B+树进一步优化

**房间创建** (`createNewRoom`):
- **时间复杂度**：O(1)
- **操作**：直接插入到map和slice中

**房间移除** (`cleanupRoom`):
- **时间复杂度**：O(R)，其中R为该分数下的房间数
- **操作**：需要遍历slice找到对应房间并移除

**玩家移除** (`removePlayerFromRooms`):
- **当前实现**：O(n × p)，其中n为房间数，p为平均每房间玩家数
- **最坏情况**：O(4n) = O(n)
- **优化建议**：可添加玩家到房间的反向索引

#### 7.8.3 匹配类型优化
- **精确分流**：根据奖杯数获取匹配类型，避免不必要的处理逻辑
  - 测试类型(0)和PVE类型(1)：延时3秒后创建机器人战斗，跳过房间系统
  - PVP类型(2,3,4)：进入房间匹配系统
- **主动匹配机制**：仅PVP类型会持续主动寻找可合并的房间
- **动态浮动范围**：严格按照配置表的浮动范围，根据等待时间自动扩大
- **房间合并逻辑**：支持将不同房间的玩家合并到一起
- **双向匹配检查**：确保两个房间都在对方的浮动范围内
- **超时处理**：PVP类型超时后直接填充机器人，不再尝试额外匹配
- **匹配频率**：1秒tick间隔，符合业内标准，平衡响应性和性能

#### 7.8.4 匹配时间配置
- **配置驱动**：使用`table.GetMainRankMatchTime()`获取时间数组
- **动态范围**：根据配置的时间节点动态扩展匹配范围
- **兜底机制**：配置为空时使用默认10秒间隔

---

## 8. 资源管理与清理

### **资源清理层次**

#### **1. 回合切换时**：

#### **回合开始处理流程**

**核心步骤**：
1. **数据保存**：保存所有玩家的棋盘数据（当前数据和历史数据）
2. **对手配对**：根据血量排序和避重机制进行配对
3. **实例创建**：创建新的战斗实例
4. **数据恢复**：在新实例中恢复玩家的棋盘数据
5. **清理优化**：清理孤立实体和临时位，重置准备状态

**关键特性**：
- **数据跟随玩家**：棋盘数据跟随玩家而不是战斗实例
- **对手变动兼容**：支持每回合对手变化的数据恢复
- **智能镜像映射**：当玩家区域切换时自动进行镜像转换

#### **2. 战斗实例销毁**：

// BattleInstance.Dispose()

CheckerBoard?.Dispose();

// BattleInstanceManager.OnClear()

foreach (var instance in _instances.Values)

{

  instance.Dispose();

}

#### **3. 场景销毁**：

// AutoChessScene.Dispose()

_playerManager?.Dispose();

_checkerBoard?.Dispose();

_battleStateManager?.Dispose();

_buffManager?.Dispose();

#### **4. 服务层清理**：

// BattleService.CleanupBattle()

_battles.TryRemove(battleId, out _);

_sceneManager.TryRemove(battleId, out var scene);

SceneManager.Instance.RemoveAutoChessScene(battleId);

#### **5. 玩家登出清理**：

// BattleService.CleanupBattleByPlayerId()

if (_playerToBattle.TryGetValue(playerId, out long battleId))

{

  var scene = _sceneManager.GetValueOrDefault(battleId);

  var realPlayerCount = scene.GetAllPlayerIds().Count(id => !id.ToString().StartsWith("9"));



  if (realPlayerCount <= 1)

  {

    // 只有一个真实玩家，清理整个战斗
    
    CleanupBattle(battleId);

  }

  else

  {

    // 多个真实玩家，只移除当前玩家映射
    
    _playerToBattle.TryRemove(playerId, out _);

  }

}

### **清理时机**

- **每回合开始**：清理上一回合跟踪数据
- **对手重新配对**：销毁旧战斗实例，创建新实例
- **游戏结束**：完整清理所有资源
- **玩家登出**：智能清理玩家相关资源
- **异常情况**：定时清理超时战斗

---

## 9. 玩家登出清理机制

### **延迟清理策略**

采用**延迟清理策略**解决PVE场景下断线重连和映射管理问题：

#### **核心原理**
- **LeaveBattle时不清理**：无法区分真正退出、短暂断线或杀端，保留映射支持重连
- **CreateBattle时强制清理**：创建新战斗明确表示放弃旧战斗，清理旧战斗资源
- **游戏结束时清理**：正常结束时清理所有玩家映射

#### **处理流程**

**1. 玩家离线/退出**
```
玩家断线/杀端 → LeaveBattle RPC → 仅记录日志，保留映射
```

**2. 创建新战斗时清理**
```
CreateBattle → 检测旧映射 → 清理策略选择：
├─ PVE场景(1真人) → 清理整个旧战斗
└─ 多人场景 → 仅处理该玩家离开，其他玩家继续
```

**3. 游戏正常结束**
```
GameOver → 清理所有玩家映射 → 允许新匹配
```

### **清理策略详解**

#### **PVE场景处理**
- **旧战斗清理**：玩家创建新战斗时，旧的PVE战斗被完全清理
- **断线重连**：LeaveBattle不清理映射，支持玩家重连继续游戏
- **避免冲突**：新旧战斗不会产生映射冲突或幽灵通知

#### **多人场景处理**
- **部分清理**：只处理离开玩家，其他真实玩家战斗继续
- **状态同步**：通知场景处理玩家离开，更新游戏状态
- **资源保护**：不影响其他玩家的游戏体验

### **技术实现**

#### **映射管理**
- **_playerToBattle**：维护玩家ID到战斗ID的映射关系
- **线程安全**：使用ConcurrentDictionary确保并发安全
- **生命周期**：CreateBattle时建立，CreateBattle/GameOver时清理

#### **状态同步**
- **GameServer状态**：battleServerId、battleId字段标识玩家战斗状态
- **BattleServer状态**：_battles、_sceneManager管理战斗和场景实例
- **一致性保证**：通过延迟清理避免状态不一致问题

### **战斗操作状态检测**

**检测机制**：所有战斗相关操作都进行状态验证，确保玩家在战斗中

**覆盖操作**：
- RoundBattleStart - 进入战斗检测
- SelectBuffer - 选择Buff检测
- MergeHero - 合并英雄检测
- BattleReady - 战斗准备检测
- RoundBattleEnd - 回合结束检测

### **错误处理**

#### **网络异常处理**
```go
err := battleServiceClient.LeaveBattle(ctx, leaveBattleReq, b.battleServerId)
if err != nil {
    log.Error("LeaveBattle RPC failed", log.Err(err))
    // 即使RPC失败，也继续清理本地状态
}
// 清理本地状态
b.battleServerId = ""
b.battleId = 0
```

#### **参数验证**
```csharp
if (uid <= 0)
{
    Log.Warning($"Invalid player ID in LeaveBattle request: {uid}");
    return new LeaveBattleResp { Code = -2 };
}
```

#### **ReadyBattle错误码处理**
- **Code = 0**：成功
- **Code = -1**：玩家不在战斗中（通过延迟清理策略解决映射丢失问题）
- **Code = -2**：参数错误或系统异常

#### **延迟清理策略解决的问题**
- **PVE断线重连**：LeaveBattle时保留映射，支持玩家重连继续游戏
- **映射丢失**：避免第二回合ReadyBattle返回-1的问题
- **资源清理**：CreateBattle时清理旧战斗，避免资源泄漏和幽灵通知
- **状态一致性**：通过明确的清理时机避免状态不一致

### **监控与日志**

#### **关键日志点**
```csharp
// CreateBattle时的延迟清理
Log.Info($"Player {playerId} creating new battle, cleaning up previous battle {oldBattleId}");
Log.Info($"Previous battle {oldBattleId} only has player {playerId}, cleaning up entire battle");

// LeaveBattle的保守处理
Log.Info($"Player {uid} left battle, mapping will be cleaned when creating new battle");

// ReadyBattle的映射检查
Log.Info($"Player {request.Uid} mapping exists before GetScene: {hasMappingBefore}");
Log.Error($"CRITICAL: No battle found for player {request.Uid} in ReadyBattle!");
```

#### **错误日志**
```csharp
Log.Error($"LeaveBattle error for player {request?.Uid}: {ex.Message}");
Log.Error($"LeaveBattle stack trace: {ex.StackTrace}");
```

### **测试场景**

#### **场景1：单玩家测试环境**
- **输入**：1真人 + 3机器人，真人登出
- **预期**：整个战斗被清理，所有资源释放
- **验证**：BattleService状态为空，场景实例被销毁

#### **场景2：多玩家生产环境**
- **输入**：4真人，1人登出
- **预期**：只移除登出玩家，其他3人继续战斗
- **验证**：战斗继续，只有玩家映射被移除

#### **场景3：网络异常**
- **输入**：RPC调用失败
- **预期**：GameServer本地状态仍被清理
- **验证**：玩家状态重置，不影响后续操作

### **性能优化**

#### **异步处理**
- 使用NATS Publish模式，避免阻塞
- 本地状态立即清理，不等待远程响应

#### **内存管理**
- 及时清理ConcurrentDictionary中的映射
- 智能判断是否需要清理整个战斗实例

#### **并发安全**
- 全面使用线程安全的数据结构
- 原子操作保证状态一致性

---

## 11. 战斗流程控制

### **状态转换等待机制**

#### **核心修复**：

- ✅ **StatePreparation → StateBattleStarting**：等待所有玩家`ReadyBattleReq`
- ✅ **StateBattleInProgress → StateRoundSettlement**：等待所有玩家`EndBattleReq`
- ✅ **StateEliminationCheck → StateRoundStart**：等待所有玩家`EnterBattleReq`（回合确认）

#### **EnterBattle逻辑修复**：

// 区分初始进入和回合确认

if (currentBattleState == BattleState.StateNone)

{

  // 第一次进入：正常处理玩家进入

  enteredPlayers.Add(playerId);

  if (enteredPlayers.Count >= allPlayers.Count)

  {

​    scene.StartBattleStateMachine();

  }

}

else

{

  // 回合确认：通知Scene处理新回合确认

  scene.HandleRoundConfirmation(playerId);

}

### **结算机制演进历史**

#### **旧机制（已废弃）**：
- 按实例推送：单个实例完成后立即推送结果
- 问题：无法准确判断游戏是否结束，导致isEnd字段错误

#### **新机制（当前使用）**：
- 分阶段结算：等待所有实例完成后统一处理
- 优势：确保isEnd字段准确，消息顺序正确，避免重复扣血

### **血量和胜负机制**

#### **血量计算系统**：

**配置来源**：PlayMode表的PlayerHP字段
```json
"PlayerHP": [[0,999,3],[1000,100000,4]]
```

**计算规则**：
- **统一血量原则**：所有玩家使用相同血量值
- **最高杯段原则**：取4名玩家中最高奖杯数对应的血量
- **超限处理**：超过配置上限时使用最高血量值

**实现位置**：
- **GameServer**：在`matchResultHandler`中计算血量并设置到`LCMatchSuccessNotify`
- **BattleServer**：在`PlayerManager.Initialize`中使用相同逻辑计算血量

**血量配置示例**：
- **0-999奖杯**：3血
- **1000+奖杯**：4血

#### **胜负判定**：

1. **一方全灭**：对方获胜
2. **双方全灭**：平局，双方扣1血
3. **战斗超时**：按减员数量判定，减员多的败，相同则平局

#### **游戏结束条件**：

- **3人生命值为0**：剩余1人获胜
- **血量为0不再扣除**：避免负血量显示

## 10. 回合结算与消息推送机制

### 10.1 结算流程概述

自走棋战斗采用**分阶段结算机制**，确保消息顺序正确和isEnd字段准确：

1. **多实例并行战斗**：每回合可能有多个战斗实例同时进行
2. **等待所有实例完成**：先完成的实例等待后完成的实例
3. **统一结算处理**：所有实例完成后统一扣血和发送通知
4. **分类处理玩家**：被淘汰玩家立即处理，存活玩家延迟处理

### 10.2 核心结算逻辑

#### **阶段1：实例完成检测**

```csharp
// 在HandleBattleEnd_Internal中
if (allInstancesFinished) {
    // 所有实例都完成，进入统一结算
    _battleStateManager.EndBattle();
} else {
    // 还有其他实例未完成，记录结果但不处理
    ProcessSingleInstanceResult(winnerId, loserId);
}
```

**关键特点**：
- 单个实例完成时**不立即扣血**，避免重复扣血
- 等待所有实例完成后才能准确判断游戏状态

#### **阶段2：统一结算处理**

```csharp
// 在ProcessBattleResults中
foreach (var instance in finishedInstances) {
    // 1. 扣血
    _playerManager.ReducePlayerHealth(loserId, 1);

    // 2. 检查是否被淘汰
    if (_playerManager.IsPlayerEliminated(loserId)) {
        // 立即处理被淘汰玩家
        NotifyEliminatedPlayerImmediately(winnerId, loserId);
    } else {
        // 加入待处理列表
        pendingPlayers.Add((winnerId, loserId));
    }
}

// 3. 处理存活玩家
ProcessPendingPlayers(pendingPlayers);
```

### 10.3 消息推送机制

#### **被淘汰玩家（立即处理）**

```csharp
// NotifyEliminatedPlayerImmediately流程
1. 发送 RoundBattleEndReq (isEnd=true)
2. 发送 BattleEndReq (包含排名信息)
3. 清理战斗映射
4. 标记为已通知，避免重复
```

**时序保证**：
- 使用同步发送确保消息顺序
- 先发送`RoundBattleEnd`，再发送`BattleEnd`
- 立即清理映射，允许玩家加入新匹配

#### **存活玩家（延迟处理）**

```csharp
// ProcessPendingPlayers流程
1. 判断游戏是否结束 (activePlayerIds.Count <= 1)
2. 为每个存活玩家发送 RoundBattleEndReq (isEnd=准确值)
3. 如果游戏结束，发送 BattleEndReq 给所有剩余玩家
```

**isEnd字段设置**：
- **被淘汰玩家**：`isEnd=true`（游戏对该玩家结束）
- **存活玩家**：`isEnd=gameEnding`（基于实际游戏状态）
- **冠军玩家**：`isEnd=true`（游戏结束时）

### 10.4 多实例处理验证

#### **实际案例分析**

以下是真实战斗日志的验证结果：

**第6回合两实例场景**：
- **实例1**：10102021313 vs 90000061495（存活玩家对战）
- **实例2**：90000093443 vs 90000046724（存活vs已淘汰填充）

**时间差异**：
- 实例2完成：15:55:24.9969
- 实例1完成：15:55:42.9745
- **时间差**：18秒

**处理流程**：
```
15:55:24.9969 - 实例2完成，等待其他实例
15:55:42.9745 - 实例1完成
15:55:42.9863 - 所有实例完成，开始统一结算
15:55:42.9863 - 准确判断游戏结束，发送isEnd=true
```

### 10.5 被淘汰玩家处理机制

#### **淘汰触发条件**：
- 玩家血量降至0时，系统自动标记该玩家为已淘汰状态

#### **淘汰处理流程**：

1. **血量扣除和淘汰检查**：
   - 战斗失败后，系统调用`ReducePlayerHealth`扣除血量
   - 如果血量降至0，自动调用`EliminatePlayer`标记玩家为已淘汰

2. **立即通知处理**：
   - **第一步**：被淘汰玩家收到`RoundBattleEndReq`，其中`isEnd=true`
   - **第二步**：被淘汰玩家立即收到个人`BattleEndReq`（包含最终排名和阵容信息）
   - **第三步**：清理战斗映射，允许立即加入新匹配

3. **重复通知防护**：
   - 使用`_notifiedEliminatedPlayers`集合防止重复通知
   - 后续回合跳过已被淘汰的玩家，避免发送多余的`RoundBattleEnd`

4. **排名计算**：
   - 存活玩家按血量排序（血量高的排名靠前）
   - 被淘汰玩家按淘汰顺序排名（后淘汰的排名靠前）

5. **后续参与**：
   - 被淘汰玩家在旧战斗中作为"机器人镜像"继续存在，避免轮空
   - 真实玩家可立即参与新战斗匹配
   - 镜像使用上一回合棋盘数据，纯自动操作

### 10.6 客户端处理建议

#### **消息处理顺序**：
- 收到`isEnd=true`的`RoundBattleEndReq`时，应立即显示结算界面
- 收到`BattleEndReq`时，显示最终排名和奖励结算
- **严格按顺序处理**：先处理`RoundBattleEnd`，再处理`BattleEnd`

#### **状态管理**：
- 被淘汰玩家可安全退出战斗并立即开始新的匹配
- 存活玩家根据`isEnd`字段判断是否继续下一回合
- 冠军玩家在收到`isEnd=true`后显示胜利界面

#### **异常处理**：
- 如果消息顺序错乱，客户端应缓存`BattleEnd`直到收到对应的`RoundBattleEnd`
- 超时机制确保即使网络异常也能正常结算

### 10.7 关键设计原则总结

#### **核心原则**：
1. **消息顺序保证**：`RoundBattleEnd` → `BattleEnd`，使用同步发送确保顺序
2. **isEnd字段准确**：基于完整游戏状态判断，避免客户端状态错乱
3. **避免重复扣血**：只在统一结算阶段扣血，单实例完成时不处理
4. **分类处理玩家**：被淘汰玩家立即处理，存活玩家等待统一判断
5. **防重复通知**：使用标记机制避免向已淘汰玩家发送多余通知

#### **性能优化**：
- **多实例等待机制**：确保准确性的同时最小化等待时间
- **立即映射清理**：被淘汰玩家可立即加入新匹配
- **同步通知发送**：确保消息顺序的同时避免过度延迟

#### **稳定性保证**：
- **状态一致性**：服务器和客户端状态完全同步
- **异常恢复**：网络异常时的超时和重试机制
- **资源管理**：及时清理映射和状态，避免内存泄漏

---

## 12. 战斗日志系统

### 11.1 设计目标

为每场战斗生成独立的日志文件，包含完整的战斗过程记录，便于问题排查和数据分析。

### 11.2 实现方案

**核心特性**：
- **零代码修改**：现有的`Log.Info`、`Log.Error`等调用自动输出到战斗日志
- **智能路径检测**：自动适配不同的工作目录环境
- **自动目录创建**：系统自动创建必要的日志目录

**文件命名格式**：
```
battle_年-月-日_时-分-秒_战斗ID.log
例如：battle_2025-01-17_14-30-25_123456789.log
```

**存储位置**：`liteframe/bin/battlelog/`

### 11.3 技术实现

**BattleLogger类**：
- 动态创建NLog文件目标，将BattleServer的所有日志重定向到战斗专用文件
- 智能路径检测：根据当前工作目录自动选择正确的日志路径
- 自动资源管理：战斗结束时自动清理NLog配置

**集成方式**：
- `AutoChessScene.InitBattleWithoutStart()`：创建BattleLogger实例
- `AutoChessScene.Dispose()`：清理BattleLogger资源

### 11.4 日志内容

每个战斗日志文件包含：
- 战斗初始化和玩家信息
- 回合开始/结束事件
- 玩家操作记录（准备、选择Buff、战斗结果等）
- 状态机转换过程
- 错误和异常信息
- 战斗结束和结果

---

## 13. 性能指标

### 12.1 单回合时间

- **理想情况**（真人操作）：约125秒/回合
- **机器人优化**（1真人+3机器人）：约65秒/回合
- **超时兜底**：最多127秒/回合

### 12.2 并发支持

- **线程安全**：全面使用ConcurrentDictionary
- **事件驱动**：异步处理，高并发支持
- **资源管理**：完善的清理机制，避免内存泄漏

### 12.3 可靠性保证

- **超时托管**：所有关键节点都有超时处理
- **错误恢复**：完善的异常处理和日志记录
- **状态一致性**：原子操作保证数据一致性

---

## 14. 事件驱动架构

### **BattleEventBus设计**

AutoChessScene使用事件总线模式实现组件间的解耦通信：

```csharp
// 事件注册
_eventBus.Subscribe<BattleStateChangedEvent>(OnBattleStateChanged);
_eventBus.Subscribe<AllPlayersReadyEvent>(OnAllPlayersReady);
_eventBus.Subscribe<PlayerEliminatedEvent>(OnPlayerEliminated);

// 事件发布
_eventBus.Publish(new BattleStateChangedEvent(oldState, newState, countdown));
```

### **核心事件类型**

#### **状态事件**
- `BattleStateChangedEvent`：战斗状态变化
- `RoundStartedEvent`：回合开始
- `BattleTimeoutEvent`：战斗超时

#### **玩家事件**
- `AllPlayersReadyEvent`：所有玩家准备完毕
- `PlayerEliminatedEvent`：玩家被淘汰（自动清理实体、Buff和战斗映射）
- `GameOverEvent`：游戏结束

#### **实体事件**
- `EntityCreatedEvent`：实体创建
- `EntityMovedEvent`：实体移动
- `EntityMergedEvent`：实体合成
- `EntityRemovedEvent`：实体移除

### **事件处理流程**

1. **状态变化触发**：BattleStateManager发布状态变化事件
2. **组件响应**：各组件订阅相关事件并执行对应逻辑
3. **级联处理**：一个事件可能触发多个组件的响应
4. **异步执行**：事件处理不阻塞主流程


---

## 15. 多线程模型与线程安全

### 14.1 线程架构设计

#### 14.1.1 线程分布模型

新架构采用**主线程 + WorkUnit线程池**的设计模式：

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    主线程        │    │  WorkUnit线程1   │    │  WorkUnit线程2   │
│                 │    │                 │    │                 │
│ BattleService   │    │ AutoChessScene1 │    │ AutoChessScene2 │
│ SceneManager    │    │ (战斗498001)     │    │ (战斗498002)     │
│ AutoChessScene  │    │                 │    │                 │
│ Handler         │    │                 │    │                 │
│ NatsClient      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### 14.1.2 线程职责划分

**主线程职责**：
- **路由协调**：处理RPC请求，转换为Packet消息
- **状态管理**：维护全局战斗状态映射
- **生命周期管理**：创建和销毁WorkUnit线程
- **消息分发**：将消息路由到正确的WorkUnit线程

**WorkUnit线程职责**：
- **业务逻辑处理**：执行具体的战斗逻辑
- **状态机管理**：管理单个战斗的状态转换
- **时间管理**：处理倒计时和超时逻辑
- **事件处理**：处理战斗内的各种事件

### 14.2 线程间通信机制

#### 14.2.1 消息传递模式

**主线程 → WorkUnit线程**：
- **通信方式**：Packet消息队列
- **消息类型**：AutoChess专用消息（1000-1013）
- **处理方式**：异步队列处理，不阻塞主线程
- **线程安全**：通过ConcurrentQueue保证

**WorkUnit线程 → 主线程**：
- **通信方式**：响应回调机制
- **数据传递**：通过返回值传递处理结果
- **同步机制**：主线程等待WorkUnit处理完成

#### 14.2.2 跨线程数据访问

**线程安全数据结构**：
- `ConcurrentDictionary<long, long> _playerToBattle`：玩家到战斗的映射
- `ConcurrentDictionary<long, AutoChessScene> _autoChessScenes`：战斗场景映射
- `ConcurrentDictionary<long, BattleState> _battles`：战斗状态映射

**访问模式**：
- **读取操作**：多线程并发读取，无锁竞争
- **写入操作**：通过ConcurrentDictionary的原子操作保证安全
- **复合操作**：在单个线程内完成，避免竞态条件

### 14.3 线程安全保证机制

#### 14.3.1 无锁设计原则

**消除锁竞争**：
- **原架构问题**：大量`lock`语句导致性能瓶颈和死锁风险
- **新架构解决**：完全消除`lock`语句，通过线程隔离保证安全

**线程隔离策略**：
- **数据隔离**：每个AutoChessScene的业务数据只在其WorkUnit线程中访问
- **状态隔离**：全局状态通过线程安全数据结构管理
- **操作隔离**：玩家操作路由到对应的WorkUnit线程处理

#### 14.3.2 内存访问模式

**同线程访问（高性能）**：
```text
WorkUnit线程中的调用链：
AutoChessScene.SelectBuff()
  → SceneManager.Instance.IsPlayerInBattle()  // 同线程，直接内存访问
  → _battleStateManager.Update()              // 同线程，直接方法调用
  → _playerManager.GetPlayer()                // 同线程，直接数据访问
```

**跨线程访问（受控）**：
```text
主线程 → WorkUnit线程：
BattleService.SelectBuffer()
  → Packet消息队列                           // 线程安全队列
  → AutoChessSceneHandler.SelectBuffReqHandler() // WorkUnit线程处理
```

#### 14.3.3 竞态条件防护

**玩家状态一致性**：
- **问题场景**：玩家同时参与多个战斗时的状态管理
- **解决方案**：通过战斗归属检查防止状态污染
- **实现机制**：`IsPlayerInBattle(playerId, battleId)`验证

**消息推送保护**：
- **问题场景**：旧战斗向已转移玩家推送消息
- **解决方案**：推送前检查玩家当前归属
- **实现机制**：所有推送前调用归属验证

### 14.4 性能优化策略

#### 14.4.1 内存管理优化

**对象池化**：
- **Packet消息**：复用消息对象，减少GC压力
- **事件对象**：事件总线中的事件对象复用

**内存局部性**：
- **数据聚合**：相关数据在同一线程中处理
- **缓存友好**：减少跨线程数据访问

### 14.5 故障隔离与恢复

#### 14.5.1 故障隔离机制

**线程级隔离**：
- **单战斗故障**：不影响其他战斗的正常进行
- **WorkUnit异常**：自动捕获和恢复，不影响主线程
- **资源泄漏防护**：WorkUnit销毁时自动清理资源

**状态一致性保护**：
- **映射验证**：定期检查状态映射的一致性
- **自动修复**：发现不一致时自动修复
- **日志记录**：详细记录所有状态变更

#### 14.5.2 监控与诊断

**线程监控**：
- **WorkUnit状态**：监控每个WorkUnit的运行状态
- **消息队列长度**：监控消息积压情况
- **处理延迟**：监控消息处理延迟

**性能指标**：
- **线程利用率**：各线程的CPU使用率
- **内存使用**：各线程的内存占用
- **GC压力**：垃圾回收频率和耗时

### 14.6 架构优势总结

#### 14.6.1 性能提升

**并发能力**：
- **原架构**：单线程处理，锁竞争严重
- **新架构**：多线程并行，无锁竞争

**响应延迟**：
- **原架构**：锁等待导致延迟不稳定
- **新架构**：线程隔离，延迟稳定可预测

#### 14.6.2 可维护性提升

**代码简化**：
- **BattleService**：从995行减少到355行（减少64%）
- **复杂度降低**：消除所有锁逻辑
- **职责清晰**：每个组件职责明确

**调试友好**：
- **线程隔离**：问题定位更容易
- **状态透明**：全局状态集中管理
- **日志完整**：详细的线程执行日志

#### 14.6.3 扩展性提升

**水平扩展**：
- **多战斗并行**：可以同时处理更多战斗
- **资源隔离**：单个战斗不影响整体性能

**功能扩展**：
- **组件化设计**：新功能易于添加
- **事件驱动**：功能间松耦合

---

## 16. 总结

AutoChess BattleServer 是一个复杂的多人实时战斗系统，通过精心设计的组件化架构和状态管理，实现了稳定可靠的4人自走棋对战功能。

### 16.1 核心特性

- **多人实时对战**：支持4人同时进行的自走棋战斗
- **跨服务器支持**：玩家可以来自不同的GameServer
- **状态同步机制**：实时同步战斗状态到GameServer
- **Buff选择系统**：回合制的策略选择机制
- **英雄合成系统**：复杂的棋子合成和升级逻辑
- **统一坐标系统**：服务器使用绝对GridID，客户端负责视角转换
- **AI对战支持**：奇数玩家与AI对战的完整实现
- **分阶段结算机制**：确保消息顺序正确和isEnd字段准确的结算系统

### 16.2 技术亮点

- **组件化设计**：清晰的职责分离和依赖注入
- **事件驱动架构**：基于EventBus的松耦合通信
- **状态机管理**：严格的战斗状态流转控制
- **内存池优化**：高效的对象复用机制
- **NATS RPC通信**：可靠的跨服务通信
- **坐标系统设计**：复杂的多视角坐标转换机制
- **多实例结算优化**：等待所有实例完成后统一处理，确保游戏状态判断准确

### 16.3 超时处理的可靠性保证

#### **多层防护机制**
1. **客户端超时**: 客户端自己的倒计时UI提示
2. **服务器超时**: 服务器强制处理兜底保证游戏继续
3. **机器人托管**: 机器人立即操作减少等待时间

#### **离线处理能力**
- 客户端离线时，服务器超时机制确保游戏继续进行
- 机器人填充确保4人游戏正常运转
- 所有关键节点都有超时兜底处理，避免游戏卡死

#### **超时时间戳推送**
系统通过协议字段向客户端推送超时时间戳：
- `RoundStartReq.TimeoutTimestamp`: 准备阶段超时时间戳
- `RoundStartReq.BuffTimeoutTimestamp`: Buff选择超时时间戳
- `RoundBattleStartReq.TimeoutTimestamp`: 战斗阶段超时时间戳
- `RoundBattleEndReq.TimeoutTimestamp`: 结算确认超时时间戳

这确保客户端和服务器的超时处理完全同步。

### 16.4 系统稳定性

系统已经能够稳定支持复杂的多人自走棋对战场景，包括GridID唯一性保证、SelectBuff功能完整性、奇数玩家AI对战支持、回合间状态一致性、完善的超时处理机制等，现已达到生产环境可用的稳定性水平。
