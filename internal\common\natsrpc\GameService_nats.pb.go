// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v4.23.2
// source: GameService.proto

package natsrpc

import (
	context "context"
	fmt "fmt"
	nats_go "github.com/nats-io/nats.go"
	proto "google.golang.org/protobuf/proto"
	log "liteframe/pkg/log"
	znats "liteframe/pkg/znats"
	debug "runtime/debug"
)

var _ = new(context.Context)
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = znats.Version
var _ = nats_go.Version
var _ = log.Version
var _ = debug.Stack

// gameServiceClient is the client API for GameService service.
type gameServiceClient struct {
	client *znats.Client
}

func NewNatsRpcGameServiceClient(client *znats.Client) *gameServiceClient {
	return &gameServiceClient{client: client}
}

func (c *gameServiceClient) Auth(ctx context.Context, req *AuthReq, serverId string) (*AuthResp, error) {
	resp := &AuthResp{}
	err := c.client.Request(ctx, "/"+serverId+"/natsrpc.GameService/Auth", req, resp)
	return resp, err
}
func (c *gameServiceClient) AsyncAuth(ctx context.Context, req *AuthReq, serverId string, cb func(*AuthResp, error)) {
	go func() {
		defer func() {
			if e := recover(); e != nil {
				trace := string(debug.Stack())
				debug.PrintStack()
				log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
			}
		}()
		resp := &AuthResp{}
		err := c.client.Request(ctx, "/"+serverId+"/natsrpc.GameService/Auth", req, resp)
		cb(resp, err)
	}()
}

func (c *gameServiceClient) MatchResult(ctx context.Context, req *MatchResultRequest, serverId string) error {
	return c.client.Publish("/"+serverId+"/natsrpc.GameService/MatchResult", req)
}

func (c *gameServiceClient) RoundStart(ctx context.Context, req *RoundStartReq, serverId string) error {
	return c.client.Publish("/"+serverId+"/natsrpc.GameService/RoundStart", req)
}

func (c *gameServiceClient) RoundBattleStart(ctx context.Context, req *RoundBattleStartReq, serverId string) error {
	return c.client.Publish("/"+serverId+"/natsrpc.GameService/RoundBattleStart", req)
}

func (c *gameServiceClient) RoundBattleEnd(ctx context.Context, req *RoundBattleEndReq, serverId string) error {
	return c.client.Publish("/"+serverId+"/natsrpc.GameService/RoundBattleEnd", req)
}

func (c *gameServiceClient) BattleEnd(ctx context.Context, req *BattleEndReq, serverId string) error {
	return c.client.Publish("/"+serverId+"/natsrpc.GameService/BattleEnd", req)
}

func (c *gameServiceClient) OnBattleStateChanged(ctx context.Context, req *BattleStateChangeReq, serverId string) error {
	return c.client.Publish("/"+serverId+"/natsrpc.GameService/OnBattleStateChanged", req)
}

type GameServiceServerNatsRpcServer struct {
	server *znats.Server
	GameServiceServer
	serverId string
}

func NewGameServiceServerNatsRpcServer(s *znats.Server, impl GameServiceServer, serverId string) *GameServiceServerNatsRpcServer {
	server := &GameServiceServerNatsRpcServer{
		server:            s,
		GameServiceServer: impl,
		serverId:          serverId,
	}
	s.QueueSubscribe("/"+serverId+"/natsrpc.GameService/Auth", "GameService", server.AuthHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.GameService/MatchResult", "GameService", server.MatchResultHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.GameService/RoundStart", "GameService", server.RoundStartHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.GameService/RoundBattleStart", "GameService", server.RoundBattleStartHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.GameService/RoundBattleEnd", "GameService", server.RoundBattleEndHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.GameService/BattleEnd", "GameService", server.BattleEndHandlerWrap)
	s.QueueSubscribe("/"+serverId+"/natsrpc.GameService/OnBattleStateChanged", "GameService", server.OnBattleStateChangedHandlerWrap)
	return server
}

func (s *GameServiceServerNatsRpcServer) AuthHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &AuthReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.Auth(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *GameServiceServerNatsRpcServer) MatchResultHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &MatchResultRequest{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.MatchResult(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *GameServiceServerNatsRpcServer) RoundStartHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &RoundStartReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.RoundStart(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *GameServiceServerNatsRpcServer) RoundBattleStartHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &RoundBattleStartReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.RoundBattleStart(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *GameServiceServerNatsRpcServer) RoundBattleEndHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &RoundBattleEndReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.RoundBattleEnd(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *GameServiceServerNatsRpcServer) BattleEndHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &BattleEndReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.BattleEnd(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}

func (s *GameServiceServerNatsRpcServer) OnBattleStateChangedHandlerWrap(msg *nats_go.Msg) {
	defer func() {
		if e := recover(); e != nil {
			trace := string(debug.Stack())
			debug.PrintStack()
			log.Error("RecoverHandler", log.Kv("panic\n", e), log.Kv("stack", trace))
		}
	}()

	req := &BattleStateChangeReq{}
	ret := &nats_go.Msg{Header: make(nats_go.Header)}
	defer msg.RespondMsg(ret)

	err := proto.Unmarshal(msg.Data, req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("unmarshal request error:%s", err.Error()))
		return
	}
	resp, err := s.OnBattleStateChanged(context.Background(), req)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("response error:%s", err.Error()))
		return
	}
	ds, err := proto.Marshal(resp)
	if err != nil {
		ret.Header.Set(znats.ErrorField, fmt.Sprintf("marshal error:%s", err.Error()))
		return
	}
	ret.Data = ds
}
