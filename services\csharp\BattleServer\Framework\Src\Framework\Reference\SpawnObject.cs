//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-10-25
//*********************************************************


using System;

namespace Aurora.Framework
{
    public abstract class SpawnObject : IReference
    {
        private string m_Name;
        private object m_Target;
        private bool m_Locked;
        private DateTime m_LastUseTime;

        public object Target
        {
            get { return m_Target; }
        }
        public string Name
        {
            get { return m_Name; }
        }
        public bool Locked
        {
            get { return m_Locked; }
            set { m_Locked = value; }
        }
        public DateTime LastUseTime
        {
            get { return m_LastUseTime; }
            set { m_LastUseTime = value; }
        }

        public SpawnObject()
        {
            m_Target = null;
            m_Locked = false;
            m_LastUseTime = default(DateTime);
        }

        public void BindTarget(object target, string name="", bool locked = false)
        {
            if(target == null)
            {
                throw new FrameworkException("SpawnObject bind target with null!");
            }
            m_Target = target;
            m_Locked = locked;
            m_Name = name;
            m_LastUseTime = DateTime.UtcNow;
        }



        public virtual void Clear()
        {
            m_Target = null;
            m_Locked = false;
            m_LastUseTime = default(DateTime);
            m_Name = "";
        }

        public virtual void OnSpawn()
        {
        }

        public virtual void OnUnspawn()
        {
        }

        public abstract void Release();
    }
}

