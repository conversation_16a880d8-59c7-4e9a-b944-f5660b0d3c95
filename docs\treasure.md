# 宝物模块文档

## 概述

宝物模块是游戏中的核心养成系统，提供宝物收集、升级、升星和抽取功能。作为自走棋战斗羁绊效果的载体，为玩家提供中长期养成线和付费深度。

## 功能架构

### 核心功能
- **宝物列表**：返回玩家所有已激活宝物的状态信息
- **宝物升级**：消耗材料提升宝物等级，增加属性加成
- **宝物升星**：消耗同名宝物和材料提升星级，开启羁绊效果
- **宝物抽取**：复杂的抽卡系统，支持多种消耗方式和概率机制

### 数据结构

#### 宝物信息 (PBTreasureInfo)
```protobuf
message PBTreasureInfo {
    int32 treasureId = 1;  // 宝物ID
    int32 level = 2;       // 等级
    int32 star = 3;        // 星级
    int32 count = 4;       // 当前数量
}
```

#### 宝物数据存储 (Treasure)
```protobuf
message Treasure {
    repeated PBTreasureInfo treasureList = 1;        // 宝物列表
    map<int32, int32> totalAcquiredCount = 2;        // 累计获得数量
    map<int32, int32> gachaPityCounts = 3;           // 保底计数
    int32 dailyAdGachaCounts = 4;                    // 每日广告抽取次数
    int64 lastAdGachaResetTime = 5;                  // 上次广告次数重置时间
}
```

## 配置表结构

### 基础配置表

#### TableTreasure - 宝物基础配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 宝物ID |
| quality | string | 品质 |
| MaxLv | int32 | 最大等级 |
| MaxStar | int32 | 最大星级 |
| Role | int32[] | 羁绊英雄 |
| EffectRelation | int32[] | 羁绊效果 |
| EffectStar | int32[] | 升星效果 |
| Attribute | int32[][] | 等级属性 |

#### TableTreasureLv - 升级配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 宝物ID |
| Quality | int32 | 品质 |
| Lv | int32 | 等级 |
| Cost | int32[][] | 消耗材料 [[道具ID, 数量], ...] |

#### TableTreasureStar - 升星配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 宝物ID |
| Quality | int32 | 品质 |
| Star | int32 | 星级 |
| Cost1 | int32 | 消耗同名宝物数量 |
| Cost2 | int32[][] | 消耗材料 [[道具ID, 数量], ...] |
| EffectRelation | int32 | 羁绊效果档位 |

### 抽取配置表

#### TableTreasureGacha - 抽卡配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 卡池ID |
| Cost | int32[] | 消耗 [道具ID, 数量] |
| Bag | int32[][] | 普通卡包 [[卡包ID, 权重], ...] |
| BagSpecial | int32[][] | 保底卡包 [[卡包ID, 权重], ...] |
| Count | int32 | 保底次数 |

#### TableTreasureGachaPro - 抽卡概率配置
| 字段 | 类型 | 说明 |
|------|------|------|
| ID | int32 | 递增ID |
| Bag | int32 | 卡包ID |
| Item | int32 | 道具ID |
| Count | int32 | 道具数量 |
| Pro | int32 | 基础概率 |

## 抽取算法详解

### 整体流程
```
客户端请求 → 配置验证 → 消耗检查 → 多次抽取循环 → 卡包选择 → 道具抽取 → 概率修正 → 奖励发放 → 返回结果
```

### 1. 卡包选择算法

#### 保底机制
```go
currentPityCount := t.db.GachaPityCounts[req.GachaId]
isSpecialBag := (currentPityCount+1) >= gachaConfig.Count

if isSpecialBag && gachaConfig.BagSpecial != nil {
    // 使用保底卡包，重置计数
    selectedBagId = t.selectBagByWeight(gachaConfig.BagSpecial)
    t.db.GachaPityCounts[req.GachaId] = 0
} else {
    // 使用普通卡包，增加计数
    selectedBagId = t.selectBagByWeight(gachaConfig.Bag)
    t.db.GachaPityCounts[req.GachaId] = currentPityCount + 1
}
```

#### 权重选择算法
```go
func (t *Treasure) selectBagByWeight(bags [][]int32) int32 {
    // 1. 计算总权重
    totalWeight := int32(0)
    for _, bag := range bags {
        totalWeight += bag[1] // bag[1]是权重
    }
    
    // 2. 生成随机数
    randomValue := rand.Int31n(totalWeight)
    
    // 3. 查找目标区间
    currentWeight := int32(0)
    for _, bag := range bags {
        currentWeight += bag[1]
        if randomValue < currentWeight {
            return bag[0] // bag[0]是卡包ID
        }
    }
}
```

### 2. 概率修正算法

#### 修正逻辑
- **仅对宝物生效**：普通道具不进行概率修正
- **基于累计获得数量**：已获得越多，概率越低
- **动态调整系数**：使用配置的修正系数数组

#### 修正计算
```go
func (t *Treasure) calculateProbabilityModifier(itemId int32) int32 {
    // 检查是否是宝物
    treasureConfig := table.GetTable().TableTreasure.GetById(itemId)
    if treasureConfig == nil {
        return 10000 // 不是宝物，不修正
    }
    
    // 获取已有数量
    acquiredCount := t.getTotalAcquiredCount(itemId)
    
    // 获取修正系数配置 [20000, 9000, 8000, 7000, 6000, 5000...]
    modifyConfig := table.GetTreasureGachaProModify()
    
    // 根据数量获取对应系数
    if int(acquiredCount) < len(modifyConfig) {
        return modifyConfig[acquiredCount]
    }
    return modifyConfig[len(modifyConfig)-1]
}
```

#### 修正示例
假设宝物基础概率为100：
- 数量为0时：实际概率 = 100 * 20000 / 10000 = 200 (2倍)
- 数量为1时：实际概率 = 100 * 9000 / 10000 = 90 (0.9倍)
- 数量为2时：实际概率 = 100 * 8000 / 10000 = 80 (0.8倍)

### 3. 圆桌抽取算法

#### 算法原理
圆桌抽取是根据权重进行随机选择的经典算法，将所有选项按权重比例排列在数轴上，随机投点选择。

#### 实现步骤
```go
// 1. 收集道具并计算修正后概率
items := make([]*ItemProb, 0)
for _, bagItem := range bagItems {
    modifier := t.calculateProbabilityModifier(bagItem.ItemId)
    realPro := bagItem.BasePro * modifier / 10000
    items = append(items, &ItemProb{
        ItemId: bagItem.ItemId,
        Count: bagItem.Count,
        RealPro: realPro,
    })
}

// 2. 计算总概率
totalPro := int32(0)
for _, item := range items {
    totalPro += item.RealPro
}

// 3. 生成随机数并查找
randomValue := rand.Int31n(totalPro)
currentPro := int32(0)
for _, item := range items {
    currentPro += item.RealPro
    if randomValue < currentPro {
        return item.ItemId, item.Count
    }
}
```

#### 概率区间示例
假设卡包内有4个道具：
```
道具A: 修正后概率 200
道具B: 修正后概率 80  
道具C: 修正后概率 500
道具D: 修正后概率 300
总概率: 1080

区间分布:
[0---200][200---280][280---780][780---1080]
 道具A     道具B      道具C      道具D

随机数350 → 落在[280, 780) → 选中道具C
```

## 懒加载索引优化

### 优化背景
原始实现每次抽取都需要遍历整个TreasureGachaPro表查找对应卡包的道具，时间复杂度O(n)，在频繁抽取时性能较差。

### 优化方案
采用懒加载索引策略，在首次抽取时建立卡包道具索引，后续抽取直接使用索引。

#### 数据结构
```go
type TreasureGachaItem struct {
    ItemId  int32  // 道具ID
    Count   int32  // 道具数量
    BasePro int32  // 基础概率
}

type Treasure struct {
    // 懒加载索引：卡包ID -> 道具列表
    bagItemsIndex map[int32][]*TreasureGachaItem
}
```

#### 索引建立
```go
func (t *Treasure) buildBagItemsIndex(bagId int32) {
    // 检查索引是否已存在
    if _, exists := t.bagItemsIndex[bagId]; exists {
        return
    }
    
    // 遍历配置表建立索引
    items := make([]*TreasureGachaItem, 0)
    table.GetTable().TableTreasureGachaPro.Foreach(func(config *table_data.TableTreasureGachaPro) bool {
        if config.Bag == bagId {
            items = append(items, &TreasureGachaItem{
                ItemId:  config.Item,
                Count:   config.Count,
                BasePro: config.Pro,
            })
        }
        return false
    })
    
    // 存储索引
    t.bagItemsIndex[bagId] = items
}
```

#### 索引使用
```go
func (t *Treasure) getBagItems(bagId int32) []*TreasureGachaItem {
    // 懒加载：如果索引不存在则建立
    if _, exists := t.bagItemsIndex[bagId]; !exists {
        t.buildBagItemsIndex(bagId)
    }
    return t.bagItemsIndex[bagId]
}
```

### 性能对比

#### 优化前
- 单次抽取：O(n) - 遍历整个配置表
- 10连抽：O(10n) - 10次全表遍历
- 内存占用：无额外占用

#### 优化后
- 首次抽取：O(n) + O(1) - 建立索引 + 使用索引
- 后续抽取：O(1) - 直接使用索引
- 10连抽：O(n + 10) - 1次建立索引 + 10次索引查找
- 内存占用：索引数据（约6KB，10个卡包×50个道具×12字节）

#### 性能提升
假设配置表1000行，卡包50个道具：
- **10连抽**：从10,000次操作降低到1,500次操作，提升6.7倍
- **多次抽取**：随着抽取次数增加，性能提升越来越明显

## 操作类型定义

### 道具增加类型
```go
ADD_ITEM_TREASURE_GACHA_ITEM     // 宝物抽取获得道具
ADD_ITEM_TREASURE_GACHA_AD       // 宝物抽取 - 广告
ADD_ITEM_TREASURE_GM_COMMAND     // 宝物GM命令添加
```

### 道具消耗类型
```go
DEL_ITEM_TREASURE_LEVELUP        // 宝物升级消耗
DEL_ITEM_TREASURE_STARUP         // 宝物升星消耗
DEL_ITEM_TREASURE_GACHA_COST     // 宝物抽取消耗
DEL_ITEM_TREASURE_GM_COMMAND     // 宝物GM命令删除
```

## GM命令系统

### 命令格式
```
gm treasure <subType> <params>
```

### 支持的GM命令

#### 1. 删除宝物
```bash
# 格式：gm treasure 1 treasureId [count]
gm treasure 1 101 5      # 删除宝物101的5个数量
gm treasure 1 101        # 删除宝物101的1个数量（默认）
```

#### 2. 设置宝物等级
```bash
# 格式：gm treasure 2 treasureId level
gm treasure 2 101 50     # 设置宝物101的等级为50
```
- 如果宝物不存在会自动激活
- 等级必须 >= 1

#### 3. 设置宝物星级
```bash
# 格式：gm treasure 3 treasureId star
gm treasure 3 101 5      # 设置宝物101的星级为5
```
- 如果宝物不存在会自动激活
- 星级必须 >= 1

#### 4. 重置广告次数
```bash
# 格式：gm treasure 4
gm treasure 4            # 重置每日广告抽取次数为0
```

#### 5. 模拟抽取
```bash
# 格式：gm treasure 5 gachaId times
gm treasure 5 1 1000     # 模拟卡池1抽取1000次
```
- 输出详细的统计结果到日志
- 包含每个道具的获得数量和最终保底计数

### GM命令使用示例

#### 测试宝物升级流程
```bash
# 1. 设置宝物等级
gm treasure 2 101 10

# 2. 客户端请求宝物列表验证
# 3. 客户端请求宝物升级测试消耗检查
```

#### 测试抽取概率分布
```bash
# 1. 重置广告次数
gm treasure 4

# 2. 大量模拟抽取
gm treasure 5 1 10000

# 3. 查看日志统计结果验证概率分布
```

#### 测试保底机制
```bash
# 1. 模拟接近保底次数的抽取
gm treasure 5 1 19       # 假设保底次数为20

# 2. 再抽取1次触发保底
gm treasure 5 1 1

# 3. 查看日志确认保底卡包被使用
```

## 错误码定义

| 错误码 | 常量名 | 说明 |
|--------|--------|------|
| 6000200 | ERROR_TREASURE_NOT_FOUND | 宝物不存在 |
| 6000201 | ERROR_TREASURE_MAX_LEVEL | 宝物已达到最大等级 |
| 6000202 | ERROR_TREASURE_MAX_STAR | 宝物已达到最大星级 |
| 6000203 | ERROR_TREASURE_LEVELUP_MATERIAL_NOT_ENOUGH | 宝物升级材料不足 |
| 6000204 | ERROR_TREASURE_STARUP_MATERIAL_NOT_ENOUGH | 宝物升星材料不足 |
| 6000205 | ERROR_TREASURE_STARUP_SAME_TREASURE_NOT_ENOUGH | 宝物升星同名宝物数量不足 |
| 6000206 | ERROR_TREASURE_GACHA_CONFIG_NOT_FOUND | 宝物抽取配置不存在 |
| 6000207 | ERROR_TREASURE_GACHA_COST_NOT_ENOUGH | 宝物抽取消耗道具不足 |
| 6000208 | ERROR_TREASURE_GACHA_AD_TIMES_NOT_ENOUGH | 宝物抽取广告次数不足 |
| 6000209 | ERROR_TREASURE_GACHA_COST_TYPE_INVALID | 宝物抽取类型错误 |

## 测试建议

### 功能测试
1. **基础功能**：宝物激活、升级、升星、抽取
2. **边界测试**：最大等级、最大星级、次数限制
3. **错误处理**：参数验证、资源不足、配置缺失

### 性能测试
1. **索引效果**：对比优化前后的抽取性能
2. **内存占用**：监控索引数据的内存使用
3. **并发测试**：多玩家同时抽取的性能表现

### 概率测试
1. **概率分布**：使用模拟抽取验证概率正确性
2. **概率修正**：验证宝物数量对概率的影响
3. **保底机制**：确认保底触发的准确性

### 集成测试
1. **道具系统**：验证与道具模块的正确交互
2. **配置表**：确认所有配置表的完整性和一致性
3. **跨天逻辑**：测试广告次数的跨天重置

## 注意事项

### 开发注意事项
1. **配置表依赖**：确保所有相关配置表正确配置
2. **随机数种子**：生产环境需要合适的随机数种子
3. **并发安全**：当前实现未考虑并发，如需要请添加锁机制

### 运营注意事项
1. **概率公示**：根据法规要求公示抽取概率
2. **保底机制**：确保保底机制符合运营策略
3. **数据统计**：建议添加抽取行为的BI统计

### 性能注意事项
1. **索引内存**：监控索引数据的内存使用情况
2. **配置表大小**：如果配置表过大，考虑进一步优化
3. **缓存策略**：可考虑添加概率计算结果的缓存

---
