﻿using System;
using System.Collections.Generic;

namespace Aurora.Framework
{
    public class PercentileStatisticalList
    {
        private ulong _sampleCount;
        private long _sampleTotal;
        private SortedList<long, ulong> _sampleList;

        public PercentileStatisticalList()
        {
            _sampleCount = 0;
            _sampleTotal = 0;
            _sampleList = new SortedList<long, ulong>();
        }

        public void Clear()
        {
            _sampleCount = 0;
            _sampleTotal = 0;
            _sampleList.Clear();
        }

        public void AddSample(long sample)
        {
            ++_sampleCount;
            _sampleTotal += sample;

            if (!_sampleList.ContainsKey(sample))
            {
                _sampleList.Add(sample, 0);
            }
            ++_sampleList[sample];
        }

        public void RemoveSample(long sample)
        {
            if (_sampleList.ContainsKey(sample))
            {
                if (--_sampleList[sample] == 0)
                {
                    _sampleList.Remove(sample);
                }

                _sampleTotal -= sample;
                --_sampleCount;
            }
        }

        public long CalcAverage()
        {
            if (_sampleCount == 0)
            {
                return 0;
            }
            else
            {
                return _sampleTotal / (long)_sampleCount;
            }
        }

        public long CalcMin()
        {
            return CalcPercentile(0);
        }

        public long CalcMax()
        {
            return CalcPercentile(1);
        }

        public long CalcMedian()
        {
            return CalcPercentile(0.5);
        }

        public long CalcPercentile(double percent)
        {
            long percentile = 0;

            if (_sampleCount > 0)
            {
                ulong position;
                {
                    percent = Math.Clamp(percent, 0, 1);
                    position = (ulong)(_sampleCount * percent);
                    if (position >= _sampleCount)
                    {
                        position = _sampleCount - 1;
                    }
                }

                ulong index = 0;
                foreach (KeyValuePair<long, ulong> keyValuePair in _sampleList)
                {
                    index += keyValuePair.Value;
                    if (index > position)
                    {
                        percentile = keyValuePair.Key;
                        break;
                    }
                }
            }

            return percentile;
        }

        public double CalcPercent(long value)
        {
            double percent = 0;

            if (_sampleCount > 0)
            {
                ulong index = 0;
                double offset = 0;
                foreach (KeyValuePair<long, ulong> keyValuePair in _sampleList)
                {
                    if (keyValuePair.Key < value)
                    {
                        index += keyValuePair.Value;
                    }
                    else
                    {
                        if (keyValuePair.Key == value)
                        {
                            offset = (double)keyValuePair.Value / 2;
                        }
                        break;
                    }
                }
                percent = (index + offset) / _sampleCount;
                percent = Math.Clamp(percent, 0, 1);
            }

            return percent;
        }
    }
}
