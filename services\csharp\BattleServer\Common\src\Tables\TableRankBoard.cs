#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableRankBoard
	{

		public static readonly string TName="RankBoard.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 表名id 
		/// </summary> 
		public int Name {get; set;}
		#endregion

		public static TableRankBoard GetData(int ID)
		{
			return TableManager.RankBoardData.Get(ID);
		}

		public static List<TableRankBoard> GetAllData()
		{
			return TableManager.RankBoardData.GetAll();
		}

	}
	public sealed partial class TableRankBoardData
	{
		private Dictionary<int, TableRankBoard> dict = new Dictionary<int, TableRankBoard>();
		private List<TableRankBoard> dataList = new List<TableRankBoard>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableRankBoard.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableRankBoard>>(jsonContent);
			foreach (TableRankBoard config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableRankBoard Get(int id)
		{
			if (dict.TryGetValue(id, out TableRankBoard item))
				return item;
			return null;
		}

		public List<TableRankBoard> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
