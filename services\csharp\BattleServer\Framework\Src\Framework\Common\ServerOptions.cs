﻿////*********************************************************
//// Framework
//// Author:  Jasen
//// Date  :  2022-11-30
////*********************************************************

//using System.Collections.Generic;

//namespace Aurora.Framework
//{
//    public class ServerConfig
//    {
//        public int ServerID { get; set; }
//        public string IP { get; set; }
//        public int OutPort { get; set; }
//        public int InPort { get; set; }
//    }
//    public class ServerOptions : Singleton<ServerOptions>
//    {
//        public int CurServerID { get; set; }

//        private Dictionary<int, ServerConfig> ServerConfigs = new Dictionary<int, ServerConfig>();
//        public void InitServerGroup(int curServerID, List<ServerConfig> serverConfigs)
//        {
//            CurServerID = curServerID;

//            foreach (var config in serverConfigs)
//            {
//                ServerConfigs.Add(config.ServerID, config);
//            }
//        }
//        public ServerConfig GetServerConfig(int inServerID)
//        {
//            return ServerConfigs[inServerID];
//        }
//    }
//}
