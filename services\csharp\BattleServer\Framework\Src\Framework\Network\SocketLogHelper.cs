//*********************************************************
// Author:  Assistant
// Desc:	Socket连接日志帮助类
// Date  :  2024-12-19
//*********************************************************

using Aurora.Framework;
using System.Net;
using System.Net.Sockets;

namespace Aurora.Framework
{
    public static class SocketLogHelper
    {
        /// <summary>
        /// 记录Socket服务端监听开始
        /// </summary>
        public static void LogSocketServerStart(string serviceType, IPEndPoint endPoint)
        {
            Log.Info($"SocketLogHelper: 开始Socket服务端监听 - 服务类型: {serviceType}, 监听地址: {endPoint}");
        }

        /// <summary>
        /// 记录Socket服务端监听成功
        /// </summary>
        public static void LogSocketServerSuccess(string serviceType, IPEndPoint endPoint)
        {
            Log.Info($"SocketLogHelper: Socket服务端监听成功 - 服务类型: {serviceType}, 监听地址: {endPoint}");
        }

        /// <summary>
        /// 记录Socket服务端监听失败
        /// </summary>
        public static void LogSocketServerFailed(string serviceType, IPEndPoint endPoint, string errorMessage)
        {
            Log.Error($"SocketLogHelper: Socket服务端监听失败 - 服务类型: {serviceType}, 监听地址: {endPoint}, 错误: {errorMessage}");
        }

        /// <summary>
        /// 记录Socket客户端连接开始
        /// </summary>
        public static void LogSocketClientStart(string serviceType, IPEndPoint targetEndPoint)
        {
            Log.Info($"SocketLogHelper: 开始Socket客户端连接 - 服务类型: {serviceType}, 目标地址: {targetEndPoint}");
        }

        /// <summary>
        /// 记录Socket客户端连接成功
        /// </summary>
        public static void LogSocketClientSuccess(string serviceType, IPEndPoint targetEndPoint)
        {
            Log.Info($"SocketLogHelper: Socket客户端连接成功 - 服务类型: {serviceType}, 目标地址: {targetEndPoint}");
        }

        /// <summary>
        /// 记录Socket客户端连接失败
        /// </summary>
        public static void LogSocketClientFailed(string serviceType, IPEndPoint targetEndPoint, string errorMessage)
        {
            Log.Error($"SocketLogHelper: Socket客户端连接失败 - 服务类型: {serviceType}, 目标地址: {targetEndPoint}, 错误: {errorMessage}");
        }

        /// <summary>
        /// 记录Socket连接接受
        /// </summary>
        public static void LogSocketAccept(string serviceType, IPEndPoint remoteEndPoint)
        {
            Log.Info($"SocketLogHelper: Socket连接接受 - 服务类型: {serviceType}, 远程地址: {remoteEndPoint}");
        }

        /// <summary>
        /// 记录Socket连接断开
        /// </summary>
        public static void LogSocketDisconnect(string serviceType, IPEndPoint endPoint)
        {
            Log.Info($"SocketLogHelper: Socket连接断开 - 服务类型: {serviceType}, 地址: {endPoint}");
        }

        /// <summary>
        /// 记录Socket发送数据
        /// </summary>
        public static void LogSocketSend(string serviceType, IPEndPoint endPoint, int dataSize)
        {
            Log.Info($"SocketLogHelper: Socket发送数据 - 服务类型: {serviceType}, 地址: {endPoint}, 数据大小: {dataSize} bytes");
        }

        /// <summary>
        /// 记录Socket接收数据
        /// </summary>
        public static void LogSocketReceive(string serviceType, IPEndPoint endPoint, int dataSize)
        {
            Log.Info($"SocketLogHelper: Socket接收数据 - 服务类型: {serviceType}, 地址: {endPoint}, 数据大小: {dataSize} bytes");
        }
    }
} 