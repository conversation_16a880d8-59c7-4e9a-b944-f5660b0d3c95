// Code generated by rpcparse. DO NOT EDIT.

package rpcgen

import (
	"context"
	"google.golang.org/protobuf/proto"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/game-logic/gameserver/logic/player"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
)

// RegisterTreasureServicecomment
func RegisterTreasureService(dispatch *actor.Dispatcher, service TreasureServiceInterface) {

	// treasurelist
	TreasureListHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLTreasureListReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("TreasureService:TreasureList unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCTreasureListResp{}
			p.SendToClient(rpc_def.LCTreasureListResp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCTreasureListResp{}
			p.SendToClient(rpc_def.LCTreasureListResp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.TreasureList(ctx, p, in)
		p.SendToClient(rpc_def.LCTreasureListResp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLTreasureListReq), TreasureListHandler)

	// treasurelevelup
	TreasureLevelUpHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLTreasureLevelUpReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("TreasureService:TreasureLevelUp unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCTreasureLevelUpResp{}
			p.SendToClient(rpc_def.LCTreasureLevelUpResp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCTreasureLevelUpResp{}
			p.SendToClient(rpc_def.LCTreasureLevelUpResp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.TreasureLevelUp(ctx, p, in)
		p.SendToClient(rpc_def.LCTreasureLevelUpResp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLTreasureLevelUpReq), TreasureLevelUpHandler)

	// treasurestarup
	TreasureStarUpHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLTreasureStarUpReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("TreasureService:TreasureStarUp unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCTreasureStarUpResp{}
			p.SendToClient(rpc_def.LCTreasureStarUpResp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCTreasureStarUpResp{}
			p.SendToClient(rpc_def.LCTreasureStarUpResp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.TreasureStarUp(ctx, p, in)
		p.SendToClient(rpc_def.LCTreasureStarUpResp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLTreasureStarUpReq), TreasureStarUpHandler)

	// treasuregacha
	TreasureGachaHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLTreasureGachaReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("TreasureService:TreasureGacha unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCTreasureGachaResp{}
			p.SendToClient(rpc_def.LCTreasureGachaResp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCTreasureGachaResp{}
			p.SendToClient(rpc_def.LCTreasureGachaResp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.TreasureGacha(ctx, p, in)
		p.SendToClient(rpc_def.LCTreasureGachaResp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLTreasureGachaReq), TreasureGachaHandler)
}
