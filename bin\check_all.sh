#!/bin/bash

# 设置当前目录
current_dir=$(dirname $(readlink -f $0))
cd ${current_dir}

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

check_server() {
    local name=$1
    local pidfile="${name}.pid"
    
    if [ -f $pidfile ]; then
        pid=$(cat $pidfile)
        if kill -0 $pid 2>/dev/null; then
            echo -e "${GREEN}✓ $name is running (PID: $pid)${NC}"
            return 0
        else
            echo -e "${RED}✗ $name is not running (stale PID file)${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}- $name is not running (no PID file)${NC}"
        return 1
    fi
}

echo "=== Server Status Check ==="
echo

# 核心服务
echo "Core Services:"
check_server "webserver"
check_server "gameserver"
check_server "matchserver"
check_server "payserver"
check_server "battleserver"

echo

# 微服务
echo "Microservices:"
check_server "playerinfo"
check_server "friend"
check_server "rank"
check_server "redeemcode"
check_server "chat"

echo

# 通用服务
echo "General Services:"
check_server "textdetect"

echo
echo "=== Summary ==="

# 统计运行状态
running=0
total=0
servers=("webserver" "gameserver" "matchserver" "payserver" "battleserver" "playerinfo" "friend" "rank" "redeemcode" "chat" "textdetect")

for server in "${servers[@]}"; do
    total=$((total + 1))
    if [ -f "${server}.pid" ]; then
        pid=$(cat "${server}.pid")
        if kill -0 $pid 2>/dev/null; then
            running=$((running + 1))
        fi
    fi
done

echo -e "Running: ${GREEN}$running${NC}/$total servers"

if [ $running -eq $total ]; then
    echo -e "${GREEN}All servers are running!${NC}"
    exit 0
else
    echo -e "${YELLOW}Some servers are not running${NC}"
    exit 1
fi