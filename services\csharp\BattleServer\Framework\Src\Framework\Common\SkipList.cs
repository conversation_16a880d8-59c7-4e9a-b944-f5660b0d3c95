﻿using System;
using System.Collections.Generic;

namespace Aurora.Framework
{
    public class SkipList : IReference
    {
        public class Node : IReference
        {
            private long m_Value;
            private List<Node> m_PreviousList;
            private List<Node> m_NextList;

            private Object m_CustomObject;
            private SkipList m_Owner;

            public Node()
            {
                m_PreviousList = new List<Node>();
                m_NextList = new List<Node>();
                m_CustomObject = null;
                m_Owner = null;
            }

            internal void Init(long value, byte nLayerCount, Object customObject, SkipList owner)
            {
                m_Value = value;
                for (byte nLayerIndex = 0; nLayerIndex < nLayerCount; ++nLayerIndex)
                {
                    m_PreviousList.Add(null);
                    m_NextList.Add(null);
                }
                m_CustomObject = customObject;
                m_Owner = owner;
            }

            public void Clear()
            {
                m_Owner = null;
                m_CustomObject = null;
                m_PreviousList.Clear();
                m_NextList.Clear();
            }

            public long Value
            {
                get
                {
                    return m_Value;
                }
                internal set
                {
                    m_Value = value;
                }
            }

            public Object CustomObject
            {
                get
                {
                    return m_CustomObject;
                }
            }

            public Node Previous
            {
                get
                {
                    return m_PreviousList[0];
                }
            }

            public Node Next
            {
                get
                {
                    return m_NextList[0];
                }
            }

            internal SkipList Owner
            {
                get
                {
                    return m_Owner;
                }
            }

            internal byte LayerCount
            {
                get
                {
                    return (byte)Math.Min(m_PreviousList.Count, m_NextList.Count);
                }
            }

            internal Node GetPrevious(byte nLayerIndex)
            {
                return m_PreviousList[nLayerIndex];
            }

            internal void SetPrevious(byte nLayerIndex, Node previousNode)
            {
                m_PreviousList[nLayerIndex] = previousNode;
            }

            internal Node GetNext(byte nLayerIndex)
            {
                return m_NextList[nLayerIndex];
            }

            internal void SetNext(byte nLayerIndex, Node nextNode)
            {
                m_NextList[nLayerIndex] = nextNode;
            }
        }

        private byte m_nMaxLayer;
        private float m_fGrowthProbability;

        private Node m_HeadNode;
        private Node m_TailNode;
        private uint m_nCount;
        private byte m_nLayerCount;
        private List<uint> m_NodeCountList;

        public SkipList()
        {
            m_HeadNode = new Node();
            m_TailNode = new Node();
            m_nCount = 0;
            m_nLayerCount = 1;
            m_NodeCountList = new List<uint>();
        }

        /// <summary>
        /// Init All Data. Once Init, Please Do Not Init Again!
        /// </summary>
        /// <param name="nMaxLayer"></param>
        /// <param name="fGrowthProbability"></param>
        public void Init(byte nMaxLayer, float fGrowthProbability)
        {
            m_nMaxLayer = (nMaxLayer >= 1) ? nMaxLayer : (byte)1;
            m_fGrowthProbability = fGrowthProbability;

            m_HeadNode.Init(long.MinValue, m_nMaxLayer, null, this);
            m_TailNode.Init(long.MaxValue, m_nMaxLayer, null, this);

            byte nLayerIndex = m_nMaxLayer;
            while (nLayerIndex-- > 0)
            {
                m_HeadNode.SetNext(nLayerIndex, m_TailNode);
                m_TailNode.SetPrevious(nLayerIndex, m_HeadNode);

                m_NodeCountList.Add(0);
            }
        }

        /// <summary>
        /// Clear All Data. Once Clear, Please Init Before The Next Use!
        /// </summary>
        public void Clear()
        {
            RemoveAllNodes();
            m_NodeCountList.Clear();

            m_HeadNode.Clear();
            m_TailNode.Clear();
        }

        /// <summary>
        /// Remove And Destroy All Nodes.
        /// </summary>
        public void RemoveAllNodes()
        {
            if (m_nCount > 0)
            {
                Node currentNode = m_HeadNode.Next;
                while (currentNode != m_TailNode)
                {
                    if (currentNode != null)
                    {
                        Node cacheNode = currentNode;
                        currentNode = currentNode.Next;
                        ReferencePool.Release(cacheNode);
                    }
                    else
                    {
                        Log.Exception($"Fatal Error! Skip List Node Is Null!");
                        break;
                    }
                }

                byte nLayerIndex = m_nMaxLayer;
                while (nLayerIndex-- > 0)
                {
                    m_HeadNode.SetNext(nLayerIndex, m_TailNode);
                    m_TailNode.SetPrevious(nLayerIndex, m_HeadNode);

                    m_NodeCountList[nLayerIndex] = 0;
                }

                m_nLayerCount = 1;
                m_nCount = 0;
            }
        }

        private void InsertNode(Node node)
        {
            Node currentNode = m_HeadNode;
            byte nNodeLayerCount = node.LayerCount;
            byte nLayerIndex = m_nLayerCount;
            while (nLayerIndex-- > 0)
            {
                Node nextNode = currentNode.GetNext(nLayerIndex);
                while (nextNode.Value < node.Value)
                {
                    currentNode = nextNode;
                    nextNode = currentNode.GetNext(nLayerIndex);
                }

                if (nLayerIndex < nNodeLayerCount)
                {
                    currentNode.SetNext(nLayerIndex, node);
                    node.SetPrevious(nLayerIndex, currentNode);
                    nextNode.SetPrevious(nLayerIndex, node);
                    node.SetNext(nLayerIndex, nextNode);
                }
            }
        }

        private void CutNode(Node node)
        {
            byte nLayerIndex = node.LayerCount;
            while (nLayerIndex-- > 0)
            {
                Node previousNode = node.GetPrevious(nLayerIndex);
                Node nextNode = node.GetNext(nLayerIndex);
                if (previousNode != null)
                {
                    previousNode.SetNext(nLayerIndex, nextNode);
                }
                if (nextNode != null)
                {
                    nextNode.SetPrevious(nLayerIndex, previousNode);
                }
                node.SetPrevious(nLayerIndex, null);
                node.SetNext(nLayerIndex, null);
            }
        }

        /// <summary>
        /// Create And Add A New Node.
        /// </summary>
        /// <param name="value"></param>
        /// <param name="customObject"></param>
        /// <returns></returns>
        public Node AddNode(long value, Object customObject)
        {
            byte nNodeLayerCount = 1;
            while ((nNodeLayerCount < m_nMaxLayer) && (RandomGenerator.RandFloat01() < m_fGrowthProbability))
            {
                ++nNodeLayerCount;
            }
            Node newNode = ReferencePool.Acquire<Node>();
            newNode.Init(value, nNodeLayerCount, customObject, this);
            {
                ++m_nCount;

                if (newNode.LayerCount > m_nLayerCount)
                {
                    m_nLayerCount = newNode.LayerCount;
                }

                byte nLayerIndex = (byte)(newNode.LayerCount - 1);
                ++m_NodeCountList[nLayerIndex];
            }
            InsertNode(newNode);

            return newNode;
        }

        /// <summary>
        /// Remove And Destroy A Given Node.
        /// </summary>
        /// <param name="node"></param>
        public void RemoveNode(Node node)
        {
            if (node != null)
            {
                if (node.Owner == this)
                {
                    if ((node != m_HeadNode) && (node != m_TailNode))
                    {
                        CutNode(node);
                        {
                            byte nLayerIndex = (byte)(node.LayerCount - 1);
                            if (m_NodeCountList[nLayerIndex] > 0)
                            {
                                --m_NodeCountList[nLayerIndex];
                            }
                            else
                            {
                                Log.Exception($"Fatal Error! Skip List Node Count Should Not Be Zero!");
                            }

                            if ((node.LayerCount == m_nLayerCount) && (m_NodeCountList[nLayerIndex] == 0))
                            {
                                bool bFound = false;
                                while (nLayerIndex-- > 0)
                                {
                                    if (m_NodeCountList[nLayerIndex] > 0)
                                    {
                                        bFound = true;
                                        m_nLayerCount = (byte)(nLayerIndex + 1);
                                        break;
                                    }
                                }
                                if (!bFound)
                                {
                                    m_nLayerCount = 1;
                                }
                            }

                            if (m_nCount > 0)
                            {
                                --m_nCount;
                            }
                            else
                            {
                                Log.Exception($"Fatal Error! Skip List Count Should Not Be Zero!");
                            }
                        }
                        ReferencePool.Release(node);
                    }
                    else
                    {
                        Log.Error($"Skip List Remove Node Fail! The Node Is Head Or Tail!");
                    }
                }
                else
                {
                    Log.Error($"Skip List Remove Node Fail! The Node Is Not In The List!");
                }
            }
            else
            {
                Log.Error($"Skip List Remove Node Fail! The Node Is Null!");
            }
        }

        /// <summary>
        /// Move A Given Node To New Value.
        /// </summary>
        /// <param name="node"></param>
        /// <param name="newValue"></param>
        public void MoveNode(Node node, long newValue)
        {
            if (node != null)
            {
                if (node.Owner == this)
                {
                    if ((node != m_HeadNode) && (node != m_TailNode))
                    {
                        if ((newValue < node.Previous.Value) || (newValue > node.Next.Value))
                        {
                            CutNode(node);
                            node.Value = newValue;
                            InsertNode(node);
                        }
                        else
                        {
                            node.Value = newValue;
                        }
                    }
                    else
                    {
                        Log.Error($"Skip List Move Node Fail! The Node Is Head Or Tail!");
                    }
                }
                else
                {
                    Log.Error($"Skip List Move Node Fail! The Node Is Not In The List!");
                }
            }
            else
            {
                Log.Error($"Skip List Move Node Fail! The Node Is Null!");
            }
        }

        /// <summary>
        /// Search The Nearest Node Before Target Value.
        /// </summary>
        /// <param name="targetValue"></param>
        /// <returns></returns>
        public Node SearchNodeBefore(long targetValue)
        {
            if (targetValue == long.MinValue)
            {
                return m_HeadNode;
            }

            Node currentNode = m_HeadNode;
            byte nLayerIndex = m_nLayerCount;
            while (nLayerIndex-- > 0)
            {
                Node nextNode = currentNode.GetNext(nLayerIndex);
                while (nextNode.Value < targetValue)
                {
                    currentNode = nextNode;
                    nextNode = currentNode.GetNext(nLayerIndex);
                }
            }
            return currentNode;
        }

        /// <summary>
        /// Search The Nearest Node After Target Value.
        /// </summary>
        /// <param name="targetValue"></param>
        /// <returns></returns>
        public Node SearchNodeAfter(long targetValue)
        {
            if (targetValue == long.MaxValue)
            {
                return m_TailNode;
            }

            Node currentNode = m_HeadNode;
            byte nLayerIndex = m_nLayerCount;
            while (nLayerIndex-- > 0)
            {
                Node nextNode = currentNode.GetNext(nLayerIndex);
                while (nextNode.Value <= targetValue)
                {
                    currentNode = nextNode;
                    nextNode = currentNode.GetNext(nLayerIndex);
                }
            }
            return currentNode.Next;
        }
    }
}
