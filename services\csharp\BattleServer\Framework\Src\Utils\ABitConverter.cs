﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-3-20
//*********************************************************


using System;
using System.Buffers.Binary;

//由于客户端使用.net较低版本，这里重新实现做兼容

namespace Aurora.Framework
{
    public class BinaryPrimitivesEx
    {
        public static void WriteSingleLittleEndian(Span<byte> destination, float value)
        {
            int tempVal = BitConverter.SingleToInt32Bits(value);
            BinaryPrimitives.WriteInt32LittleEndian(destination, tempVal);
        }

        public static float ReadSingleLittleEndian(ReadOnlySpan<byte> source)
        {
            int tempVal = BinaryPrimitives.ReadInt32LittleEndian(source);
            return BitConverter.Int32BitsToSingle(tempVal);
        }

        public static void WriteDoubleLittleEndian(Span<byte> destination, double value)
        {
            long tempVal = BitConverter.DoubleToInt64Bits(value);
            BinaryPrimitives.WriteInt64LittleEndian(destination, tempVal);
        }

        public static double ReadDoubleLittleEndian(ReadOnlySpan<byte> source)
        {
            long tempVal = BinaryPrimitives.ReadInt64LittleEndian(source);
            return BitConverter.Int64BitsToDouble(tempVal);
        }
    }
}
