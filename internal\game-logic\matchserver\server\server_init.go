package server

import (
	"fmt"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	conf "liteframe/internal/common/config"
	"liteframe/internal/common/constant"
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/game-logic/matchserver/config"
	"liteframe/internal/game-logic/matchserver/nats_service"
	"liteframe/pkg/config_watcher/etcd_handler"
	"liteframe/pkg/registry/etcd"
	"liteframe/pkg/util/netutil"
	"liteframe/pkg/znats"

	clientv3 "go.etcd.io/etcd/client/v3"

	"liteframe/internal/game-logic/matchserver/global"

	"liteframe/pkg/log"
	"liteframe/pkg/util"
	"liteframe/pkg/uuid"

	"github.com/pkg/errors"
)

func (s *Server) loadConfig(name, commName string) error {
	s.conf.Config = &config.Config{}

	if err := util.LoadConfig(name, &s.conf.Config); err != nil {
		return errors.Wrap(err, fmt.Sprintf("load config %s failed", name))
	}
	s.conf.NewConfig = &conf.NewConfig{}
	if err := util.LoadConfig(commName, &s.conf.NewConfig); err != nil {
		return errors.Wrap(err, fmt.Sprintf("load common config %s failed", name))
	}
	// 使用配置文件中的server_id，与gameserver保持一致
	global.ServerID = strconv.Itoa(s.conf.ServerID)

	log.Info("matchserver config loaded",
		log.Kv("serverID", global.ServerID),
		log.Kv("csvPath", s.conf.CsvPath))

	return nil
}

func (s *Server) initRegister() error {
	timeout := s.conf.NewConfig.Config.Etcd.DialTimeout
	ttl := s.conf.NewConfig.Config.Etcd.TTL
	key := s.conf.NewConfig.Config.Etcd.Prefix

	reg, err := etcd.New(clientv3.Config{
		Endpoints:   s.conf.NewConfig.Config.Etcd.Endpoints,
		DialTimeout: time.Duration(timeout) * time.Second,
	},
		etcd.RegisterTTL(time.Second*time.Duration(ttl)),
		etcd.Namespace(key))

	if err != nil {
		return errors.Wrap(err, "etcd New failed")
	}
	s.reg = reg
	return nil
}

func (s *Server) initEtcdHandler() error {
	conf := s.conf.NewConfig.Config.Etcd
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   conf.Endpoints,
		DialTimeout: time.Duration(conf.DialTimeout) * time.Second,
	})
	if err != nil {
		return errors.Wrap(err, "initEtcdHandler")
	}
	s.etcdHandle = etcd_handler.New(cli)
	return nil
}

func (s *Server) initNatsRpcClient() error {
	client, err := znats.NewClient("match_client", znats.Config{
		Server:         s.conf.NewConfig.Nats.Servers,
		User:           s.conf.NewConfig.Nats.User,
		Pwd:            s.conf.NewConfig.Nats.Pwd,
		RequestTimeout: int32(s.conf.NewConfig.Nats.RequestTimeOut),
		ReconnectWait:  int32(s.conf.NewConfig.Nats.ReconnectWait),
		MaxReconnects:  int32(s.conf.NewConfig.Nats.MaxReconnects),
	})
	if err != nil {
		return err
	}

	s.matchServiceClient = natsrpc.NewNatsRpcMatchServiceClient(client)
	s.gameServiceClient = natsrpc.NewNatsRpcGameServiceClient(client)
	s.battleRpcClient = natsrpc.NewNatsRpcBattleServiceClient(client)
	global.GameRpcClient = s.gameServiceClient
	global.MatchRpcClient = s.matchServiceClient
	global.BattleRpcClient = s.battleRpcClient

	return nil
}

func (s *Server) initNatsRpcServer() error {
	server, err := znats.NewServer(string(constant.ServiceNameMatchServer), znats.Config{
		Server:         s.conf.NewConfig.Nats.Servers,
		User:           s.conf.NewConfig.Nats.User,
		Pwd:            s.conf.NewConfig.Nats.Pwd,
		RequestTimeout: int32(s.conf.NewConfig.Nats.RequestTimeOut),
		ReconnectWait:  int32(s.conf.NewConfig.Nats.ReconnectWait),
		MaxReconnects:  int32(s.conf.NewConfig.Nats.MaxReconnects),
	})
	if err != nil {
		return err
	}
	// init match service
	serverImpl, err := nats_service.NewMatchService()
	if err != nil {
		return err
	}
	s.matchServiceImpl = serverImpl
	s.rpcService = natsrpc.NewMatchServiceServerNatsRpcServer(server, serverImpl, global.ServerID)
	return nil
}

func (s *Server) initServer() error {
	// init log
	logConf := s.conf.Log
	logCfg := &log.Config{
		Console:     logConf.Console,
		FilePath:    logConf.FilePath,
		Level:       logConf.Level,
		Rotate:      logConf.Rotate,
		RotateSize:  logConf.RotateSize,
		RotateDaily: logConf.RotateDaily,
		Compress:    logConf.Compress,
		MaxAge:      logConf.MaxAge,
		MaxBackups:  logConf.MaxBackups,
	}

	logger, err := log.NewZapLogger(
		logCfg,
		log.WithConsoleEncoder(log.ConsoleEncoderConfig{
			TimeFormat:   "2006-01-02 15:04:05",
			DisableColor: false,
		}),
		log.WithFileEncoder(log.FileEncoderConfig{
			UseJSON:    true,
			TimeFormat: time.RFC3339,
		}),
		log.WithOutputLevels("DEBUG", "DEBUG"),
	)
	if err == nil {
		log.SetLogger(logger)
	}
	log.Info("log init success!")

	// init uuid generator
	// 使用服务器ID的哈希值作为节点ID
	nodeId := int64(netutil.NetAddrToUint64(global.ServerID)) % 4095 // 限制在12位范围内
	uuid.Init(nodeId)
	log.Info("uuid generator init success!", log.Kv("nodeId", nodeId))

	// init nats client
	if err := s.initNatsRpcClient(); err != nil {
		return err
	} else {
		log.Info("nats rpc client init success!")
	}

	// init nats
	if err := s.initNatsRpcServer(); err != nil {
		return err
	} else {
		log.Info("nats rpc server init success!")
	}

	// init etcd register
	if err := s.initRegister(); err != nil {
		return err
	} else {
		log.Info("register init success!")
	}

	// init etcd handler
	if err := s.initEtcdHandler(); err != nil {
		return err
	} else {
		log.Info("etcd handler init success!")
	}

	PidFile := constant.ServiceNameMatchServer + ".pid"
	pid := fmt.Sprintf("%d", os.Getpid())
	f, err := os.OpenFile(PidFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	err = os.WriteFile(PidFile, []byte(pid), 0)
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)
	}

	//register signal
	signal.Notify(s.sigCh, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	return nil
}
