#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableShopGiftPack
	{

		public static readonly string TName="ShopGiftPack.json";

		#region 属性定义
		/// <summary> 
		/// ID（当前第几期） 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 类型（0普通礼包，1限时礼包） 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 本期目标描述(语言包） 
		/// </summary> 
		public int DescID {get; set;}
		/// <summary> 
		/// 首次购买礼包掉落道具（掉落组ID） 
		/// </summary> 
		public int FirstDropGroupId {get; set;}
		/// <summary> 
		/// 多次购买礼包掉落道具（掉落组ID） 
		/// </summary> 
		public int DropGroupId {get; set;}
		/// <summary> 
		/// 刷新类型（-1,不刷新 0每日刷新时间点 1 周刷新 每周一 2固定时间段） 
		/// </summary> 
		public int RefreshType {get; set;}
		/// <summary> 
		/// 限时礼包开始时间 
		/// </summary> 
		public string StartDate {get; set;}
		/// <summary> 
		/// 限时礼包结束时间 
		/// </summary> 
		public string EndDate {get; set;}
		/// <summary> 
		/// 原价 
		/// </summary> 
		public int OriginalPrice {get; set;}
		/// <summary> 
		/// 现价 
		/// </summary> 
		public int NowPrice {get; set;}
		/// <summary> 
		/// 限购数量 -1不限购 
		/// </summary> 
		public int BuyLimit {get; set;}
		/// <summary> 
		/// 购买条件1(开服后几天) 
		/// </summary> 
		public int BuyCond1 {get; set;}
		/// <summary> 
		/// 购买条件2 任务进度 
		/// </summary> 
		public int BuyCond2 {get; set;}
		/// <summary> 
		/// 购买条件3 前置礼包id 
		/// </summary> 
		public int BuyCond3 {get; set;}
		#endregion

		public static TableShopGiftPack GetData(int ID)
		{
			return TableManager.ShopGiftPackData.Get(ID);
		}

		public static List<TableShopGiftPack> GetAllData()
		{
			return TableManager.ShopGiftPackData.GetAll();
		}

	}
	public sealed partial class TableShopGiftPackData
	{
		private Dictionary<int, TableShopGiftPack> dict = new Dictionary<int, TableShopGiftPack>();
		private List<TableShopGiftPack> dataList = new List<TableShopGiftPack>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableShopGiftPack.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableShopGiftPack>>(jsonContent);
			foreach (TableShopGiftPack config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableShopGiftPack Get(int id)
		{
			if (dict.TryGetValue(id, out TableShopGiftPack item))
				return item;
			return null;
		}

		public List<TableShopGiftPack> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
