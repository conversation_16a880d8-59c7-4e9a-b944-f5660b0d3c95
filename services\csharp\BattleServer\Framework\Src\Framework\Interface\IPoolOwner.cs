﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-10-24
//*********************************************************

using System.Collections.Generic;
namespace Aurora.Framework
{
    public interface IPoolOwner
    {
        int PoolType { get; }
        int DataID { get; }
        void SetPool(ParamPool pool);
        ParamPool GetPool();
        Dictionary<int, ParamPool> GetChildPoolList();

        void AddChildPool(ParamPool pool);
    }

    public interface IPoolContainer
    {
        void InitPoolEntity(PoolEntity entity, int index = -1);
        void AddPoolEntity(PoolEntity entity, int index = -1);
        void RemovePoolEntity(PoolEntity entity);
        void Update();
        // PoolEntity GetEntity(int index);
    }
}


