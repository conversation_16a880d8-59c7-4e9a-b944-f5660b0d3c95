package player

import (
	"context"
	"errors"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
	"strconv"
)

func (p *Player) matchResultHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(*natsrpc.MatchResultRequest)
	if !ok {
		return errors.New("data error")
	}

	if !data.Success {
		p.NotifyMatchResult(&cs.LCMatchSuccessNotify{
			Code: int32(error_code.ALREADY_INVITED),
		})
		log.Error("match failed", log.Kv("uid", p.U<PERSON>()))
		return nil
	}

	// 设置BattleServerId和BattleId
	p.battle.battleServerId = strconv.FormatInt(data.ServerId, 10)
	p.battle.battleId = data.BattleId
	log.Info("match success, set battleServerId and battleId",
		log.Kv("uid", p.Uid()),
		log.Kv("battleId", data.BattleId),
		log.Kv("battleServerId", p.battle.battleServerId),
		log.Kv("target", data.Target))

	// 为所有玩家计算并设置正确的血量
	playersWithHealth := make([]*public.PBBattlePlayerInfo, len(data.Target))
	copy(playersWithHealth, data.Target)

	// 计算所有玩家的血量（取最高杯段玩家的血量作为统一血量）
	battleHealth := p.calculateBattleHealth(playersWithHealth)

	// 为每个玩家设置血量
	for _, player := range playersWithHealth {
		player.Hp = int32(battleHealth)
	}

	log.Info("calculated battle health for all players",
		log.Kv("battleHealth", battleHealth),
		log.Kv("playerCount", len(playersWithHealth)),
		log.Kv("playersWithHealth", playersWithHealth))

	p.NotifyMatchResult(&cs.LCMatchSuccessNotify{
		Code:    int32(error_code.ERROR_OK),
		Players: playersWithHealth,
	})
	return nil
}

// calculateBattleHealth 计算战斗血量
// 根据策划要求：取最高杯段玩家血量作为所有玩家的血量
func (p *Player) calculateBattleHealth(players []*public.PBBattlePlayerInfo) int {
	if len(players) == 0 {
		return 3 // 默认血量
	}

	// 获取PlayMode配置
	playModeConfig := table.GetTable().TablePlayMode.GetById(1) // 使用ID=1的配置
	if playModeConfig == nil {
		log.Error("PlayMode config not found, using default health")
		return 3
	}
	if playModeConfig.PlayerHP == nil {
		log.Error("PlayerHP config is nil, using default health")
		return 3
	}

	log.Info("PlayMode config loaded successfully",
		log.Kv("configId", playModeConfig.ID),
		log.Kv("playerHPConfig", playModeConfig.PlayerHP))

	// 找到最高奖杯数
	maxTrophy := int32(0)
	for _, player := range players {
		if player.Throphy > maxTrophy {
			maxTrophy = player.Throphy
		}
	}

	// 根据最高奖杯数计算血量
	battleHealth := p.getHealthByTrophy(int(maxTrophy), playModeConfig.PlayerHP)

	log.Info("calculated battle health",
		log.Kv("maxTrophy", maxTrophy),
		log.Kv("battleHealth", battleHealth),
		log.Kv("playerTrophies", func() []int32 {
			trophies := make([]int32, len(players))
			for i, player := range players {
				trophies[i] = player.Throphy
			}
			return trophies
		}()))

	return battleHealth
}

// getHealthByTrophy 根据奖杯数获取玩家血量
// 配置格式：[[0,999,3],[1000,100000,4]] 表示0-999杯3血，1000-100000杯4血
// 超过上限取上限血量
func (p *Player) getHealthByTrophy(trophyCount int, playerHPConfig [][]int32) int {
	log.Info("getHealthByTrophy called",
		log.Kv("trophyCount", trophyCount),
		log.Kv("playerHPConfig", playerHPConfig))

	if playerHPConfig == nil {
		log.Error("playerHPConfig is nil")
		return 3
	}

	maxHealth := 3 // 默认血量

	for i, tier := range playerHPConfig {
		log.Info("checking tier",
			log.Kv("tierIndex", i),
			log.Kv("tier", tier),
			log.Kv("tierLength", len(tier)))

		if len(tier) >= 3 {
			// 如果奖杯数在当前区间内，直接返回
			if trophyCount >= int(tier[0]) && trophyCount <= int(tier[1]) {
				log.Info("trophy in range, returning health",
					log.Kv("trophyCount", trophyCount),
					log.Kv("rangeMin", tier[0]),
					log.Kv("rangeMax", tier[1]),
					log.Kv("health", tier[2]))
				return int(tier[2])
			}
			// 记录最高血量（用于超过上限的情况）
			if int(tier[2]) > maxHealth {
				maxHealth = int(tier[2])
			}
		}
	}

	// 超过所有配置上限时，返回最高血量
	log.Info("trophy not in any range, returning max health",
		log.Kv("trophyCount", trophyCount),
		log.Kv("maxHealth", maxHealth))
	return maxHealth
}

func (p *Player) battleStartHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(*natsrpc.RoundStartReq)
	if !ok {
		return errors.New("data error")
	}

	p.NotifyRoundStart(&cs.LCRoundStartNotify{
		Code:                 int32(error_code.ERROR_OK),
		Buffers:              data.Buffers,
		PlayerBoards:         data.PlayerBoards,
		TimeoutTimestamp:     data.TimeoutTimestamp,
		BuffTimeoutTimestamp: data.BuffTimeoutTimestamp,
		Skills:               data.Skills,
	})
	return nil
}

func (p *Player) battleRoundStartHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(*natsrpc.RoundBattleStartReq)
	if !ok {
		return errors.New("data error")
	}

	// 验证玩家是否应该在战斗中
	if p.battle.battleServerId == "" {
		log.Error("battleRoundStartHandler called but battleServerId is empty",
			log.Kv("uid", p.Uid()),
			log.Kv("incomingBattleId", data.BattleId),
			log.Kv("currentBattleId", p.battle.battleId))
		return errors.New("battleServerId not set")
	}

	// 验证battleId是否一致（如果已设置）
	if p.battle.battleId != 0 && p.battle.battleId != data.BattleId {
		log.Error("battleId mismatch in battleRoundStartHandler",
			log.Kv("uid", p.Uid()),
			log.Kv("currentBattleId", p.battle.battleId),
			log.Kv("incomingBattleId", data.BattleId))
		return errors.New("battleId mismatch")
	}

	// 设置或更新玩家的battleId
	p.battle.battleId = data.BattleId
	log.Info("player battle ID updated",
		log.Kv("uid", p.Uid()),
		log.Kv("battleId", data.BattleId),
		log.Kv("battleServerId", p.battle.battleServerId))

	p.NotifyBattleStart(&cs.LCRoundBattleStartNotify{
		Uid:              data.BattleId,
		Seed:             data.Seed,
		Team:             data.Team,
		TimeoutTimestamp: data.TimeoutTimestamp,
	})
	return nil
}

func (p *Player) battleRoundEndHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(*natsrpc.RoundBattleEndReq)
	if !ok {
		return errors.New("data error")
	}

	// 检查是否为最后回合
	if data.IsEnd {
		log.Info("received final round battle end notification, game will end soon",
			log.Kv("uid", p.Uid()),
			log.Kv("winUid", data.WinUid),
			log.Kv("loseUid", data.LoseUid),
			log.Kv("isEnd", data.IsEnd))
	}

	p.NotifyRoundBattleEnd(&cs.LCRoundBattleEndNotify{
		WinUid:           data.WinUid,
		LoseUid:          data.LoseUid,
		IsEnd:            data.IsEnd, // 传递最后回合标识给客户端
		TimeoutTimestamp: data.TimeoutTimestamp,
	})

	return nil
}

func (p *Player) battleEndHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(*natsrpc.BattleEndReq)
	if !ok {
		return errors.New("data error")
	}

	// 使用Battle模块处理战斗结束
	p.battle.ProcessBattleEnd(data)
	return nil
}
