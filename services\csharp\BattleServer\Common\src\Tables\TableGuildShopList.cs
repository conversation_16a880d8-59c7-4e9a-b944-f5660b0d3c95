#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGuildShopList
	{

		public static readonly string TName="GuildShopList.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 商店物品组ID 
		/// </summary> 
		public int Group {get; set;}
		/// <summary> 
		/// 花费金币类型 
		/// </summary> 
		public int CostType {get; set;}
		/// <summary> 
		/// 免费次数 
		/// </summary> 
		public int FreeCount {get; set;}
		/// <summary> 
		/// 广告次数 
		/// </summary> 
		public int ADCount {get; set;}
		/// <summary> 
		/// 购买次数 
		/// </summary> 
		public int BuyCount {get; set;}
		/// <summary> 
		/// 限购刷新周期 
		/// </summary> 
		public int RefreshType {get; set;}
		/// <summary> 
		/// 个人奖励 
		/// </summary> 
		public int DropGroupID {get; set;}
		/// <summary> 
		/// 价格 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 解锁条件（先不做） 
		/// </summary> 
		public int[] Condition {get; set;}
		/// <summary> 
		/// 广告id（先不做） 
		/// </summary> 
		public int AdId {get; set;}
		/// <summary> 
		/// 优惠（客户端展示） 
		/// </summary> 
		public int UpValue {get; set;}
		#endregion

		public static TableGuildShopList GetData(int ID)
		{
			return TableManager.GuildShopListData.Get(ID);
		}

		public static List<TableGuildShopList> GetAllData()
		{
			return TableManager.GuildShopListData.GetAll();
		}

	}
	public sealed partial class TableGuildShopListData
	{
		private Dictionary<int, TableGuildShopList> dict = new Dictionary<int, TableGuildShopList>();
		private List<TableGuildShopList> dataList = new List<TableGuildShopList>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGuildShopList.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGuildShopList>>(jsonContent);
			foreach (TableGuildShopList config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGuildShopList Get(int id)
		{
			if (dict.TryGetValue(id, out TableGuildShopList item))
				return item;
			return null;
		}

		public List<TableGuildShopList> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
