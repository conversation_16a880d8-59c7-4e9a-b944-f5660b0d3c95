package rpcimpl

import (
	"context"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/logic/player"
)

type treasureService struct {
	defaultService
}

func (t *treasureService) TreasureList(ctx context.Context, sender *player.Player, in *cs.CLTreasureListReq) (out *cs.LCTreasureListResp) {
	return sender.Treasure().HandleTreasureList()
}

func (t *treasureService) TreasureLevelUp(ctx context.Context, sender *player.Player, in *cs.CLTreasureLevelUpReq) (out *cs.LCTreasureLevelUpResp) {
	return sender.Treasure().HandleTreasureLevelUp(in.TreasureId)
}

func (t *treasureService) TreasureStarUp(ctx context.Context, sender *player.Player, in *cs.CLTreasureStarUpReq) (out *cs.LCTreasureStarUpResp) {
	return sender.Treasure().HandleTreasureStarUp(in.TreasureId)
}

func (t *treasureService) TreasureGacha(ctx context.Context, sender *player.Player, in *cs.CLTreasureGachaReq) (out *cs.LCTreasureGachaResp) {
	return sender.Treasure().HandleTreasureGacha(in)
}
