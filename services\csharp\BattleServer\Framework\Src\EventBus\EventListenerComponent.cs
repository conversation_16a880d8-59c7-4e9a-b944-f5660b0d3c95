﻿/*
 * @description: 用于处理跨对象的动态监听
 * @Auther: Kimsee
 * @Date: 2022-12-21 14:31
 * 
*/
using System;
using System.Collections.Generic;
using System.Linq;

namespace Aurora.Framework
{

    public class EventListenerComponent : BaseComponent, IAwake, IDestroy
    {
        public UnOrderMultiHashSet<long, long> m_Listeners = new UnOrderMultiHashSet<long, long>();
        public UnOrderMultiHashSet<long, long> m_Senders = new UnOrderMultiHashSet<long, long>();

        public override void OnLeaveSystem()
        {
            this.Reset();

            base.OnLeaveSystem();
        }
    }

    [ComponentSystem(typeof(EventListenerComponent))]
    public static class EventListenerComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(EventListenerComponent self)
        {
        }

        [MethodDestroy]
        public static void OnDestroy(EventListenerComponent self)
        {
            self.Reset();
        }

        public static void Reset(this EventListenerComponent self)
        {
            if (self == null) return;
            foreach (KeyValuePair<long, HashSet<long>> pair in self.m_Senders)
            {
                long eventID = pair.Key;
                HashSet<long> senderList = pair.Value;
                foreach (long senderID in senderList)
                {
                    Entity sender = self.GetOwnSystem().GetEntity(senderID);
                    if (sender != null)
                    {
                        EventListenerComponent senderComponent = sender.GetComponent<EventListenerComponent>();
                        if (senderComponent != null)
                        {
                            senderComponent.m_Listeners.Remove(eventID, self.ComponentParent.InstanceID);
                        }
                    }
                    else
                    {
                        Log.Error($"Event[{eventID}] Sender Is Not Found While Listener Is Leaving!");
                    }
                }
            }
            self.m_Senders.Clear();

            foreach (KeyValuePair<long, HashSet<long>> pair in self.m_Listeners)
            {
                long eventID = pair.Key;
                HashSet<long> listenerList = pair.Value;
                foreach (long listenerID in listenerList)
                {
                    Entity listener = self.GetOwnSystem().GetEntity(listenerID);
                    if (listener != null)
                    {
                        EventListenerComponent listenerComponent = listener.GetComponent<EventListenerComponent>();
                        if (listenerComponent != null)
                        {
                            listenerComponent.m_Senders.Remove(eventID, self.ComponentParent.InstanceID);
                        }
                    }
                    else
                    {
                        Log.Error($"Event[{eventID}] Listener Is Not Found While Sender Is Leaving!");
                    }
                }
            }
            self.m_Listeners.Clear();
        }

        public static bool CanTriggerEvent(this EventListenerComponent self, long evtId)
        {
            if (self == null) return false;
            return self.m_Listeners.ContainsKey(evtId);
        }

        public static void TriggerEvent<T>(this EventListenerComponent self, long evtId, T eventArg, Entity rSender)
        {
            if (self == null) return;

            foreach(var listenerId in self.m_Listeners[evtId])
            {
                self.GetOwnSystem().EventBus.SendToListener(eventArg, rSender, listenerId);
            }

            /*List<long> listenerList = self.m_Listeners[evtId].ToList();
            if (listenerList.Count > 0)
            {
                foreach (var listenerId in listenerList)
                {
                    self.GetOwnSystem().EventBus.SendToListener(eventArg, rSender, listenerId);
                }
            }*/
        }

        public static void AddListener<T>(this EventListenerComponent self, Entity rListener) where T : EventParamBase, new()
        {
            if (self == null || rListener == null) return;
            long eventId = EventBus.GetEventID<T>();
            if (eventId < 0)
            {
                Log.Error($"EventListenerComponentSystem AddListener Error eventId:[{eventId}], listenerId:[{rListener.InstanceID}]");
                return;
            }
            if (self.m_Listeners.Contains(eventId, rListener.InstanceID))
            {
                Log.Error($"EventListenerComponentSystem AddListener duplicated eventId:[{eventId}], listenerId:[{rListener.InstanceID}]");
                return;
            }

            EventListenerComponent listenerComponent = rListener.GetComponent<EventListenerComponent>();
            if (listenerComponent != null)
            {
                self.m_Listeners.Add(eventId, rListener.InstanceID);
                listenerComponent.m_Senders.Add(eventId, self.ComponentParent.InstanceID);
            }
            else
            {
                Log.Error($"EventListenerComponentSystem AddListener Error! Listener Entity Also Requires EventListenerComponent. EventID:[{eventId}].");
            }
        }

        public static void RemoveListener<T>(this EventListenerComponent self, long listenerId) where T : EventParamBase, new()
        {
            if (self == null) return;
            long eventId = EventBus.GetEventID<T>();
            if (eventId < 0)
            {
                Log.Error($"EventListenerComponentSystem RemoveListener Error eventId:[{eventId}], listenerId:[{listenerId}]");
                return;
            }
            Entity rListener = self.GetOwnSystem().GetEntity(listenerId);
            if (rListener == null)
            {
                Log.Error($"EventListenerComponentSystem RemoveListener Error! Entity is null . EventID:[{eventId}]. ListenerID:[{listenerId}]");
                return;
            }
            EventListenerComponent listenerComponent = rListener.GetComponent<EventListenerComponent>();
            if (listenerComponent != null)
            {
                self.m_Listeners.Remove(eventId, listenerId);
                listenerComponent.m_Senders.Remove(eventId, self.ComponentParent.InstanceID);
            }
            else
            {
                Log.Error($"EventListenerComponentSystem RemoveListener Error! Listener Entity Also Requires EventListenerComponent. EventID:[{eventId}].");
            }
        }

        public static void AddClientListener(this EventListenerComponent self, long evtID, IEventDynamicCallback cb, Entity rSender)
        {
            if (self == null || rSender == null) return;
            if (!self.m_Listeners.Contains(evtID, rSender.InstanceID))
            {
                self.m_Listeners.Add(evtID, rSender.InstanceID);
            }
            self.GetOwnSystem().EventBus.AddClientListener(evtID,cb);
        }

        public static void RemoveClientListener(this EventListenerComponent self, long evtID, IEventDynamicCallback cb, Entity rSender)
        {
            if (self == null || rSender == null) return;
            if (!self.m_Listeners.Contains(evtID, rSender.InstanceID))
            {
                Log.Error($"EventListenerComponentSystem RemoveListener duplicated eventId:[{evtID}], listenerId:[{rSender.InstanceID}]");
                return;
            }
            self.GetOwnSystem().EventBus.RemoveClientListener(evtID, cb);
            if(self.GetOwnSystem().EventBus.GetClientListenerCount(evtID) <= 0)
            {
                self.m_Listeners.Remove(evtID, rSender.InstanceID);
            }
        }
    }
}
