#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableFunctionUnlock
	{

		public static readonly string TName="FunctionUnlock.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 入口名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 活动ID(暂时关联UI表，如果不想跳转配置-1) 
		/// </summary> 
		public int ActivityID {get; set;}
		/// <summary> 
		/// 开启条件（-1.默认；0.等级；1.关卡，2任务，3未开放） 
		/// </summary> 
		public int Conditions {get; set;}
		/// <summary> 
		/// 参数ID 
		/// </summary> 
		public int StepID {get; set;}
		/// <summary> 
		/// 公司等级 
		/// </summary> 
		public int company {get; set;}
		/// <summary> 
		/// 指挥官等级 
		/// </summary> 
		public int CommanderLevel {get; set;}
		/// <summary> 
		/// 开服时间 秒 
		/// </summary> 
		public int OpenServerTime {get; set;}
		/// <summary> 
		/// 军衔等级 
		/// </summary> 
		public int MilitaryRankLevel {get; set;}
		/// <summary> 
		/// 功能图标 
		/// </summary> 
		public string Icon {get; set;}
		/// <summary> 
		/// 特效播放延迟(除100) 
		/// </summary> 
		public int particalDelay {get; set;}
		/// <summary> 
		/// 特效id对应EffectRes表 
		/// </summary> 
		public int effectID {get; set;}
		/// <summary> 
		/// 是否展示解锁弹窗 
		/// </summary> 
		public int showTip {get; set;}
		/// <summary> 
		/// 循环特效id对应EffectRes表 
		/// </summary> 
		public int loopEffectID {get; set;}
		/// <summary> 
		/// unlock  1生效；-1不生效 
		/// </summary> 
		public int unlock {get; set;}
		#endregion

		public static TableFunctionUnlock GetData(int ID)
		{
			return TableManager.FunctionUnlockData.Get(ID);
		}

		public static List<TableFunctionUnlock> GetAllData()
		{
			return TableManager.FunctionUnlockData.GetAll();
		}

	}
	public sealed partial class TableFunctionUnlockData
	{
		private Dictionary<int, TableFunctionUnlock> dict = new Dictionary<int, TableFunctionUnlock>();
		private List<TableFunctionUnlock> dataList = new List<TableFunctionUnlock>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableFunctionUnlock.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableFunctionUnlock>>(jsonContent);
			foreach (TableFunctionUnlock config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableFunctionUnlock Get(int id)
		{
			if (dict.TryGetValue(id, out TableFunctionUnlock item))
				return item;
			return null;
		}

		public List<TableFunctionUnlock> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
