/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableDropGroup struct {
	// ============= 变量定义 =============
	// 掉落包ID
	ID int32
	// 最大掉落数量
	DropNum int32
	// 每个掉落是否可重复(0不可 1可以)
	IsCanDuplicate int32
	// 掉落道具
	DropBoxID []int32
	// 掉落权重
	DropWeight []int32
}




// TableDropGroupData 表格
type TableDropGroupData struct {
	file    string
	dataMap map[int32]*TableDropGroup
	Data    []*TableDropGroup
	md5     string
}

// load 加载
func (tb *TableDropGroupData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableDropGroup{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableDropGroup, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableDropGroup)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableDropGroup, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableDropGroupData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableDropGroup{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableDropGroup, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableDropGroup)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableDropGroupData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableDropGroupData) GetById(id int32) *TableDropGroup {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableDropGroupData) GetCloneById(id int32) *TableDropGroup {
	v := tb.dataMap[id]
	out := &TableDropGroup{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableDropGroupData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableDropGroupData) Foreach(call func(*TableDropGroup) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableDropGroupData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableDropGroupData) Clone() ITable {
	ntb := &TableDropGroupData{
		file:    tb.file,
		dataMap: make(map[int32]*TableDropGroup),
		Data:    make([]*TableDropGroup, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableDropGroup{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
