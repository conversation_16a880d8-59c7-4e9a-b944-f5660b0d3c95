﻿using System;

public class HermiteCurve
{
    public const float DEFAULT_WEIGHT = 1.0f / 3;

    private const float MIN_TIME_SPAN = 0.001f;
    private const float MIN_WEIGHT = 0;
    private const float MAX_WEIGHT = 1;
    private const float COEFFICIENT_EPSILON = 0.001f;

    private float _timeSpan;

    private float _timeEquationA;
    private float _timeEquationB;
    private float _timeEquationC;

    private enum EquationDegree
    {
        Constant = 0,
        Linear = 1,
        Quadratic = 2,
        Cubic = 3
    }
    private EquationDegree _timeEquationDegree;

    private float _timeCubicRemainder;
    private float _timeCubicP;
    private float _timeCubicPCube;
    private float _timeCubicPartOfQ;
    private float _timeCubicR;
    private float _timeCubicRCubeRoot;

    private float _valueEquationA;
    private float _valueEquationB;
    private float _valueEquationC;
    private float _valueEquationD;

    public HermiteCurve(float timeSpan, float beginValue, float endValue, float beginSpeed, float endSpeed, float beginWeight = DEFAULT_WEIGHT, float endWeight = DEFAULT_WEIGHT)
    {
        _timeSpan = (timeSpan > MIN_TIME_SPAN) ? timeSpan : MIN_TIME_SPAN;

        beginWeight = Math.Clamp(beginWeight, MIN_WEIGHT, MAX_WEIGHT);
        endWeight = Math.Clamp(endWeight, MIN_WEIGHT, MAX_WEIGHT);

        _timeEquationA = (beginWeight * 3) + (endWeight * 3) - 2;
        _timeEquationB = (beginWeight * -6) + (endWeight * -3) + 3;
        _timeEquationC = beginWeight * 3;

        if (MathF.Abs(_timeEquationA) > COEFFICIENT_EPSILON)
        {
            _timeEquationDegree = EquationDegree.Cubic;

            _timeCubicRemainder = -_timeEquationB / (_timeEquationA * 3);
            _timeCubicP = ((_timeEquationA * _timeEquationC * 3) - (_timeEquationB * _timeEquationB)) / (_timeEquationA * _timeEquationA * 9);
            _timeCubicPCube = _timeCubicP * _timeCubicP * _timeCubicP;
            _timeCubicPartOfQ = ((_timeEquationA * _timeEquationB * _timeEquationC * -9) + (_timeEquationB * _timeEquationB * _timeEquationB * 2)) / (_timeEquationA * _timeEquationA * _timeEquationA * 54);

            if (_timeCubicPCube < 0)
            {
                _timeCubicR = MathF.Sqrt(-_timeCubicPCube);
                _timeCubicRCubeRoot = MathF.Cbrt(_timeCubicR);
            }
        }
        else if (MathF.Abs(_timeEquationB) > COEFFICIENT_EPSILON)
        {
            _timeEquationDegree = EquationDegree.Quadratic;
        }
        else if (MathF.Abs(_timeEquationC) > COEFFICIENT_EPSILON)
        {
            _timeEquationDegree = EquationDegree.Linear;
        }
        else
        {
            _timeEquationDegree = EquationDegree.Constant;
        }

        float beginCorrectedSpeed = beginSpeed * _timeSpan * (beginWeight * 3);
        float endCorrectedSpeed = endSpeed * _timeSpan * (endWeight * 3);

        _valueEquationA = (beginValue * 2) + (endValue * -2) + beginCorrectedSpeed + endCorrectedSpeed;
        _valueEquationB = (beginValue * -3) + (endValue * 3) + (beginCorrectedSpeed * -2) + (endCorrectedSpeed * -1);
        _valueEquationC = beginCorrectedSpeed;
        _valueEquationD = beginValue;
    }

    public float Calculate(float time)
    {
        float hiddenVariable;
        {
            float correctedTime = Math.Clamp((time / _timeSpan), 0, 1);
            hiddenVariable = correctedTime;
            float timeEquationD = -correctedTime;

            if (_timeEquationDegree == EquationDegree.Cubic)
            {
                float timeCubicQ = _timeCubicPartOfQ + timeEquationD / (_timeEquationA * 2);
                float timeCubicDelta = _timeCubicPCube + (timeCubicQ * timeCubicQ);
                if (timeCubicDelta < 0)
                {
                    float timeCubicTheta = MathF.Acos(-timeCubicQ / _timeCubicR) / 3;
                    float root_0 = MathF.Cos(timeCubicTheta) * _timeCubicRCubeRoot * 2 + _timeCubicRemainder;
                    float root_1 = MathF.Cos(timeCubicTheta + (MathF.PI * 2) / 3) * _timeCubicRCubeRoot * 2 + _timeCubicRemainder;
                    float root_2 = MathF.Cos(timeCubicTheta - (MathF.PI * 2) / 3) * _timeCubicRCubeRoot * 2 + _timeCubicRemainder;

                    if ((root_0 >= 0) && (root_0 <= 1))
                    {
                        hiddenVariable = root_0;
                    }
                    else if ((root_1 >= 0) && (root_1 <= 1))
                    {
                        hiddenVariable = root_1;
                    }
                    else if ((root_2 >= 0) && (root_2 <= 1))
                    {
                        hiddenVariable = root_2;
                    }
                }
                else
                {
                    float timeCubicDeltaSquareRoot = MathF.Sqrt(timeCubicDelta);
                    float root_R = MathF.Cbrt(-timeCubicQ + timeCubicDeltaSquareRoot) + MathF.Cbrt(-timeCubicQ - timeCubicDeltaSquareRoot) + _timeCubicRemainder;

                    if ((root_R >= 0) && (root_R <= 1))
                    {
                        hiddenVariable = root_R;
                    }
                }
            }
            else if (_timeEquationDegree == EquationDegree.Quadratic)
            {
                float timeQuadraticDelta = (_timeEquationC * _timeEquationC) - (_timeEquationB * timeEquationD * 4);
                if (timeQuadraticDelta >= 0)
                {
                    float timeQuadraticDeltaSquareRoot = MathF.Sqrt(timeQuadraticDelta);
                    float root_0 = (-_timeEquationC - timeQuadraticDeltaSquareRoot) / (_timeEquationB * 2);
                    float root_1 = (-_timeEquationC + timeQuadraticDeltaSquareRoot) / (_timeEquationB * 2);

                    if ((root_0 >= 0) && (root_0 <= 1))
                    {
                        hiddenVariable = root_0;
                    }
                    else if ((root_1 >= 0) && (root_1 <= 1))
                    {
                        hiddenVariable = root_1;
                    }
                }
            }

            hiddenVariable = Math.Clamp(hiddenVariable, 0, 1);
        }

        float value;
        {
            float hiddenVariableSquare = hiddenVariable * hiddenVariable;
            float hiddenVariableCube = hiddenVariableSquare * hiddenVariable;
            value = hiddenVariableCube * _valueEquationA + hiddenVariableSquare * _valueEquationB + hiddenVariable * _valueEquationC + _valueEquationD;
        }

        return value;
    }
}
