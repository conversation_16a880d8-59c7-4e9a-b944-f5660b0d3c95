#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGiftCode
	{

		public static readonly string TName="GiftCode.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropBox {get; set;}
		/// <summary> 
		/// 兑换结束时间(-1为不限制结束时间) 
		/// </summary> 
		public string closeTime {get; set;}
		#endregion

		public static TableGiftCode GetData(int ID)
		{
			return TableManager.GiftCodeData.Get(ID);
		}

		public static List<TableGiftCode> GetAllData()
		{
			return TableManager.GiftCodeData.GetAll();
		}

	}
	public sealed partial class TableGiftCodeData
	{
		private Dictionary<int, TableGiftCode> dict = new Dictionary<int, TableGiftCode>();
		private List<TableGiftCode> dataList = new List<TableGiftCode>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGiftCode.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGiftCode>>(jsonContent);
			foreach (TableGiftCode config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGiftCode Get(int id)
		{
			if (dict.TryGetValue(id, out TableGiftCode item))
				return item;
			return null;
		}

		public List<TableGiftCode> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
