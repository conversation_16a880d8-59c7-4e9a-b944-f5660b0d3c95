﻿//逻辑耗时检测脚本
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;

namespace Aurora.Framework
{
    public static class GameAnalysisFramework
    {
        public enum MethodAnalysis
        {
            Common = 0,
            Tick = 1,
            MsgCallBack = 2,
        }

        public delegate void StartWatch(string str);
        public delegate void StopWatch(string str, MethodAnalysis type);
        public delegate void BeginSampleDele(string str);
        public delegate void EndSampleDele();
        public delegate bool GetAnalysisIsOpen();
        private static StartWatch startWatchTime = null;
        private static StopWatch stopWatchTime = null;
        private static BeginSampleDele beginSample = null;
        private static EndSampleDele endSample = null;
        
        public static bool isRunning = false;
        public static void Init(bool funcState)
		{
            if (funcState)
            {
                isRunning = true;
                Assembly assembly = Assembly.Load("Core");
                Type gameAnalysis = assembly.GetType("Aurora.Game.GameAnalysis");
                MethodInfo startWatchTimemethodInfo = gameAnalysis.GetMethod("StartWatchTime", BindingFlags.Static | BindingFlags.Public);
                MethodInfo stopWatchTimeMethodInfo = gameAnalysis.GetMethod("StopFrameworkWatchTime", BindingFlags.Static | BindingFlags.Public);
                MethodInfo beginSampleMethodInfo = gameAnalysis.GetMethod("SampleBegin", BindingFlags.Static | BindingFlags.Public);
                MethodInfo endSampleMethodInfo = gameAnalysis.GetMethod("SampleEnd", BindingFlags.Static | BindingFlags.Public);
                startWatchTime = (StartWatch)Delegate.CreateDelegate(typeof(StartWatch), null, startWatchTimemethodInfo);
                stopWatchTime = (StopWatch)Delegate.CreateDelegate(typeof(StopWatch), null, stopWatchTimeMethodInfo);
                beginSample = (BeginSampleDele)Delegate.CreateDelegate(typeof(BeginSampleDele), null, beginSampleMethodInfo);
                endSample = (EndSampleDele)Delegate.CreateDelegate(typeof(EndSampleDele), null, endSampleMethodInfo);
            }
            else
            {
                isRunning = false;
            }
        }

        public static void StartWatchTime(string str)
        {
            if (!isRunning)
                return;
            if (startWatchTime != null)
            {
                startWatchTime(str);
            }
        }

        public static void StopWatchTime(string str, MethodAnalysis type)
        {
            if (!isRunning)
                return;
            if (stopWatchTime != null)
            {
                stopWatchTime(str, type);
            }

        }

        public static void BeginSample(string str)
        {
            if (!isRunning)
                return;
            if (beginSample != null)
            {
                beginSample(str);
            }
        }

        public static void EndSample()
        {
            if (!isRunning)
                return;
            if(endSample != null)
            {
                endSample();
            }
        }
    }
}
