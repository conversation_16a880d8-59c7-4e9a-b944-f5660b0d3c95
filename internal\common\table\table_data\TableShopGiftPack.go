/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableShopGiftPack struct {
	// ============= 变量定义 =============
	// ID（当前第几期）
	ID int32
	// 类型（0普通礼包，1限时礼包）
	Type int32
	// 本期目标描述(语言包）
	DescID int32
	// 首次购买礼包掉落道具（掉落组ID）
	FirstDropGroupId int32
	// 多次购买礼包掉落道具（掉落组ID）
	DropGroupId int32
	// 刷新类型（-1,不刷新 0每日刷新时间点 1 周刷新 每周一 2固定时间段）
	RefreshType int32
	// 限时礼包开始时间
	StartDate string
	// 限时礼包结束时间
	EndDate string
	// 原价
	OriginalPrice int32
	// 现价
	NowPrice int32
	// 限购数量 -1不限购
	BuyLimit int32
	// 购买条件1(开服后几天)
	BuyCond1 int32
	// 购买条件2 任务进度
	BuyCond2 int32
	// 购买条件3 前置礼包id
	BuyCond3 int32
}




// TableShopGiftPackData 表格
type TableShopGiftPackData struct {
	file    string
	dataMap map[int32]*TableShopGiftPack
	Data    []*TableShopGiftPack
	md5     string
}

// load 加载
func (tb *TableShopGiftPackData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableShopGiftPack{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableShopGiftPack, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableShopGiftPack)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableShopGiftPack, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableShopGiftPackData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableShopGiftPack{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableShopGiftPack, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableShopGiftPack)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableShopGiftPackData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableShopGiftPackData) GetById(id int32) *TableShopGiftPack {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableShopGiftPackData) GetCloneById(id int32) *TableShopGiftPack {
	v := tb.dataMap[id]
	out := &TableShopGiftPack{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableShopGiftPackData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableShopGiftPackData) Foreach(call func(*TableShopGiftPack) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableShopGiftPackData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableShopGiftPackData) Clone() ITable {
	ntb := &TableShopGiftPackData{
		file:    tb.file,
		dataMap: make(map[int32]*TableShopGiftPack),
		Data:    make([]*TableShopGiftPack, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableShopGiftPack{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
