﻿
using BattleServer.config;
using BattleServer.Game;
using BattleServer.Nats;
using BattleServer.Server;
using BattleServer.Service;
using Game.Core;
using Aurora.Framework;
using LiteFrame.Game;
using LiteFrame.Helper;
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using YamlDotNet.Core;

namespace BattleServer.Framework
{
    public class SceneManager : GlobalManager<SceneManager>
    {
        
        // 管理AutoChessScene实例
        //private readonly ConcurrentDictionary<long, AutoChessScene> _autoChessScenes = new ConcurrentDictionary<long, AutoChessScene>();

        // 战斗状态管理（从BattleService移过来）
        private readonly ConcurrentDictionary<long, BattleState> _battles = new ConcurrentDictionary<long, BattleState>();
        private readonly ConcurrentDictionary<long, long> _playerToBattle = new ConcurrentDictionary<long, long>(); // 玩家ID到战斗ID的映射

        // 战斗玩家进入状态跟踪（从BattleService移过来）
        private readonly ConcurrentDictionary<long, HashSet<long>> _battlePlayerEntered = new ConcurrentDictionary<long, HashSet<long>>();
        private readonly ConcurrentDictionary<long, List<long>> _battleAllPlayers = new ConcurrentDictionary<long, List<long>>();
        private readonly ConcurrentDictionary<long, long> _battleCreateTime = new ConcurrentDictionary<long, long>();



        // 线程安全的battleId生成器
        private static long _battleIdCounter = 0;

        // 超时检查相关
        private int _timeoutCheckAccumulator = 0;
        private const int TIMEOUT_CHECK_INTERVAL_MS = 5000; // 每5秒检查一次超时

        private ushort m_nLastSceneID = 0;
        private Dictionary<ushort, Scene> m_SceneList = new Dictionary<ushort, Scene>();

        // 玩家所在的场景
        private Dictionary<ulong, ushort> mPlayerScene = new Dictionary<ulong, ushort>();

        private SessionComponent m_SessionComponet;

        private MessageHandlerComponent messageHandlerComponent;

        //private NatsClient m_NatsClient;


        public SceneManager()
        {
            LogName = $"GlobalManager_SceneManager";
        }

        public override void OnDestroy()
        {
            Release();

            base.OnDestroy();
        }

        public bool Init()
        {
            //m_NatsClient = natsClient;

            InitEntitySystem();
            //Test();


            return true;
        }

        private void Test()
        {
            


            //test
            for (ushort i = 0; i < 100; i++)
            {
                ushort nSceneID = GenerateSceneID();
                if (nSceneID != 0)
                {
                    Scene scene = SceneFactory.CreateScene(1001, nSceneID);
                    if (!m_SceneList.TryAdd(nSceneID, scene))
                    {
                        Log.Error($"SceneManager._InitSceneCommonData, !m_SceneList.TryAdd(nSceneID, scene), ");
                        return;
                    }

                    List<PBBattlePlayerInfo> players = new List<PBBattlePlayerInfo>();
                    PBBattlePlayerInfo player1 = new PBBattlePlayerInfo();
                    player1.Uid = 100001;
                    player1.Name = "Player1";
                    player1.ServerId = "10101";
                    player1.Level = 10;
                    player1.Throphy = 1;
                    player1.Hp = 3;
                    players.Add(player1);

                    PBBattlePlayerInfo player2 = new PBBattlePlayerInfo();
                    player2.Uid = 100002;
                    player2.Name = "Player2";
                    player2.ServerId = "10101";
                    player2.Level = 10;
                    player2.Throphy = 1;
                    player2.Hp = 3;
                    players.Add(player2);

                    PBBattlePlayerInfo player3 = new PBBattlePlayerInfo();
                    player3.Uid = 100003;
                    player3.Name = "Player3";
                    player3.ServerId = "10101";
                    player3.Level = 10;
                    player3.Throphy = 1;
                    player3.Hp = 3;
                    players.Add(player3);

                    PBBattlePlayerInfo player4 = new PBBattlePlayerInfo();
                    player4.Uid = 100004;
                    player4.Name = "Player4";
                    player4.ServerId = "10101";
                    player4.Level = 10;
                    player4.Throphy = 1;
                    player4.Hp = 3;
                    players.Add(player4);


                    List<PBBattleTeamInfo> teams = new List<PBBattleTeamInfo>();
                    PBBattleTeamInfo team1 = new PBBattleTeamInfo();
                    team1.Heros.Add(new PBBattleHeroInfo { Id = 401, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team1.Heros.Add(new PBBattleHeroInfo { Id = 402, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team1.Heros.Add(new PBBattleHeroInfo { Id = 403, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team1.Heros.Add(new PBBattleHeroInfo { Id = 404, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team1.Heros.Add(new PBBattleHeroInfo { Id = 405, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    teams.Add(team1);

                    PBBattleTeamInfo team2 = new PBBattleTeamInfo();
                    team2.Heros.Add(new PBBattleHeroInfo { Id = 401, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team2.Heros.Add(new PBBattleHeroInfo { Id = 402, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team2.Heros.Add(new PBBattleHeroInfo { Id = 403, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team2.Heros.Add(new PBBattleHeroInfo { Id = 404, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team2.Heros.Add(new PBBattleHeroInfo { Id = 405, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    teams.Add(team2);

                    PBBattleTeamInfo team3 = new PBBattleTeamInfo();
                    team3.Heros.Add(new PBBattleHeroInfo { Id = 401, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team3.Heros.Add(new PBBattleHeroInfo { Id = 402, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team3.Heros.Add(new PBBattleHeroInfo { Id = 403, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team3.Heros.Add(new PBBattleHeroInfo { Id = 404, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team3.Heros.Add(new PBBattleHeroInfo { Id = 405, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    teams.Add(team3);

                    PBBattleTeamInfo team4 = new PBBattleTeamInfo();
                    team4.Heros.Add(new PBBattleHeroInfo { Id = 401, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team4.Heros.Add(new PBBattleHeroInfo { Id = 402, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team4.Heros.Add(new PBBattleHeroInfo { Id = 403, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team4.Heros.Add(new PBBattleHeroInfo { Id = 404, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    team4.Heros.Add(new PBBattleHeroInfo { Id = 405, Level = 1, AwakeLevel = 0, StarLevel = 1 });
                    teams.Add(team4);

                    
                    scene.InitPlayers(players, teams);
                    //ThreadManager.Instance.StartWorkUnit(scene.WorkUnit, ThreadType.COMMON_THREAD);
                }
            }
        }

        public bool Release()
        {
            ClearEntitySystem();
            DestroyAllScenes();

            return true;
        }

        public bool InitEntitySystem()
        {
            //m_RootEntity = ReferencePool.Acquire<Entity>();
            //WorkUnit.EntitySystem.AddRootEntity(m_RootEntity);

            //m_RootEntity.AddComponent<CopySceneComponent>();
            MessageAddress address = new MessageAddress();
            address.SetAsGlobalMgr(0, 0, 1);
            //m_RootEntity.AddComponent<SessionComponent>(address);
            //m_RootEntity.AddComponent<TwinTreeSeasonPlayComponent>();
            //m_RootEntity.AddComponent<SceneMatchComponent>();

            MessageIDComponent messageIDComponent = new MessageIDComponent();
            MessageIDComponentSystem.OnAwake(messageIDComponent);


            messageHandlerComponent = new MessageHandlerComponent();
            //MessageHandlerComponent.Instance = messageHandlerComponent;
            MessageHandlerComponentSystem.OnAwake(messageHandlerComponent);


            m_SessionComponet = new SessionComponent();
            SessionComponentSystem.OnAwake1(m_SessionComponet, address);

            IPacketDispatcher packetDispatcher = new ServerSessionDispatcher();
            PacketDispatcherManager.Instance.SetDispatcher(packetDispatcher);

            return true;
        }
        public bool ClearEntitySystem()
        {
            //WorkUnit.EntitySystem.RemoveRootEntity(m_RootEntity);
            //m_RootEntity.Release();
            //m_RootEntity = null;

            return true;
        }

        public override void Tick(int nDeltaTime)
        {
            base.Tick(nDeltaTime);

            for (int i = 0; i < m_SceneList.Count; i++)
            {
                try
                {
                    var scene = m_SceneList.ElementAt(i).Value;
                    scene.OnUpdate(nDeltaTime);
                }
                catch (Exception ex)
                {
                    Log.Error($"[SceneManager] Scene {m_SceneList.ElementAt(i).Key} tick error: {ex.Message}");
                    Log.Error($"[SceneManager] Stack trace: {ex.StackTrace}");
                }
            }

            //// 更新所有AutoChessScene
            //var scenesToRemove = new List<long>();
            //foreach (var scene in _autoChessScenes.Values)
            //{
            //    try
            //    {
            //        scene.Tick(nDeltaTime);
            //    }
            //    catch (Exception ex)
            //    {
            //        // 检查是否是BattleStateManager未初始化的错误
            //        if (ex.Message.Contains("BattleStateManager is not initialized"))
            //        {
            //            Log.Warning($"[SceneManager] AutoChessScene {scene.BattleId} has uninitialized BattleStateManager, marking for removal");
            //            scenesToRemove.Add(scene.BattleId);
            //        }
            //        else
            //        {
            //            Log.Error($"[SceneManager] AutoChessScene {scene.BattleId} tick error: {ex.Message}");
            //            Log.Error($"[SceneManager] Stack trace: {ex.StackTrace}");
            //        }
            //    }
            //}

            //// 移除有问题的场景
            //foreach (var battleId in scenesToRemove)
            //{
            //    Log.Info($"[SceneManager] Removing problematic AutoChessScene {battleId}");
            //    RemoveAutoChessScene(battleId);
            //}

            //// 定期检查EnterBattle超时（每5秒检查一次）
            //_timeoutCheckAccumulator += nDeltaTime;
            //if (_timeoutCheckAccumulator >= TIMEOUT_CHECK_INTERVAL_MS)
            //{
            //    _timeoutCheckAccumulator = 0;
            //    try
            //    {
            //        //BattleServer.Service.BattleService.CheckEnterBattleTimeouts();
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.Error($"[SceneManager] EnterBattle timeout check error: {ex.Message}");
            //    }
            //}

            ////Log.Debug($"Tick {Thread.CurrentThread.ManagedThreadId.ToString()}");
        }

        public override void OnStart()
        {
            base.OnStart();

            Log.Debug("SceneManager Start");
        }

        public override void OnStop()
        {
            base.OnStop();

            //// 清理所有AutoChessScene
            //foreach (var scene in _autoChessScenes.Values)
            //{
            //    try
            //    {
            //        scene.Dispose();
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.Error($"[SceneManager] Error disposing AutoChessScene {scene.BattleId}: {ex.Message}");
            //    }
            //}
            //_autoChessScenes.Clear();

            Log.Debug("SceneManager Stop");
        }

        ///// <summary>
        ///// 创建AutoChessScene - 包含完整的战斗创建逻辑
        ///// </summary>
        //public long CreateAutoChessScene(List<PBBattlePlayerInfo> players, List<PBBattleTeamInfo> teams)
        //{
        //    try
        //    {
        //        var serverIdStr = config.ConfigHelper.Config.ServerID.ToString();

        //        // 生成唯一的battleId：时间戳 + 递增计数器
        //        var battleId = (Environment.TickCount64 << 16) + System.Threading.Interlocked.Increment(ref _battleIdCounter);

        //        // 检查battleId是否重复
        //        if (!_battles.TryAdd(battleId, BattleState.StateNone))
        //        {
        //            Log.Warning($"[SceneManager] Duplicate battleId {battleId}");
        //            return 0;
        //        }

        //        // 提取玩家数据和阵容数据
        //        var playerIds = new List<long>();
        //        var playerLineups = new Dictionary<long, List<int>>();
        //        var playerHeroInfos = new Dictionary<long, List<PBBattleHeroInfo>>();
        //        var playerServerIds = new Dictionary<long, string>();
        //        var playerBasicInfos = new Dictionary<long, (string name, int level, int trophy)>();

        //        for (int i = 0; i < players.Count; i++)
        //        {
        //            var player = players[i];
        //            var team = teams[i];
        //            var playerId = (long)player.Uid;

        //            playerIds.Add(playerId);

        //            // 延迟清理策略：创建新战斗时清理玩家的旧映射
        //            CleanupPlayerPreviousBattleMapping(playerId);

        //            // 强制确保玩家状态清理完成
        //            ForceCleanupPlayerState(playerId);

        //            // 从PBBattleTeamInfo中提取英雄阵容
        //            var lineup = new List<int>();
        //            var heroInfos = new List<PBBattleHeroInfo>();
        //            if (team?.Heros != null)
        //            {
        //                foreach (var hero in team.Heros)
        //                {
        //                    lineup.Add(hero.Id);
        //                    heroInfos.Add(new PBBattleHeroInfo
        //                    {
        //                        Id = hero.Id,
        //                        Level = hero.Level,
        //                        StarLevel = 1,
        //                        AwakeLevel = hero.AwakeLevel
        //                    });
        //                }
        //            }

        //            playerLineups[playerId] = lineup;
        //            playerHeroInfos[playerId] = heroInfos;
        //            playerServerIds[playerId] = player.ServerId ?? "unknown";
        //            playerBasicInfos[playerId] = (
        //                name: player.Name ?? $"Player{playerId}",
        //                level: player.Level,
        //                trophy: player.Throphy
        //            );

        //            // 建立玩家到战斗的映射（线程安全）
        //            _playerToBattle.TryAdd(playerId, battleId);

        //            Log.Info($"[SceneManager] Player {playerId} ({player.Name}) Level {player.Level} Trophy {player.Throphy} from server {player.ServerId} lineup: [{string.Join(", ", lineup)}]");
        //        }

        //        // 创建AutoChessScene但不启动状态机
        //        var scene = new AutoChessScene(serverIdStr);
        //        scene.InitBattleWithoutStart(battleId, playerIds, playerLineups, playerHeroInfos, playerServerIds, playerBasicInfos);

        //        // 初始化玩家进入状态跟踪
        //        _battlePlayerEntered.TryAdd(battleId, new HashSet<long>());
        //        _battleAllPlayers.TryAdd(battleId, new List<long>(playerIds));
        //        _battleCreateTime.TryAdd(battleId, Environment.TickCount64);

        //        // 添加到SceneManager进行线程管理
        //        AddAutoChessScene(scene);

        //        Log.Info($"[SceneManager] Battle {battleId} created successfully with {playerIds.Count} players");
        //        Log.Info($"[SceneManager] Battle {battleId} is now waiting for all players to call EnterBattle RPC");

        //        // 立即让机器人进入战斗，不等待超时
        //        AutoEnterBotsImmediately(battleId, playerIds);

        //        return battleId;
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] CreateAutoChessScene error: {ex.Message}");
        //        return 0;
        //    }
        //}

        ///// <summary>
        ///// 添加AutoChessScene到管理器
        ///// </summary>
        //public void AddAutoChessScene(AutoChessScene scene)
        //{
        //    if (scene == null) return;

        //    if (_autoChessScenes.TryAdd(scene.BattleId, scene))
        //    {
        //        scene.LogName = $"AutoChessScene_{scene.BattleId}";
        //        Log.Info($"[SceneManager] Added AutoChessScene {scene.BattleId} without WorkUnit (single-thread mode)");
        //    }
        //    else
        //    {
        //        Log.Warning($"[SceneManager] AutoChessScene {scene.BattleId} already exists");
        //    }
        //}

        ///// <summary>
        ///// 从管理器移除AutoChessScene
        ///// </summary>
        //public void RemoveAutoChessScene(long battleId)
        //{
        //    if (_autoChessScenes.TryRemove(battleId, out var scene))
        //    {
        //        try
        //        {
        //            // 先清理WorkUnit
        //            if (scene.WorkUnit != null)
        //            {
        //                Log.Info($"[SceneManager] Stopping WorkUnit for AutoChessScene {battleId}");
        //                (scene as IWorkUnitOwner).ClearWorkUnit();
        //            }

        //            // 然后Dispose场景
        //            scene.Dispose();

        //            Log.Info($"[SceneManager] Removed AutoChessScene {battleId} from thread management with WorkUnit cleanup");
        //        }
        //        catch (Exception ex)
        //        {
        //            Log.Error($"[SceneManager] Error disposing AutoChessScene {battleId}: {ex.Message}");
        //        }
        //    }
        //}

        ///// <summary>
        ///// 获取AutoChessScene
        ///// </summary>
        //public AutoChessScene GetAutoChessScene(long battleId)
        //{
        //    _autoChessScenes.TryGetValue(battleId, out var scene);
        //    return scene;
        //}

        ///// <summary>
        ///// 根据玩家ID获取AutoChessScene
        ///// </summary>
        //public AutoChessScene GetAutoChessSceneByPlayerId(long playerId)
        //{
        //    // 首先尝试从映射中查找
        //    if (_playerToBattle.TryGetValue(playerId, out long battleId))
        //    {
        //        return GetAutoChessScene(battleId);
        //    }

        //    // 映射丢失时尝试恢复：遍历所有场景查找玩家
        //    Log.Warning($"[SceneManager] Player {playerId} mapping not found, attempting recovery");

        //    foreach (var kvp in _autoChessScenes)
        //    {
        //        var scene = kvp.Value;
        //        var scenePlayerIds = scene.GetAllPlayerIds();

        //        if (scenePlayerIds.Contains(playerId))
        //        {
        //            Log.Info($"[SceneManager] Found player {playerId} in battle {kvp.Key}, restoring mapping");

        //            // 恢复映射
        //            _playerToBattle.TryAdd(playerId, kvp.Key);
        //            return scene;
        //        }
        //    }

        //    Log.Warning($"[SceneManager] Player {playerId} not found in any AutoChessScene");
        //    return null;
        //}

        ///// <summary>
        ///// 获取活跃的AutoChessScene数量
        ///// </summary>
        //public int GetAutoChessSceneCount()
        //{
        //    return _autoChessScenes.Count;
        //}

        ///// <summary>
        ///// 更新战斗状态
        ///// </summary>
        //public void UpdateBattleState(long battleId, BattleState newState)
        //{
        //    if (_battles.ContainsKey(battleId))
        //    {
        //        _battles[battleId] = newState;
        //        Log.Info($"[SceneManager] Updated battle {battleId} state to {newState}");
        //    }
        //}

        ///// <summary>
        ///// 获取战斗状态
        ///// </summary>
        //public BattleState GetBattleState(long battleId)
        //{
        //    return _battles.GetValueOrDefault(battleId, BattleState.StateNone);
        //}

        ///// <summary>
        ///// 清理被淘汰玩家的映射关系（玩家被淘汰后立即清理，因为不会再收到任何消息）
        ///// </summary>
        //public void RemoveEliminatedPlayerMapping(long playerId)
        //{
        //    try
        //    {
        //        if (_playerToBattle.TryRemove(playerId, out long battleId))
        //        {
        //            Log.Info($"[SceneManager] Removed eliminated player {playerId} from battle {battleId} mapping");
        //        }
        //        else
        //        {
        //            Log.Info($"[SceneManager] Player {playerId} was not found in battle mapping (already removed)");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] Error removing eliminated player {playerId}: {ex.Message}");
        //    }
        //}



        ///// <summary>
        ///// 清理已结束的战斗
        ///// </summary>
        //public void CleanupBattle(long battleId)
        //{
        //    Log.Info($"[SceneManager] Starting cleanup for battle {battleId}");

        //    // 线程安全地移除战斗状态
        //    if (_battles.TryRemove(battleId, out _))
        //    {
        //        Log.Info($"[SceneManager] Removed battle state for {battleId}");
        //    }

        //    // 清理玩家进入状态跟踪
        //    _battlePlayerEntered.TryRemove(battleId, out _);
        //    _battleAllPlayers.TryRemove(battleId, out _);
        //    _battleCreateTime.TryRemove(battleId, out _);

        //    // 线程安全地移除Scene
        //    if (_autoChessScenes.TryRemove(battleId, out var scene))
        //    {
        //        // 清理玩家映射
        //        var playerIds = scene.GetAllPlayerIds();
        //        foreach (var playerId in playerIds)
        //        {
        //            _playerToBattle.TryRemove(playerId, out _);
        //        }

        //        // Dispose场景
        //        try
        //        {
        //            scene.Dispose();
        //            Log.Info($"[SceneManager] Cleaned up scene for battle {battleId}");
        //        }
        //        catch (Exception ex)
        //        {
        //            Log.Error($"[SceneManager] Error disposing scene {battleId}: {ex.Message}");
        //        }
        //    }
        //    else
        //    {
        //        Log.Warning($"[SceneManager] Scene for battle {battleId} not found during cleanup");
        //    }

        //    Log.Info($"[SceneManager] Cleanup completed for battle {battleId}");
        //}

        ///// <summary>
        ///// 根据玩家ID清理战斗 - 用于玩家登出时清理
        ///// </summary>
        //public void CleanupBattleByPlayerId(long playerId)
        //{
        //    try
        //    {
        //        // 验证玩家ID
        //        if (playerId <= 0)
        //        {
        //            Log.Warning($"[SceneManager] Invalid player ID in CleanupBattleByPlayerId: {playerId}");
        //            return;
        //        }

        //        // 查找玩家所在的战斗
        //        if (_playerToBattle.TryGetValue(playerId, out long battleId))
        //        {
        //            Log.Info($"[SceneManager] Found player {playerId} in battle {battleId}, starting cleanup");

        //            // 获取场景
        //            if (_autoChessScenes.TryGetValue(battleId, out var scene))
        //            {
        //                // 通知场景该玩家离开
        //                scene.HandlePlayerLeave_Internal(playerId);

        //                var battleState = GetBattleState(battleId);
        //                if (battleState == BattleState.StateGameOver)
        //                {
        //                    // 战斗已结束，清理整个战斗
        //                    Log.Info($"[SceneManager] Battle {battleId} is over, cleaning up entire battle");
        //                    CleanupBattle(battleId);
        //                }
        //                else
        //                {
        //                    // 战斗未结束，只移除玩家映射
        //                    Log.Info($"[SceneManager] Battle {battleId} not finished, only removing player mapping");
        //                    _playerToBattle.TryRemove(playerId, out _);
        //                }
        //            }
        //            else
        //            {
        //                Log.Warning($"[SceneManager] Scene for battle {battleId} not found, removing player mapping only");
        //                _playerToBattle.TryRemove(playerId, out _);
        //            }
        //        }
        //        else
        //        {
        //            Log.Info($"[SceneManager] Player {playerId} not found in any battle mapping");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] Error in CleanupBattleByPlayerId for player {playerId}: {ex.Message}");
        //    }
        //}

        ///// <summary>
        ///// 创建新战斗时清理玩家的旧战斗映射
        ///// </summary>
        //private void CleanupPlayerPreviousBattleMapping(long playerId)
        //{
        //    try
        //    {
        //        if (_playerToBattle.TryGetValue(playerId, out long oldBattleId))
        //        {
        //            Log.Info($"[SceneManager] Player {playerId} creating new battle, cleaning up previous battle {oldBattleId}");

        //            // 先移除玩家映射
        //            _playerToBattle.TryRemove(playerId, out _);

        //            // 检查旧战斗是否还存在
        //            if (_autoChessScenes.TryGetValue(oldBattleId, out var oldScene))
        //            {
        //                var battleState = GetBattleState(oldBattleId);
        //                Log.Info($"[SceneManager] Previous battle {oldBattleId} state: {battleState}");

        //                // 统计旧战斗中的真实玩家数量
        //                var allPlayerIds = oldScene.GetAllPlayerIds();
        //                var realPlayerCount = 0;
        //                foreach (var pid in allPlayerIds)
        //                {
        //                    if (_playerToBattle.ContainsKey(pid))
        //                    {
        //                        realPlayerCount++;
        //                    }
        //                }

        //                // 如果旧战斗只有这一个真实玩家，清理整个战斗
        //                if (realPlayerCount <= 1)
        //                {
        //                    Log.Info($"[SceneManager] Previous battle {oldBattleId} only has player {playerId}, cleaning up entire battle");
        //                    CleanupBattle(oldBattleId);
        //                }
        //                else
        //                {
        //                    // 多个真实玩家，只通知场景该玩家离开
        //                    Log.Info($"[SceneManager] Previous battle {oldBattleId} has other real players, only removing player {playerId}");
        //                    oldScene.HandlePlayerLeave_Internal(playerId);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] Error in CleanupPlayerPreviousBattleMapping for player {playerId}: {ex.Message}");
        //    }
        //}

        ///// <summary>
        ///// 强制清理玩家状态，确保没有残留映射
        ///// </summary>
        //private void ForceCleanupPlayerState(long playerId)
        //{
        //    try
        //    {
        //        // 强制移除玩家映射，无论是否存在
        //        if (_playerToBattle.TryRemove(playerId, out long removedBattleId))
        //        {
        //            Log.Info($"[SceneManager] Force removed player {playerId} from battle {removedBattleId} mapping");
        //        }

        //        // 检查所有战斗场景，确保玩家不在任何场景中
        //        var scenesToCheck = _autoChessScenes.Values.ToList();
        //        foreach (var scene in scenesToCheck)
        //        {
        //            try
        //            {
        //                var allPlayerIds = scene.GetAllPlayerIds();
        //                if (allPlayerIds.Contains(playerId))
        //                {
        //                    Log.Warning($"[SceneManager] Found player {playerId} in scene {scene.BattleId}, forcing cleanup");
        //                    scene.HandlePlayerLeave_Internal(playerId);
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                Log.Error($"[SceneManager] Error checking scene {scene.BattleId} for player {playerId}: {ex.Message}");
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] Error in ForceCleanupPlayerState for player {playerId}: {ex.Message}");
        //    }
        //}

        ///// <summary>
        ///// 验证并修复映射一致性 - 用于诊断和修复映射丢失问题
        ///// </summary>
        //public void ValidateAndRepairMappings()
        //{
        //    try
        //    {
        //        Log.Info($"[SceneManager] Starting mapping consistency validation");

        //        var repairedCount = 0;
        //        var orphanedMappings = 0;

        //        // 检查场景中的玩家是否都有映射
        //        foreach (var kvp in _autoChessScenes)
        //        {
        //            var battleId = kvp.Key;
        //            var scene = kvp.Value;
        //            var playerIds = scene.GetAllPlayerIds();

        //            foreach (var playerId in playerIds)
        //            {
        //                if (!_playerToBattle.ContainsKey(playerId))
        //                {
        //                    Log.Warning($"[SceneManager] Found player {playerId} in scene {battleId} without mapping, repairing");
        //                    _playerToBattle.TryAdd(playerId, battleId);
        //                    repairedCount++;
        //                }
        //            }
        //        }

        //        // 检查映射中的玩家是否都在对应场景中
        //        var mappingsToRemove = new List<long>();
        //        foreach (var kvp in _playerToBattle)
        //        {
        //            var playerId = kvp.Key;
        //            var battleId = kvp.Value;

        //            if (!_autoChessScenes.TryGetValue(battleId, out var scene))
        //            {
        //                Log.Warning($"[SceneManager] Found orphaned mapping: player {playerId} -> battle {battleId} (scene not found)");
        //                mappingsToRemove.Add(playerId);
        //                orphanedMappings++;
        //            }
        //            else
        //            {
        //                var playerIds = scene.GetAllPlayerIds();
        //                if (!playerIds.Contains(playerId))
        //                {
        //                    Log.Warning($"[SceneManager] Found orphaned mapping: player {playerId} -> battle {battleId} (player not in scene)");
        //                    mappingsToRemove.Add(playerId);
        //                    orphanedMappings++;
        //                }
        //            }
        //        }

        //        // 清理孤立的映射
        //        foreach (var playerId in mappingsToRemove)
        //        {
        //            _playerToBattle.TryRemove(playerId, out _);
        //        }

        //        Log.Info($"[SceneManager] Mapping validation completed: repaired {repairedCount}, removed {orphanedMappings} orphaned mappings");
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] Error during mapping validation: {ex.Message}");
        //    }
        //}

        ///// <summary>
        ///// 处理玩家进入战斗 - 统一的入口点，确保状态一致性
        ///// </summary>
        //public EnterBattleResp HandlePlayerEnterBattle(long playerId)
        //{
        //    try
        //    {
        //        Log.Info($"[SceneManager] Player {playerId} is entering battle");

        //        // 查找玩家所在的战斗
        //        if (!_playerToBattle.TryGetValue(playerId, out long battleId))
        //        {
        //            Log.Info($"[SceneManager] Player {playerId} not found in any active battle for EnterBattle");
        //            return new EnterBattleResp { Code = 0 }; // 返回成功，允许玩家进入新战斗
        //        }

        //        // 获取战斗场景
        //        if (!_autoChessScenes.TryGetValue(battleId, out var scene))
        //        {
        //            Log.Error($"[SceneManager] Battle scene {battleId} not found for player {playerId}");
        //            return new EnterBattleResp { Code = -2 };
        //        }

        //        // 获取玩家进入状态跟踪
        //        if (!_battlePlayerEntered.TryGetValue(battleId, out var enteredPlayers))
        //        {
        //            Log.Error($"[SceneManager] Battle {battleId} player tracking not found");
        //            return new EnterBattleResp { Code = -3 };
        //        }

        //        // 获取当前战斗状态
        //        var currentBattleState = GetBattleState(battleId);

        //        // 检查玩家是否已经进入过
        //        lock (enteredPlayers)
        //        {
        //            // 如果是第一次进入（StateNone状态）
        //            if (currentBattleState == BattleState.StateNone)
        //            {
        //                if (enteredPlayers.Contains(playerId))
        //                {
        //                    Log.Warning($"[SceneManager] Player {playerId} already entered battle {battleId} in initial phase");
        //                    return new EnterBattleResp { Code = 0 }; // 重复进入不算错误
        //                }

        //                // 标记玩家已进入
        //                enteredPlayers.Add(playerId);

        //                // 通知AutoChessScene玩家进入
        //                var sceneResult = scene.HandlePlayerEnter(playerId);
        //                if (sceneResult.Code != 0)
        //                {
        //                    // 如果场景处理失败，回滚状态
        //                    enteredPlayers.Remove(playerId);
        //                    return sceneResult;
        //                }

        //                Log.Info($"[SceneManager] Player {playerId} entered battle {battleId} (initial), current count: {enteredPlayers.Count}");

        //                // 检查是否所有玩家都已进入
        //                if (_battleAllPlayers.TryGetValue(battleId, out var allPlayers) &&
        //                    enteredPlayers.Count >= allPlayers.Count)
        //                {
        //                    // 所有玩家都已进入，启动战斗状态机
        //                    Log.Info($"[SceneManager] All {allPlayers.Count} players entered battle {battleId}, starting battle state machine");
        //                    Log.Info($"[SceneManager] Entered players: [{string.Join(", ", enteredPlayers)}]");
        //                    Log.Info($"[SceneManager] Triggering AutoChessScene.StartBattleStateMachine() for battle {battleId}");

        //                    scene.StartBattleStateMachine();

        //                    // 更新战斗状态
        //                    UpdateBattleState(battleId, BattleState.StateRoundStart);
        //                    Log.Info($"[SceneManager] Battle state updated to StateRoundStart for battle {battleId}");
        //                }
        //                else
        //                {
        //                    var totalPlayers = allPlayers?.Count ?? 0;
        //                    Log.Info($"[SceneManager] Waiting for more players to enter battle {battleId}: {enteredPlayers.Count}/{totalPlayers}");
        //                    if (allPlayers != null)
        //                    {
        //                        var waitingPlayers = allPlayers.Where(p => !enteredPlayers.Contains(p)).ToList();
        //                        Log.Info($"[SceneManager] Still waiting for players: [{string.Join(", ", waitingPlayers)}]");
        //                    }
        //                }
        //            }
        //            else
        //            {
        //                // 只有在StateRoundSettlement状态下才处理回合确认
        //                if (currentBattleState == BattleState.StateRoundSettlement)
        //                {
        //                    Log.Info($"[SceneManager] Player {playerId} confirming round in battle {battleId}");
        //                    // 这里可以添加回合确认逻辑
        //                }
        //                else
        //                {
        //                    Log.Warning($"[SceneManager] Player {playerId} tried to enter battle {battleId} in invalid state: {currentBattleState}");
        //                    return new EnterBattleResp { Code = -4 }; // 状态错误
        //                }
        //            }
        //        }

        //        return new EnterBattleResp { Code = 0 };
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] HandlePlayerEnterBattle error for player {playerId}: {ex.Message}");
        //        return new EnterBattleResp { Code = -99 };
        //    }
        //}

        ///// <summary>
        ///// 检查玩家是否属于指定战斗
        ///// </summary>
        //public bool IsPlayerInBattle(long playerId, long battleId)
        //{
        //    try
        //    {
        //        if (_playerToBattle.TryGetValue(playerId, out long currentBattleId))
        //        {
        //            var result = currentBattleId == battleId;
        //            if (!result)
        //            {
        //                Log.Warning($"[SceneManager] Player {playerId} is in battle {currentBattleId}, not in requested battle {battleId}");
        //            }
        //            return result;
        //        }

        //        Log.Warning($"[SceneManager] Player {playerId} is not in any battle, requested battle {battleId}");
        //        return false;
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] Error checking if player {playerId} is in battle {battleId}: {ex.Message}");
        //        return false;
        //    }
        //}

        ///// <summary>
        ///// 自动让机器人进入战斗 - 立即执行，不等待超时
        ///// </summary>
        //private void AutoEnterBotsImmediately(long battleId, List<long> playerIds)
        //{
        //    try
        //    {
        //        Log.Info($"[SceneManager] Auto-entering bots for battle {battleId}");

        //        // 假设第一个玩家是真实玩家，其他都是机器人
        //        var realPlayerId = playerIds[0];
        //        var botPlayerIds = playerIds.Skip(1).ToList();

        //        Log.Info($"[SceneManager] Real player: {realPlayerId}, Bot players: [{string.Join(", ", botPlayerIds)}]");

        //        // 立即让机器人进入，使用统一的HandlePlayerEnterBattle方法
        //        foreach (var botId in botPlayerIds)
        //        {
        //            var result = HandlePlayerEnterBattle(botId);
        //            if (result.Code == 0)
        //            {
        //                Log.Info($"[SceneManager] Bot {botId} auto-entered battle {battleId}");
        //            }
        //            else
        //            {
        //                Log.Warning($"[SceneManager] Bot {botId} failed to enter battle {battleId}, code: {result.Code}");
        //            }
        //        }

        //        Log.Info($"[SceneManager] Bot auto-entry completed for battle {battleId}");
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error($"[SceneManager] Error in AutoEnterBotsImmediately for battle {battleId}: {ex.Message}");
        //    }
        //}

        private ushort GenerateSceneID()
        {
            int nTryCount = ushort.MaxValue;
            while (--nTryCount >= 0)
            {
                ushort nSceneID = ++m_nLastSceneID;
                if (nSceneID == 0)
                {
                    continue;
                }
                else if (m_SceneList.ContainsKey(nSceneID))
                {
                    continue;
                }
                else
                {
                    return nSceneID;
                }
            }
            throw new Exception($"Generate Scene ID Fail!");
        }

        public Session GetSession()
        {
            return m_SessionComponet.m_Session;
        }


        public ushort CreateScene(int configId, List<PBBattlePlayerInfo> players, List<PBBattleTeamInfo> teams)
        {
            ushort nSceneID = 0;
            var sceneList = TablePlayMode.GetAllData();

            foreach(var ele in sceneList)
            {
                if (ele.Scene != configId)
                    continue;

                nSceneID = GenerateSceneID();
                if (nSceneID != 0)
                {
                    Scene scene = SceneFactory.CreateScene(1001, nSceneID);
                    scene.PlayMode = ele;
                    if (!m_SceneList.TryAdd(nSceneID, scene))
                    {
                        Log.Error($"SceneManager._InitSceneCommonData, !m_SceneList.TryAdd(nSceneID, scene), ");
                        return 0;
                    }

                    scene.InitPlayers(players, teams);
                    //ThreadManager.Instance.StartWorkUnit(scene.WorkUnit, ThreadType.COMMON_THREAD);
                }

                //if (nSceneID != 0)
                //{
                //    Scene scene = new Scene();
                //    if (!m_SceneList.TryAdd(nSceneID, scene))
                //    {
                //        Log.Error($"SceneManager._InitSceneCommonData, !m_SceneList.TryAdd(nSceneID, scene), ");

                //        return 0;
                //    }

                //    scene.OnCreate(nSceneID, configId, players, teams);
                //    ThreadManager.Instance.StartWorkUnit(scene.WorkUnit, ThreadType.COMMON_THREAD);
                //}
            }

            for (int i = 0; i<players.Count; i++)
            {
                mPlayerScene.Add(players[i].Uid, nSceneID);
            }
            return nSceneID;
        }

        public void DestoryScene(ushort scendId)
        {
            Log.Debug($"[SceneManager] DestoryScene scendId {scendId}");
            Scene scene;
            m_SceneList.TryGetValue(scendId, out scene);
            if (scene == null)
            {
                Log.Error($"[SceneManager] DestoryScene scendId {scendId} not find");
                return;
            }

            //ThreadManager.Instance.StopWorkUnit(scene.WorkUnit, ThreadType.COMMON_THREAD);
            m_SceneList.Remove(scendId);

            ////test
            //if (m_SceneList.Count == 0)
            //{
            //    Test();
            //}
        }

        private void DestroyAllScenes()
        {
            foreach (Scene scene in m_SceneList.Values)
            {
                //ThreadManager.Instance.StopWorkUnit(scene.WorkUnit, ThreadType.COMMON_THREAD);
            }
            m_SceneList.Clear();
        }


        public Scene GetScene(ushort nID)
        {
            Scene scene = null;
            if (m_SceneList.TryGetValue(nID, out scene))
            {
                return scene;
            }
            else
            {
                return null;
            }
        }

        public ushort GetPlayerSceneID(ulong uid)
        {
            ushort sceneID = 0;
            mPlayerScene.TryGetValue(uid, out sceneID);

            return sceneID;
        }

        public void RemovePlayerScene(ulong uid)
        {
            if (mPlayerScene.ContainsKey(uid))
            {
                mPlayerScene.Remove(uid);
            }
            else
            {
                Log.Warning($"[SceneManager] RemovePlayerScene: Player {uid} not found in scene mapping.");
            }
        }
    }
}
