#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMission
	{

		public static readonly string TName="Mission.json";

		#region 属性定义
		/// <summary> 
		/// 任务id(不可复用) 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 条件描述 
		/// </summary> 
		public int Conditiondesc {get; set;}
		/// <summary> 
		/// MissionType枚举 
		/// </summary> 
		public int TaskType {get; set;}
		/// <summary> 
		/// TaskConditionType枚举 
		/// </summary> 
		public int ConditionType {get; set;}
		/// <summary> 
		/// 参数（一级分割用| 二级分割用&）（和程序协商配置） 
		/// </summary> 
		public string Param {get; set;}
		/// <summary> 
		/// 完成计数 
		/// </summary> 
		public int TotalCount {get; set;}
		/// <summary> 
		/// 奖励id 
		/// </summary> 
		public int RewadId {get; set;}
		/// <summary> 
		/// 额外参数（每日任务，参数1对应活跃宝箱增长） 
		/// </summary> 
		public int[] ExtraParam {get; set;}
		/// <summary> 
		/// 跳转ID 
		/// </summary> 
		public int JumpId {get; set;}
		/// <summary> 
		/// 弱引导点击（只配功能的） 
		/// </summary> 
		public int weakGuideClick {get; set;}
		/// <summary> 
		/// 弱引导点击停留（第一位带点任务的）初始状态（未完成状态） 
		/// </summary> 
		public int weakGuideStay {get; set;}
		/// <summary> 
		/// 下一个任务 
		/// </summary> 
		public int Next {get; set;}
		#endregion

		public static TableMission GetData(int ID)
		{
			return TableManager.MissionData.Get(ID);
		}

		public static List<TableMission> GetAllData()
		{
			return TableManager.MissionData.GetAll();
		}

	}
	public sealed partial class TableMissionData
	{
		private Dictionary<int, TableMission> dict = new Dictionary<int, TableMission>();
		private List<TableMission> dataList = new List<TableMission>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMission.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMission>>(jsonContent);
			foreach (TableMission config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMission Get(int id)
		{
			if (dict.TryGetValue(id, out TableMission item))
				return item;
			return null;
		}

		public List<TableMission> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
