#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableHeroLevel
	{

		public static readonly string TName="HeroLevel.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 英雄等级 
		/// </summary> 
		public int HeroLv {get; set;}
		/// <summary> 
		/// 英雄品质 
		/// </summary> 
		public int QualityId {get; set;}
		/// <summary> 
		/// 升级消耗:神话消耗为碎片，其他为同名角色 
		/// </summary> 
		public int Cost {get; set;}
		/// <summary> 
		/// 升级消耗神石 
		/// </summary> 
		public int[] CostItem {get; set;}
		/// <summary> 
		/// 金币消耗 
		/// </summary> 
		public int GoldCost {get; set;}
		/// <summary> 
		/// 觉醒等级 
		/// </summary> 
		public int WakeUpLV {get; set;}
		#endregion

		public static TableHeroLevel GetData(int ID)
		{
			return TableManager.HeroLevelData.Get(ID);
		}

		public static List<TableHeroLevel> GetAllData()
		{
			return TableManager.HeroLevelData.GetAll();
		}

	}
	public sealed partial class TableHeroLevelData
	{
		private Dictionary<int, TableHeroLevel> dict = new Dictionary<int, TableHeroLevel>();
		private List<TableHeroLevel> dataList = new List<TableHeroLevel>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableHeroLevel.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableHeroLevel>>(jsonContent);
			foreach (TableHeroLevel config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableHeroLevel Get(int id)
		{
			if (dict.TryGetValue(id, out TableHeroLevel item))
				return item;
			return null;
		}

		public List<TableHeroLevel> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
