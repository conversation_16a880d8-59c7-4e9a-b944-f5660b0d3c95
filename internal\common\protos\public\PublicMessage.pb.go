// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.2
// source: PublicMessage.proto

package public

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//玩家基础数据
type PBPlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID            int64              `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"`                                     //id
	NickName              string             `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`                                          //名字
	HeadIcon              string             `protobuf:"bytes,3,opt,name=headIcon,proto3" json:"headIcon,omitempty"`                                          //头像
	Level                 int32              `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`                                               //等级
	FrameId               int32              `protobuf:"varint,5,opt,name=FrameId,proto3" json:"FrameId,omitempty"`                                           //头像框 id
	MissionData           []int32            `protobuf:"varint,6,rep,packed,name=missionData,proto3" json:"missionData,omitempty"`                            //MissionData
	QuestionnaireProgress int32              `protobuf:"varint,7,opt,name=questionnaireProgress,proto3" json:"questionnaireProgress,omitempty"`               // 问卷进度，初始为 0.MSDK 版本该值 0 代表 PublicEnum UserQuestionState
	Exp                   int64              `protobuf:"varint,8,opt,name=Exp,proto3" json:"Exp,omitempty"`                                                   //经验（大数值表示）
	MoneyData             *PBPlayerMoneyInfo `protobuf:"bytes,9,opt,name=moneyData,proto3" json:"moneyData,omitempty"`                                        //代币信息
	FrameExpireTime       int64              `protobuf:"varint,10,opt,name=frameExpireTime,proto3" json:"frameExpireTime,omitempty"`                          //头像框过期时间
	WorldChatRoom         string             `protobuf:"bytes,11,opt,name=worldChatRoom,proto3" json:"worldChatRoom,omitempty"`                               //世界聊天频道
	PChatCleanTime        int64              `protobuf:"varint,12,opt,name=pChatCleanTime,proto3" json:"pChatCleanTime,omitempty"`                            //聊天屏蔽时间戳
	PlayerShareTime       int32              `protobuf:"varint,13,opt,name=PlayerShareTime,proto3" json:"PlayerShareTime,omitempty"`                          // 玩家分享次数
	AreaID                int32              `protobuf:"varint,14,opt,name=AreaID,proto3" json:"AreaID,omitempty"`                                            //玩家大区 ID
	GameCenterLoginType   LoginByType        `protobuf:"varint,15,opt,name=gameCenterLoginType,proto3,enum=LoginByType" json:"gameCenterLoginType,omitempty"` //游戏中心登录类型
	TitleData             *PBPlayerTitleData `protobuf:"bytes,16,opt,name=titleData,proto3" json:"titleData,omitempty"`                                       //称号信息
	ShowSDK               bool               `protobuf:"varint,17,opt,name=showSDK,proto3" json:"showSDK,omitempty"`                                          //是否显示 SDK
	Birthday              int32              `protobuf:"varint,18,opt,name=birthday,proto3" json:"birthday,omitempty"`                                        //生日 格式：月*100 + 日  6 月 1 日：601
	ShowKLCDK             bool               `protobuf:"varint,19,opt,name=showKLCDK,proto3" json:"showKLCDK,omitempty"`                                      //是否显示口令 cdk
	ModelId               int32              `protobuf:"varint,20,opt,name=modelId,proto3" json:"modelId,omitempty"`                                          //模型的 id 信息
	AttributeLevelupList  []*PBAttributeInfo `protobuf:"bytes,21,rep,name=attributeLevelupList,proto3" json:"attributeLevelupList,omitempty"`                 //玩家属性升级系统 使用的 属性集合
	TalentData            *PBTalentData      `protobuf:"bytes,22,opt,name=talentData,proto3" json:"talentData,omitempty"`                                     //天赋系统数据
	EquipSlots            []int32            `protobuf:"varint,23,rep,packed,name=equipSlots,proto3" json:"equipSlots,omitempty"`                             //角色槽位信息，下标为装备类型位置 EquipType，值为装备 ID，为 0 表示当前槽位空
	HallowsSlots          []int32            `protobuf:"varint,24,rep,packed,name=hallowsSlots,proto3" json:"hallowsSlots,omitempty"`                         //圣物槽位信息，下标为圣物类型位置 HallowsType，值为圣物 ID，为 0 表示当前槽位空
	PetSlots              []int32            `protobuf:"varint,25,rep,packed,name=petSlots,proto3" json:"petSlots,omitempty"`                                 //宠物槽位信息，下标为宠物类型位置 HallowsType，值为宠物 ID，为 0 表示当前槽位空
	SettingData           *PBSettingData     `protobuf:"bytes,26,opt,name=settingData,proto3" json:"settingData,omitempty"`                                   //玩家设置相关数据
	BornServerId          int32              `protobuf:"varint,27,opt,name=bornServerId,proto3" json:"bornServerId,omitempty"`                                //出生服 ID，用于支付订单账号校验
	LoginServerId         int32              `protobuf:"varint,28,opt,name=loginServerId,proto3" json:"loginServerId,omitempty"`                              //当前登录服 ID，用于统计
	ServerCodeSvnVersion  string             `protobuf:"bytes,29,opt,name=serverCodeSvnVersion,proto3" json:"serverCodeSvnVersion,omitempty"`                 //服务器最新版本号（整包维护和热更新都会更改此版本号）
	OpenServerTime        int64              `protobuf:"varint,30,opt,name=openServerTime,proto3" json:"openServerTime,omitempty"`                            //开服时间戳
	Sigin                 string             `protobuf:"bytes,31,opt,name=sigin,proto3" json:"sigin,omitempty"`                                               //个性签名
	Gender                EGenderType        `protobuf:"varint,32,opt,name=gender,proto3,enum=EGenderType" json:"gender,omitempty"`                           //玩家性别，对应 GenderType 枚举
}

func (x *PBPlayerInfo) Reset() {
	*x = PBPlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPlayerInfo) ProtoMessage() {}

func (x *PBPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPlayerInfo.ProtoReflect.Descriptor instead.
func (*PBPlayerInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{0}
}

func (x *PBPlayerInfo) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *PBPlayerInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBPlayerInfo) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBPlayerInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBPlayerInfo) GetFrameId() int32 {
	if x != nil {
		return x.FrameId
	}
	return 0
}

func (x *PBPlayerInfo) GetMissionData() []int32 {
	if x != nil {
		return x.MissionData
	}
	return nil
}

func (x *PBPlayerInfo) GetQuestionnaireProgress() int32 {
	if x != nil {
		return x.QuestionnaireProgress
	}
	return 0
}

func (x *PBPlayerInfo) GetExp() int64 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *PBPlayerInfo) GetMoneyData() *PBPlayerMoneyInfo {
	if x != nil {
		return x.MoneyData
	}
	return nil
}

func (x *PBPlayerInfo) GetFrameExpireTime() int64 {
	if x != nil {
		return x.FrameExpireTime
	}
	return 0
}

func (x *PBPlayerInfo) GetWorldChatRoom() string {
	if x != nil {
		return x.WorldChatRoom
	}
	return ""
}

func (x *PBPlayerInfo) GetPChatCleanTime() int64 {
	if x != nil {
		return x.PChatCleanTime
	}
	return 0
}

func (x *PBPlayerInfo) GetPlayerShareTime() int32 {
	if x != nil {
		return x.PlayerShareTime
	}
	return 0
}

func (x *PBPlayerInfo) GetAreaID() int32 {
	if x != nil {
		return x.AreaID
	}
	return 0
}

func (x *PBPlayerInfo) GetGameCenterLoginType() LoginByType {
	if x != nil {
		return x.GameCenterLoginType
	}
	return LoginByType_LoginByType_Other
}

func (x *PBPlayerInfo) GetTitleData() *PBPlayerTitleData {
	if x != nil {
		return x.TitleData
	}
	return nil
}

func (x *PBPlayerInfo) GetShowSDK() bool {
	if x != nil {
		return x.ShowSDK
	}
	return false
}

func (x *PBPlayerInfo) GetBirthday() int32 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

func (x *PBPlayerInfo) GetShowKLCDK() bool {
	if x != nil {
		return x.ShowKLCDK
	}
	return false
}

func (x *PBPlayerInfo) GetModelId() int32 {
	if x != nil {
		return x.ModelId
	}
	return 0
}

func (x *PBPlayerInfo) GetAttributeLevelupList() []*PBAttributeInfo {
	if x != nil {
		return x.AttributeLevelupList
	}
	return nil
}

func (x *PBPlayerInfo) GetTalentData() *PBTalentData {
	if x != nil {
		return x.TalentData
	}
	return nil
}

func (x *PBPlayerInfo) GetEquipSlots() []int32 {
	if x != nil {
		return x.EquipSlots
	}
	return nil
}

func (x *PBPlayerInfo) GetHallowsSlots() []int32 {
	if x != nil {
		return x.HallowsSlots
	}
	return nil
}

func (x *PBPlayerInfo) GetPetSlots() []int32 {
	if x != nil {
		return x.PetSlots
	}
	return nil
}

func (x *PBPlayerInfo) GetSettingData() *PBSettingData {
	if x != nil {
		return x.SettingData
	}
	return nil
}

func (x *PBPlayerInfo) GetBornServerId() int32 {
	if x != nil {
		return x.BornServerId
	}
	return 0
}

func (x *PBPlayerInfo) GetLoginServerId() int32 {
	if x != nil {
		return x.LoginServerId
	}
	return 0
}

func (x *PBPlayerInfo) GetServerCodeSvnVersion() string {
	if x != nil {
		return x.ServerCodeSvnVersion
	}
	return ""
}

func (x *PBPlayerInfo) GetOpenServerTime() int64 {
	if x != nil {
		return x.OpenServerTime
	}
	return 0
}

func (x *PBPlayerInfo) GetSigin() string {
	if x != nil {
		return x.Sigin
	}
	return ""
}

func (x *PBPlayerInfo) GetGender() EGenderType {
	if x != nil {
		return x.Gender
	}
	return EGenderType_Default
}

//天赋系统数据（有些字段可能不赋值）
type PBTalentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResetTalentCount    int32              `protobuf:"varint,1,opt,name=resetTalentCount,proto3" json:"resetTalentCount,omitempty"`      //天赋系统已重置次数
	SpareTalentCount    int32              `protobuf:"varint,2,opt,name=spareTalentCount,proto3" json:"spareTalentCount,omitempty"`      //天赋系统剩余点数
	TotalTalentCount    int32              `protobuf:"varint,3,opt,name=totalTalentCount,proto3" json:"totalTalentCount,omitempty"`      //天赋系统总点数
	AttributeTalentList []*PBAttributeInfo `protobuf:"bytes,4,rep,name=attributeTalentList,proto3" json:"attributeTalentList,omitempty"` //玩家天赋系统 使用的 属性集合
}

func (x *PBTalentData) Reset() {
	*x = PBTalentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBTalentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBTalentData) ProtoMessage() {}

func (x *PBTalentData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBTalentData.ProtoReflect.Descriptor instead.
func (*PBTalentData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{1}
}

func (x *PBTalentData) GetResetTalentCount() int32 {
	if x != nil {
		return x.ResetTalentCount
	}
	return 0
}

func (x *PBTalentData) GetSpareTalentCount() int32 {
	if x != nil {
		return x.SpareTalentCount
	}
	return 0
}

func (x *PBTalentData) GetTotalTalentCount() int32 {
	if x != nil {
		return x.TotalTalentCount
	}
	return 0
}

func (x *PBTalentData) GetAttributeTalentList() []*PBAttributeInfo {
	if x != nil {
		return x.AttributeTalentList
	}
	return nil
}

//玩家设置相关
type PBSettingData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsShowPush bool `protobuf:"varint,1,opt,name=isShowPush,proto3" json:"isShowPush,omitempty"` //true 开启推送，false 关闭推送
}

func (x *PBSettingData) Reset() {
	*x = PBSettingData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBSettingData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBSettingData) ProtoMessage() {}

func (x *PBSettingData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBSettingData.ProtoReflect.Descriptor instead.
func (*PBSettingData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{2}
}

func (x *PBSettingData) GetIsShowPush() bool {
	if x != nil {
		return x.IsShowPush
	}
	return false
}

type PBHeroInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeroId     int32 `protobuf:"varint,1,opt,name=heroId,proto3" json:"heroId,omitempty"`         //英雄id
	HeroLevel  int32 `protobuf:"varint,2,opt,name=heroLevel,proto3" json:"heroLevel,omitempty"`   //当前等级
	Exp        int32 `protobuf:"varint,3,opt,name=exp,proto3" json:"exp,omitempty"`               //经验/碎片数量
	AwakeLevel int32 `protobuf:"varint,4,opt,name=awakeLevel,proto3" json:"awakeLevel,omitempty"` //觉醒等级
}

func (x *PBHeroInfo) Reset() {
	*x = PBHeroInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBHeroInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBHeroInfo) ProtoMessage() {}

func (x *PBHeroInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBHeroInfo.ProtoReflect.Descriptor instead.
func (*PBHeroInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{3}
}

func (x *PBHeroInfo) GetHeroId() int32 {
	if x != nil {
		return x.HeroId
	}
	return 0
}

func (x *PBHeroInfo) GetHeroLevel() int32 {
	if x != nil {
		return x.HeroLevel
	}
	return 0
}

func (x *PBHeroInfo) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *PBHeroInfo) GetAwakeLevel() int32 {
	if x != nil {
		return x.AwakeLevel
	}
	return 0
}

//金钱信息
type PBPlayerMoneyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gold      int32 `protobuf:"varint,1,opt,name=gold,proto3" json:"gold,omitempty"`           //金币
	Diamon    int32 `protobuf:"varint,2,opt,name=diamon,proto3" json:"diamon,omitempty"`       //钻石
	Power     int32 `protobuf:"varint,3,opt,name=power,proto3" json:"power,omitempty"`         //体力
	Refined   int32 `protobuf:"varint,4,opt,name=refined,proto3" json:"refined,omitempty"`     //洗炼石
	Hallows   int32 `protobuf:"varint,5,opt,name=hallows,proto3" json:"hallows,omitempty"`     //圣物币
	GuildCoin int32 `protobuf:"varint,6,opt,name=guildCoin,proto3" json:"guildCoin,omitempty"` //公会币
}

func (x *PBPlayerMoneyInfo) Reset() {
	*x = PBPlayerMoneyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPlayerMoneyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPlayerMoneyInfo) ProtoMessage() {}

func (x *PBPlayerMoneyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPlayerMoneyInfo.ProtoReflect.Descriptor instead.
func (*PBPlayerMoneyInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{4}
}

func (x *PBPlayerMoneyInfo) GetGold() int32 {
	if x != nil {
		return x.Gold
	}
	return 0
}

func (x *PBPlayerMoneyInfo) GetDiamon() int32 {
	if x != nil {
		return x.Diamon
	}
	return 0
}

func (x *PBPlayerMoneyInfo) GetPower() int32 {
	if x != nil {
		return x.Power
	}
	return 0
}

func (x *PBPlayerMoneyInfo) GetRefined() int32 {
	if x != nil {
		return x.Refined
	}
	return 0
}

func (x *PBPlayerMoneyInfo) GetHallows() int32 {
	if x != nil {
		return x.Hallows
	}
	return 0
}

func (x *PBPlayerMoneyInfo) GetGuildCoin() int32 {
	if x != nil {
		return x.GuildCoin
	}
	return 0
}

//称号信息
type PBPlayerTitleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleId         int32 `protobuf:"varint,1,opt,name=titleId,proto3" json:"titleId,omitempty"`                 //称号 id
	TitleExpireTime int64 `protobuf:"varint,2,opt,name=titleExpireTime,proto3" json:"titleExpireTime,omitempty"` //称号过期时间
}

func (x *PBPlayerTitleData) Reset() {
	*x = PBPlayerTitleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPlayerTitleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPlayerTitleData) ProtoMessage() {}

func (x *PBPlayerTitleData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPlayerTitleData.ProtoReflect.Descriptor instead.
func (*PBPlayerTitleData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{5}
}

func (x *PBPlayerTitleData) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

func (x *PBPlayerTitleData) GetTitleExpireTime() int64 {
	if x != nil {
		return x.TitleExpireTime
	}
	return 0
}

//设备信息
type PBDeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientVersion  string  `protobuf:"bytes,1,opt,name=ClientVersion,proto3" json:"ClientVersion,omitempty"`   //客户端版本
	SystemSoftware string  `protobuf:"bytes,2,opt,name=SystemSoftware,proto3" json:"SystemSoftware,omitempty"` //移动终端操作系统版本
	SystemHardware string  `protobuf:"bytes,3,opt,name=SystemHardware,proto3" json:"SystemHardware,omitempty"` //移动终端机型
	TelecomOper    string  `protobuf:"bytes,4,opt,name=TelecomOper,proto3" json:"TelecomOper,omitempty"`       //运营商
	Network        string  `protobuf:"bytes,5,opt,name=Network,proto3" json:"Network,omitempty"`               //3G/WIFI/2G
	ScreenWidth    int32   `protobuf:"varint,6,opt,name=ScreenWidth,proto3" json:"ScreenWidth,omitempty"`      //显示屏宽度
	ScreenHight    int32   `protobuf:"varint,7,opt,name=ScreenHight,proto3" json:"ScreenHight,omitempty"`      //显示屏高度
	Density        float32 `protobuf:"fixed32,8,opt,name=Density,proto3" json:"Density,omitempty"`             //像素密度
	CpuHardware    string  `protobuf:"bytes,9,opt,name=CpuHardware,proto3" json:"CpuHardware,omitempty"`       //cpu 类型 | 频率 | 核数
	Memory         int32   `protobuf:"varint,10,opt,name=Memory,proto3" json:"Memory,omitempty"`               //内存信息单位 M
	GLRender       string  `protobuf:"bytes,11,opt,name=GLRender,proto3" json:"GLRender,omitempty"`            //opengl render 信息
	GLVersion      string  `protobuf:"bytes,12,opt,name=GLVersion,proto3" json:"GLVersion,omitempty"`          //opengl 版本信息
	DeviceId       string  `protobuf:"bytes,13,opt,name=DeviceId,proto3" json:"DeviceId,omitempty"`            //设备 ID，安卓上报 IMEI,IOS 上报 IDFA(报原始信息，不要加密
	PlatID         int32   `protobuf:"varint,14,opt,name=PlatID,proto3" json:"PlatID,omitempty"`               //设备类型 ID：IOS 填 0，安卓填 1，不能写其它值。
	RegChannel     int32   `protobuf:"varint,15,opt,name=RegChannel,proto3" json:"RegChannel,omitempty"`       //注册渠道
	LoginChannel   int32   `protobuf:"varint,16,opt,name=LoginChannel,proto3" json:"LoginChannel,omitempty"`   //登录渠道可通过 MSDK 获取
	SecReportData  string  `protobuf:"bytes,17,opt,name=secReportData,proto3" json:"secReportData,omitempty"`  //安全日志
	DeviceUDID     string  `protobuf:"bytes,18,opt,name=deviceUDID,proto3" json:"deviceUDID,omitempty"`        //设备 UDID
	IsEmulator     bool    `protobuf:"varint,19,opt,name=isEmulator,proto3" json:"isEmulator,omitempty"`       //是否是模拟器
	AndroidId      string  `protobuf:"bytes,20,opt,name=AndroidId,proto3" json:"AndroidId,omitempty"`          //ANDROID_ID
	Idfv           string  `protobuf:"bytes,21,opt,name=Idfv,proto3" json:"Idfv,omitempty"`                    //ios idfv，iOS 6.0 系统新增，用于给应用开发商（Vendor）标识用户
	MAC            string  `protobuf:"bytes,22,opt,name=MAC,proto3" json:"MAC,omitempty"`                      //MAC
	OAID           string  `protobuf:"bytes,23,opt,name=OAID,proto3" json:"OAID,omitempty"`                    //OAID
	CAID           string  `protobuf:"bytes,24,opt,name=CAID,proto3" json:"CAID,omitempty"`                    //CAID
}

func (x *PBDeviceInfo) Reset() {
	*x = PBDeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBDeviceInfo) ProtoMessage() {}

func (x *PBDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBDeviceInfo.ProtoReflect.Descriptor instead.
func (*PBDeviceInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{6}
}

func (x *PBDeviceInfo) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *PBDeviceInfo) GetSystemSoftware() string {
	if x != nil {
		return x.SystemSoftware
	}
	return ""
}

func (x *PBDeviceInfo) GetSystemHardware() string {
	if x != nil {
		return x.SystemHardware
	}
	return ""
}

func (x *PBDeviceInfo) GetTelecomOper() string {
	if x != nil {
		return x.TelecomOper
	}
	return ""
}

func (x *PBDeviceInfo) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *PBDeviceInfo) GetScreenWidth() int32 {
	if x != nil {
		return x.ScreenWidth
	}
	return 0
}

func (x *PBDeviceInfo) GetScreenHight() int32 {
	if x != nil {
		return x.ScreenHight
	}
	return 0
}

func (x *PBDeviceInfo) GetDensity() float32 {
	if x != nil {
		return x.Density
	}
	return 0
}

func (x *PBDeviceInfo) GetCpuHardware() string {
	if x != nil {
		return x.CpuHardware
	}
	return ""
}

func (x *PBDeviceInfo) GetMemory() int32 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *PBDeviceInfo) GetGLRender() string {
	if x != nil {
		return x.GLRender
	}
	return ""
}

func (x *PBDeviceInfo) GetGLVersion() string {
	if x != nil {
		return x.GLVersion
	}
	return ""
}

func (x *PBDeviceInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PBDeviceInfo) GetPlatID() int32 {
	if x != nil {
		return x.PlatID
	}
	return 0
}

func (x *PBDeviceInfo) GetRegChannel() int32 {
	if x != nil {
		return x.RegChannel
	}
	return 0
}

func (x *PBDeviceInfo) GetLoginChannel() int32 {
	if x != nil {
		return x.LoginChannel
	}
	return 0
}

func (x *PBDeviceInfo) GetSecReportData() string {
	if x != nil {
		return x.SecReportData
	}
	return ""
}

func (x *PBDeviceInfo) GetDeviceUDID() string {
	if x != nil {
		return x.DeviceUDID
	}
	return ""
}

func (x *PBDeviceInfo) GetIsEmulator() bool {
	if x != nil {
		return x.IsEmulator
	}
	return false
}

func (x *PBDeviceInfo) GetAndroidId() string {
	if x != nil {
		return x.AndroidId
	}
	return ""
}

func (x *PBDeviceInfo) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *PBDeviceInfo) GetMAC() string {
	if x != nil {
		return x.MAC
	}
	return ""
}

func (x *PBDeviceInfo) GetOAID() string {
	if x != nil {
		return x.OAID
	}
	return ""
}

func (x *PBDeviceInfo) GetCAID() string {
	if x != nil {
		return x.CAID
	}
	return ""
}

//IOS 广告信息
type PBASAIadData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IadToken  string `protobuf:"bytes,1,opt,name=IadToken,proto3" json:"IadToken,omitempty"`   //IOS14.3 以上
	IadError  string `protobuf:"bytes,2,opt,name=IadError,proto3" json:"IadError,omitempty"`   //IOS14.3 以上
	IadData   string `protobuf:"bytes,3,opt,name=IadData,proto3" json:"IadData,omitempty"`     //IOS14.3 以下 IOS 广告数据
	UserAgent string `protobuf:"bytes,4,opt,name=UserAgent,proto3" json:"UserAgent,omitempty"` //浏览器用户代理
}

func (x *PBASAIadData) Reset() {
	*x = PBASAIadData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBASAIadData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBASAIadData) ProtoMessage() {}

func (x *PBASAIadData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBASAIadData.ProtoReflect.Descriptor instead.
func (*PBASAIadData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{7}
}

func (x *PBASAIadData) GetIadToken() string {
	if x != nil {
		return x.IadToken
	}
	return ""
}

func (x *PBASAIadData) GetIadError() string {
	if x != nil {
		return x.IadError
	}
	return ""
}

func (x *PBASAIadData) GetIadData() string {
	if x != nil {
		return x.IadData
	}
	return ""
}

func (x *PBASAIadData) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

//=====================米大师开始=============================
//米大师：支付信息 [c#]
type PB_MidasInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MidasToken string `protobuf:"bytes,1,opt,name=midasToken,proto3" json:"midasToken,omitempty"` //用于米大师接口调用（例如查询余额，系统赠送等）
	Pf         string `protobuf:"bytes,2,opt,name=pf,proto3" json:"pf,omitempty"`                 //用于米大师接口调用（例如查询余额，系统赠送等）
	Pfkey      string `protobuf:"bytes,3,opt,name=pfkey,proto3" json:"pfkey,omitempty"`           //用于米大师接口调用（例如查询余额，系统赠送等）
}

func (x *PB_MidasInfo) Reset() {
	*x = PB_MidasInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PB_MidasInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PB_MidasInfo) ProtoMessage() {}

func (x *PB_MidasInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PB_MidasInfo.ProtoReflect.Descriptor instead.
func (*PB_MidasInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{8}
}

func (x *PB_MidasInfo) GetMidasToken() string {
	if x != nil {
		return x.MidasToken
	}
	return ""
}

func (x *PB_MidasInfo) GetPf() string {
	if x != nil {
		return x.Pf
	}
	return ""
}

func (x *PB_MidasInfo) GetPfkey() string {
	if x != nil {
		return x.Pfkey
	}
	return ""
}

//=====================米大师结束=============================
//通用的 key 和 value
type PBCommonKeyValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   int32 `protobuf:"varint,1,opt,name=key,proto3" json:"key,omitempty"`
	Value int32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	Type  int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *PBCommonKeyValue) Reset() {
	*x = PBCommonKeyValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBCommonKeyValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBCommonKeyValue) ProtoMessage() {}

func (x *PBCommonKeyValue) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBCommonKeyValue.ProtoReflect.Descriptor instead.
func (*PBCommonKeyValue) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{9}
}

func (x *PBCommonKeyValue) GetKey() int32 {
	if x != nil {
		return x.Key
	}
	return 0
}

func (x *PBCommonKeyValue) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *PBCommonKeyValue) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

//通用的 key 和 value
type PBCommonLongKeyValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   int64 `protobuf:"varint,1,opt,name=key,proto3" json:"key,omitempty"`
	Value int64 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	Type  int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *PBCommonLongKeyValue) Reset() {
	*x = PBCommonLongKeyValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBCommonLongKeyValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBCommonLongKeyValue) ProtoMessage() {}

func (x *PBCommonLongKeyValue) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBCommonLongKeyValue.ProtoReflect.Descriptor instead.
func (*PBCommonLongKeyValue) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{10}
}

func (x *PBCommonLongKeyValue) GetKey() int64 {
	if x != nil {
		return x.Key
	}
	return 0
}

func (x *PBCommonLongKeyValue) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *PBCommonLongKeyValue) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

//通用的 key 和 value
type PBCommonIntBool struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   int32 `protobuf:"varint,1,opt,name=key,proto3" json:"key,omitempty"`
	Value bool  `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PBCommonIntBool) Reset() {
	*x = PBCommonIntBool{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBCommonIntBool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBCommonIntBool) ProtoMessage() {}

func (x *PBCommonIntBool) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBCommonIntBool.ProtoReflect.Descriptor instead.
func (*PBCommonIntBool) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{11}
}

func (x *PBCommonIntBool) GetKey() int32 {
	if x != nil {
		return x.Key
	}
	return 0
}

func (x *PBCommonIntBool) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

//通用的 key 和 value
type PBIntPair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   int32 `protobuf:"varint,1,opt,name=key,proto3" json:"key,omitempty"`
	Value int32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *PBIntPair) Reset() {
	*x = PBIntPair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBIntPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBIntPair) ProtoMessage() {}

func (x *PBIntPair) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBIntPair.ProtoReflect.Descriptor instead.
func (*PBIntPair) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{12}
}

func (x *PBIntPair) GetKey() int32 {
	if x != nil {
		return x.Key
	}
	return 0
}

func (x *PBIntPair) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

//=====================章节开始=============================
//掉落道具信息
type PBDropItemDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId     int32         `protobuf:"varint,1,opt,name=itemId,proto3" json:"itemId,omitempty"`                        //道具 id
	ItemCount  int32         `protobuf:"varint,2,opt,name=itemCount,proto3" json:"itemCount,omitempty"`                  //道具数量
	ItemType   AwardItemType `protobuf:"varint,3,opt,name=itemType,proto3,enum=AwardItemType" json:"itemType,omitempty"` //道具类型
	HighEffect bool          `protobuf:"varint,4,opt,name=highEffect,proto3" json:"highEffect,omitempty"`                //高级特效 true 表示可以闪屏
}

func (x *PBDropItemDataInfo) Reset() {
	*x = PBDropItemDataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBDropItemDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBDropItemDataInfo) ProtoMessage() {}

func (x *PBDropItemDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBDropItemDataInfo.ProtoReflect.Descriptor instead.
func (*PBDropItemDataInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{13}
}

func (x *PBDropItemDataInfo) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *PBDropItemDataInfo) GetItemCount() int32 {
	if x != nil {
		return x.ItemCount
	}
	return 0
}

func (x *PBDropItemDataInfo) GetItemType() AwardItemType {
	if x != nil {
		return x.ItemType
	}
	return AwardItemType_AwardItemType_Money
}

func (x *PBDropItemDataInfo) GetHighEffect() bool {
	if x != nil {
		return x.HighEffect
	}
	return false
}

//道具使用参数
type UseItemParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid        int64        `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`              //物品唯一标识 ID
	ItemId      int32        `protobuf:"varint,2,opt,name=itemId,proto3" json:"itemId,omitempty"`          //物品模版 ID
	UseNum      int32        `protobuf:"varint,3,opt,name=useNum,proto3" json:"useNum,omitempty"`          //使用数量
	ChooseInfos []*PBIntPair `protobuf:"bytes,4,rep,name=chooseInfos,proto3" json:"chooseInfos,omitempty"` //自选信息集（key:选中的 id，val:此选项的次数）
}

func (x *UseItemParam) Reset() {
	*x = UseItemParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseItemParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemParam) ProtoMessage() {}

func (x *UseItemParam) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemParam.ProtoReflect.Descriptor instead.
func (*UseItemParam) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{14}
}

func (x *UseItemParam) GetGuid() int64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *UseItemParam) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *UseItemParam) GetUseNum() int32 {
	if x != nil {
		return x.UseNum
	}
	return 0
}

func (x *UseItemParam) GetChooseInfos() []*PBIntPair {
	if x != nil {
		return x.ChooseInfos
	}
	return nil
}

//====================================功能解锁====================================
//功能解锁
type PBFunctionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuncId     int32 `protobuf:"varint,1,opt,name=funcId,proto3" json:"funcId,omitempty"`         // 功能 ID
	FuncStatus int32 `protobuf:"varint,2,opt,name=funcStatus,proto3" json:"funcStatus,omitempty"` // 功能状态 0关闭 1开启
}

func (x *PBFunctionData) Reset() {
	*x = PBFunctionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBFunctionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBFunctionData) ProtoMessage() {}

func (x *PBFunctionData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBFunctionData.ProtoReflect.Descriptor instead.
func (*PBFunctionData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{15}
}

func (x *PBFunctionData) GetFuncId() int32 {
	if x != nil {
		return x.FuncId
	}
	return 0
}

func (x *PBFunctionData) GetFuncStatus() int32 {
	if x != nil {
		return x.FuncStatus
	}
	return 0
}

//=====================分歧开始=============================
//节点信息
type PBBranchFlagData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BranchId   int32 `protobuf:"varint,1,opt,name=branchId,proto3" json:"branchId,omitempty"`
	BranchFlag int32 `protobuf:"varint,2,opt,name=branchFlag,proto3" json:"branchFlag,omitempty"`
}

func (x *PBBranchFlagData) Reset() {
	*x = PBBranchFlagData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBranchFlagData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBranchFlagData) ProtoMessage() {}

func (x *PBBranchFlagData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBranchFlagData.ProtoReflect.Descriptor instead.
func (*PBBranchFlagData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{16}
}

func (x *PBBranchFlagData) GetBranchId() int32 {
	if x != nil {
		return x.BranchId
	}
	return 0
}

func (x *PBBranchFlagData) GetBranchFlag() int32 {
	if x != nil {
		return x.BranchFlag
	}
	return 0
}

//红点
type PBRedDotData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SyncIndex int32 `protobuf:"varint,1,opt,name=SyncIndex,proto3" json:"SyncIndex,omitempty"` //ID
	SyncData  int32 `protobuf:"varint,2,opt,name=SyncData,proto3" json:"SyncData,omitempty"`   //true:1    false :0
}

func (x *PBRedDotData) Reset() {
	*x = PBRedDotData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBRedDotData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBRedDotData) ProtoMessage() {}

func (x *PBRedDotData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBRedDotData.ProtoReflect.Descriptor instead.
func (*PBRedDotData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{17}
}

func (x *PBRedDotData) GetSyncIndex() int32 {
	if x != nil {
		return x.SyncIndex
	}
	return 0
}

func (x *PBRedDotData) GetSyncData() int32 {
	if x != nil {
		return x.SyncData
	}
	return 0
}

//属性类
type PBAttributeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeType AttributeType `protobuf:"varint,1,opt,name=attributeType,proto3,enum=AttributeType" json:"attributeType,omitempty"` //属性枚举类型
	Type          int32         `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                                      //1 整形类型 2 浮点类型 3 百分比
	Value         int32         `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`                                    //值
	Level         int32         `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`                                    //等级
	Unlock        bool          `protobuf:"varint,5,opt,name=unlock,proto3" json:"unlock,omitempty"`                                  //是否解锁
}

func (x *PBAttributeInfo) Reset() {
	*x = PBAttributeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBAttributeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBAttributeInfo) ProtoMessage() {}

func (x *PBAttributeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBAttributeInfo.ProtoReflect.Descriptor instead.
func (*PBAttributeInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{18}
}

func (x *PBAttributeInfo) GetAttributeType() AttributeType {
	if x != nil {
		return x.AttributeType
	}
	return AttributeType_AttributeType_NONE
}

func (x *PBAttributeInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *PBAttributeInfo) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *PBAttributeInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBAttributeInfo) GetUnlock() bool {
	if x != nil {
		return x.Unlock
	}
	return false
}

//=====================同步消息开始=============================
//道具信息（暂未使用）
type PBItemDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId    int32 `protobuf:"varint,1,opt,name=ItemId,proto3" json:"ItemId,omitempty"`       //道具 id
	ItemCount int32 `protobuf:"varint,2,opt,name=ItemCount,proto3" json:"ItemCount,omitempty"` //道具数量
	NewFlag   int32 `protobuf:"varint,3,opt,name=newFlag,proto3" json:"newFlag,omitempty"`     //新获得
}

func (x *PBItemDataInfo) Reset() {
	*x = PBItemDataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBItemDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBItemDataInfo) ProtoMessage() {}

func (x *PBItemDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBItemDataInfo.ProtoReflect.Descriptor instead.
func (*PBItemDataInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{19}
}

func (x *PBItemDataInfo) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *PBItemDataInfo) GetItemCount() int32 {
	if x != nil {
		return x.ItemCount
	}
	return 0
}

func (x *PBItemDataInfo) GetNewFlag() int32 {
	if x != nil {
		return x.NewFlag
	}
	return 0
}

type PBBagItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       BagItemType       `protobuf:"varint,1,opt,name=type,proto3,enum=BagItemType" json:"type,omitempty"` //背包数据结构类型，详情参照枚举定义
	NormalItem *PBNormalItemInfo `protobuf:"bytes,2,opt,name=normalItem,proto3" json:"normalItem,omitempty"`       //常规物品
}

func (x *PBBagItemInfo) Reset() {
	*x = PBBagItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBagItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBagItemInfo) ProtoMessage() {}

func (x *PBBagItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBagItemInfo.ProtoReflect.Descriptor instead.
func (*PBBagItemInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{20}
}

func (x *PBBagItemInfo) GetType() BagItemType {
	if x != nil {
		return x.Type
	}
	return BagItemType_NormalItem
}

func (x *PBBagItemInfo) GetNormalItem() *PBNormalItemInfo {
	if x != nil {
		return x.NormalItem
	}
	return nil
}

//常规道具实体
type PBNormalItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid   int64 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`     //物品唯一标识
	ItemId int32 `protobuf:"varint,2,opt,name=itemId,proto3" json:"itemId,omitempty"` //物品表 ID
	Value  int32 `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`   //物品数量
}

func (x *PBNormalItemInfo) Reset() {
	*x = PBNormalItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBNormalItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBNormalItemInfo) ProtoMessage() {}

func (x *PBNormalItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBNormalItemInfo.ProtoReflect.Descriptor instead.
func (*PBNormalItemInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{21}
}

func (x *PBNormalItemInfo) GetGuid() int64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *PBNormalItemInfo) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *PBNormalItemInfo) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

//装备信息
type PBEquipDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                    //装备 ID
	Level     int32     `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`              //当前等级
	Count     int32     `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`              //现有数量
	Type      EquipType `protobuf:"varint,4,opt,name=type,proto3,enum=EquipType" json:"type,omitempty"` //装备类型
	OwnPro    int32     `protobuf:"varint,5,opt,name=OwnPro,proto3" json:"OwnPro,omitempty"`            //拥有属性数值 第一条值（默认值都填为 0）
	OwnPro1   int32     `protobuf:"varint,6,opt,name=OwnPro1,proto3" json:"OwnPro1,omitempty"`          //拥有属性数值 第二条值（默认值都填为 0）
	DreesPro  int32     `protobuf:"varint,7,opt,name=DreesPro,proto3" json:"DreesPro,omitempty"`        //穿戴属性数值 第一条值（默认值都填为 0）
	DreesPro1 int32     `protobuf:"varint,8,opt,name=DreesPro1,proto3" json:"DreesPro1,omitempty"`      //穿戴属性数值 第二条值（默认值都填为 0）
}

func (x *PBEquipDataInfo) Reset() {
	*x = PBEquipDataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBEquipDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBEquipDataInfo) ProtoMessage() {}

func (x *PBEquipDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBEquipDataInfo.ProtoReflect.Descriptor instead.
func (*PBEquipDataInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{22}
}

func (x *PBEquipDataInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBEquipDataInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBEquipDataInfo) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *PBEquipDataInfo) GetType() EquipType {
	if x != nil {
		return x.Type
	}
	return EquipType_Equip_NONE
}

func (x *PBEquipDataInfo) GetOwnPro() int32 {
	if x != nil {
		return x.OwnPro
	}
	return 0
}

func (x *PBEquipDataInfo) GetOwnPro1() int32 {
	if x != nil {
		return x.OwnPro1
	}
	return 0
}

func (x *PBEquipDataInfo) GetDreesPro() int32 {
	if x != nil {
		return x.DreesPro
	}
	return 0
}

func (x *PBEquipDataInfo) GetDreesPro1() int32 {
	if x != nil {
		return x.DreesPro1
	}
	return 0
}

//装备强化信息
type PBEquipLevelupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                      //装备 ID
	OldLevel     int32     `protobuf:"varint,2,opt,name=oldLevel,proto3" json:"oldLevel,omitempty"`          //旧等级
	Level        int32     `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`                //当前等级
	Count        int32     `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                //现有数量
	Type         EquipType `protobuf:"varint,5,opt,name=type,proto3,enum=EquipType" json:"type,omitempty"`   //装备类型
	ExtraDiamond int64     `protobuf:"varint,6,opt,name=extraDiamond,proto3" json:"extraDiamond,omitempty"`  //升满后溢出部分转化为钻石数，0 表示无钻石产出。
	OwnPro       int32     `protobuf:"varint,7,opt,name=OwnPro,proto3" json:"OwnPro,omitempty"`              //当前 拥有属性数值 第一条值（默认值都填为 0）
	OwnPro1      int32     `protobuf:"varint,8,opt,name=OwnPro1,proto3" json:"OwnPro1,omitempty"`            //当前 拥有属性数值 第二条值（默认值都填为 0）
	DreesPro     int32     `protobuf:"varint,9,opt,name=DreesPro,proto3" json:"DreesPro,omitempty"`          //当前 穿戴属性数值 第一条值（默认值都填为 0）
	DreesPro1    int32     `protobuf:"varint,10,opt,name=DreesPro1,proto3" json:"DreesPro1,omitempty"`       //当前 穿戴属性数值 第二条值（默认值都填为 0）
	OldOwnPro    int32     `protobuf:"varint,11,opt,name=OldOwnPro,proto3" json:"OldOwnPro,omitempty"`       //旧 拥有属性数值 第一条值（默认值都填为 0）
	OldOwnPro1   int32     `protobuf:"varint,12,opt,name=OldOwnPro1,proto3" json:"OldOwnPro1,omitempty"`     //旧 拥有属性数值 第二条值（默认值都填为 0）
	OldDreesPro  int32     `protobuf:"varint,13,opt,name=OldDreesPro,proto3" json:"OldDreesPro,omitempty"`   //旧 穿戴属性数值 第一条值（默认值都填为 0）
	OldDreesPro1 int32     `protobuf:"varint,14,opt,name=OldDreesPro1,proto3" json:"OldDreesPro1,omitempty"` //旧 穿戴属性数值 第二条值（默认值都填为 0）
}

func (x *PBEquipLevelupInfo) Reset() {
	*x = PBEquipLevelupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBEquipLevelupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBEquipLevelupInfo) ProtoMessage() {}

func (x *PBEquipLevelupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBEquipLevelupInfo.ProtoReflect.Descriptor instead.
func (*PBEquipLevelupInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{23}
}

func (x *PBEquipLevelupInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetOldLevel() int32 {
	if x != nil {
		return x.OldLevel
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetType() EquipType {
	if x != nil {
		return x.Type
	}
	return EquipType_Equip_NONE
}

func (x *PBEquipLevelupInfo) GetExtraDiamond() int64 {
	if x != nil {
		return x.ExtraDiamond
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetOwnPro() int32 {
	if x != nil {
		return x.OwnPro
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetOwnPro1() int32 {
	if x != nil {
		return x.OwnPro1
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetDreesPro() int32 {
	if x != nil {
		return x.DreesPro
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetDreesPro1() int32 {
	if x != nil {
		return x.DreesPro1
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetOldOwnPro() int32 {
	if x != nil {
		return x.OldOwnPro
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetOldOwnPro1() int32 {
	if x != nil {
		return x.OldOwnPro1
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetOldDreesPro() int32 {
	if x != nil {
		return x.OldDreesPro
	}
	return 0
}

func (x *PBEquipLevelupInfo) GetOldDreesPro1() int32 {
	if x != nil {
		return x.OldDreesPro1
	}
	return 0
}

//=========================邮件相关开始====================
//邮件信息
type PBMail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                             //ID
	Type        MailType            `protobuf:"varint,2,opt,name=type,proto3,enum=MailType" json:"type,omitempty"`           //邮件类型
	Title       string              `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`                        //标题
	Content     string              `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`                    //内容
	Goods       []*PBCommonKeyValue `protobuf:"bytes,5,rep,name=goods,proto3" json:"goods,omitempty"`                        //道具信息
	Status      MailStateType       `protobuf:"varint,6,opt,name=status,proto3,enum=MailStateType" json:"status,omitempty"`  //邮件状态
	Reason      MailReasonType      `protobuf:"varint,7,opt,name=reason,proto3,enum=MailReasonType" json:"reason,omitempty"` //邮件的原因
	CreateTime  int64               `protobuf:"varint,8,opt,name=createTime,proto3" json:"createTime,omitempty"`             //发送时间
	ExpireTime  int64               `protobuf:"varint,9,opt,name=expireTime,proto3" json:"expireTime,omitempty"`             //过期时间
	SenderGuild int64               `protobuf:"varint,10,opt,name=senderGuild,proto3" json:"senderGuild,omitempty"`          //发送人 GUID
	Sender      string              `protobuf:"bytes,11,opt,name=sender,proto3" json:"sender,omitempty"`                     //发送人名称
	Recerguild  int64               `protobuf:"varint,12,opt,name=recerguild,proto3" json:"recerguild,omitempty"`            //接受者 GUID
	Recer       string              `protobuf:"bytes,13,opt,name=recer,proto3" json:"recer,omitempty"`                       //接受者
}

func (x *PBMail) Reset() {
	*x = PBMail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBMail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBMail) ProtoMessage() {}

func (x *PBMail) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBMail.ProtoReflect.Descriptor instead.
func (*PBMail) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{24}
}

func (x *PBMail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBMail) GetType() MailType {
	if x != nil {
		return x.Type
	}
	return MailType_MailType_NonReward
}

func (x *PBMail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PBMail) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *PBMail) GetGoods() []*PBCommonKeyValue {
	if x != nil {
		return x.Goods
	}
	return nil
}

func (x *PBMail) GetStatus() MailStateType {
	if x != nil {
		return x.Status
	}
	return MailStateType_MailStateType_UnRead
}

func (x *PBMail) GetReason() MailReasonType {
	if x != nil {
		return x.Reason
	}
	return MailReasonType_MailReasonType_TestGM
}

func (x *PBMail) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PBMail) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *PBMail) GetSenderGuild() int64 {
	if x != nil {
		return x.SenderGuild
	}
	return 0
}

func (x *PBMail) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *PBMail) GetRecerguild() int64 {
	if x != nil {
		return x.Recerguild
	}
	return 0
}

func (x *PBMail) GetRecer() string {
	if x != nil {
		return x.Recer
	}
	return ""
}

//=====================社交分裂开始=============================
type PBFriendRasinRewardData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableId      int32 `protobuf:"varint,1,opt,name=tableId,proto3" json:"tableId,omitempty"`           //进度
	Rewardstatus int32 `protobuf:"varint,2,opt,name=rewardstatus,proto3" json:"rewardstatus,omitempty"` //阶段性奖励 0 未完成 1 已完成 2 已领奖
}

func (x *PBFriendRasinRewardData) Reset() {
	*x = PBFriendRasinRewardData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBFriendRasinRewardData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBFriendRasinRewardData) ProtoMessage() {}

func (x *PBFriendRasinRewardData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBFriendRasinRewardData.ProtoReflect.Descriptor instead.
func (*PBFriendRasinRewardData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{25}
}

func (x *PBFriendRasinRewardData) GetTableId() int32 {
	if x != nil {
		return x.TableId
	}
	return 0
}

func (x *PBFriendRasinRewardData) GetRewardstatus() int32 {
	if x != nil {
		return x.Rewardstatus
	}
	return 0
}

type PBFriendGrowthRewardData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableId         int32  `protobuf:"varint,1,opt,name=tableId,proto3" json:"tableId,omitempty"`                 //id
	Unlock          int32  `protobuf:"varint,2,opt,name=unlock,proto3" json:"unlock,omitempty"`                   //是否解锁 0 解锁 1 未解锁
	PlayerName      string `protobuf:"bytes,3,opt,name=playerName,proto3" json:"playerName,omitempty"`            //玩家名称
	PlayerUID       int64  `protobuf:"varint,4,opt,name=playerUID,proto3" json:"playerUID,omitempty"`             //玩家 UID
	Schedule        int32  `protobuf:"varint,5,opt,name=schedule,proto3" json:"schedule,omitempty"`               //进度
	OverallSchedule int32  `protobuf:"varint,6,opt,name=overallSchedule,proto3" json:"overallSchedule,omitempty"` //总进度
}

func (x *PBFriendGrowthRewardData) Reset() {
	*x = PBFriendGrowthRewardData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBFriendGrowthRewardData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBFriendGrowthRewardData) ProtoMessage() {}

func (x *PBFriendGrowthRewardData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBFriendGrowthRewardData.ProtoReflect.Descriptor instead.
func (*PBFriendGrowthRewardData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{26}
}

func (x *PBFriendGrowthRewardData) GetTableId() int32 {
	if x != nil {
		return x.TableId
	}
	return 0
}

func (x *PBFriendGrowthRewardData) GetUnlock() int32 {
	if x != nil {
		return x.Unlock
	}
	return 0
}

func (x *PBFriendGrowthRewardData) GetPlayerName() string {
	if x != nil {
		return x.PlayerName
	}
	return ""
}

func (x *PBFriendGrowthRewardData) GetPlayerUID() int64 {
	if x != nil {
		return x.PlayerUID
	}
	return 0
}

func (x *PBFriendGrowthRewardData) GetSchedule() int32 {
	if x != nil {
		return x.Schedule
	}
	return 0
}

func (x *PBFriendGrowthRewardData) GetOverallSchedule() int32 {
	if x != nil {
		return x.OverallSchedule
	}
	return 0
}

type PBFriendGrowthQuestRewardData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TableId      int32 `protobuf:"varint,1,opt,name=tableId,proto3" json:"tableId,omitempty"`           //进度
	Rewardstatus int32 `protobuf:"varint,2,opt,name=rewardstatus,proto3" json:"rewardstatus,omitempty"` //阶段性奖励 0 未完成 1 已完成 2 已领奖
}

func (x *PBFriendGrowthQuestRewardData) Reset() {
	*x = PBFriendGrowthQuestRewardData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBFriendGrowthQuestRewardData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBFriendGrowthQuestRewardData) ProtoMessage() {}

func (x *PBFriendGrowthQuestRewardData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBFriendGrowthQuestRewardData.ProtoReflect.Descriptor instead.
func (*PBFriendGrowthQuestRewardData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{27}
}

func (x *PBFriendGrowthQuestRewardData) GetTableId() int32 {
	if x != nil {
		return x.TableId
	}
	return 0
}

func (x *PBFriendGrowthQuestRewardData) GetRewardstatus() int32 {
	if x != nil {
		return x.Rewardstatus
	}
	return 0
}

//阶段宝箱相关数据
type PBCommonExpBoxInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BoxId        int32               `protobuf:"varint,1,opt,name=box_id,json=boxId,proto3" json:"box_id,omitempty"` //宝箱经验
	BoxExp       int32               `protobuf:"varint,2,opt,name=box_exp,json=boxExp,proto3" json:"box_exp,omitempty"`
	BoxIndexList []*PBCommonKeyValue `protobuf:"bytes,3,rep,name=box_index_list,json=boxIndexList,proto3" json:"box_index_list,omitempty"` //宝箱信息
}

func (x *PBCommonExpBoxInfo) Reset() {
	*x = PBCommonExpBoxInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBCommonExpBoxInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBCommonExpBoxInfo) ProtoMessage() {}

func (x *PBCommonExpBoxInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBCommonExpBoxInfo.ProtoReflect.Descriptor instead.
func (*PBCommonExpBoxInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{28}
}

func (x *PBCommonExpBoxInfo) GetBoxId() int32 {
	if x != nil {
		return x.BoxId
	}
	return 0
}

func (x *PBCommonExpBoxInfo) GetBoxExp() int32 {
	if x != nil {
		return x.BoxExp
	}
	return 0
}

func (x *PBCommonExpBoxInfo) GetBoxIndexList() []*PBCommonKeyValue {
	if x != nil {
		return x.BoxIndexList
	}
	return nil
}

//通用活动信息
type PBActivityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityType   ActivityType   `protobuf:"varint,1,opt,name=activityType,proto3,enum=ActivityType" json:"activityType,omitempty"`       // 活动大类型
	ActivityStatus ActivityStatus `protobuf:"varint,2,opt,name=activityStatus,proto3,enum=ActivityStatus" json:"activityStatus,omitempty"` // 活动的状态
	CurActivityId  int32          `protobuf:"varint,3,opt,name=curActivityId,proto3" json:"curActivityId,omitempty"`                       // 当前活动 ID
	StartTime      int64          `protobuf:"varint,4,opt,name=startTime,proto3" json:"startTime,omitempty"`                               // 活动的开始时间
	EndTime        int64          `protobuf:"varint,5,opt,name=endTime,proto3" json:"endTime,omitempty"`                                   // 活动的结束时间
	PreActivityId  int32          `protobuf:"varint,6,opt,name=preActivityId,proto3" json:"preActivityId,omitempty"`                       // 上一期的活动 ID，业务是做判断清理数据使用。默认为 0，根据活动类型取用
	CloseTime      int32          `protobuf:"varint,7,opt,name=closeTime,proto3" json:"closeTime,omitempty"`                               // 活动的关闭时间
	NextStartTime  int32          `protobuf:"varint,9,opt,name=nextStartTime,proto3" json:"nextStartTime,omitempty"`                       // 下一期开始时间戮，单位：秒
}

func (x *PBActivityInfo) Reset() {
	*x = PBActivityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBActivityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBActivityInfo) ProtoMessage() {}

func (x *PBActivityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBActivityInfo.ProtoReflect.Descriptor instead.
func (*PBActivityInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{29}
}

func (x *PBActivityInfo) GetActivityType() ActivityType {
	if x != nil {
		return x.ActivityType
	}
	return ActivityType_ActivityType_BattlePass
}

func (x *PBActivityInfo) GetActivityStatus() ActivityStatus {
	if x != nil {
		return x.ActivityStatus
	}
	return ActivityStatus_ActivityStatus_NO_OPEN
}

func (x *PBActivityInfo) GetCurActivityId() int32 {
	if x != nil {
		return x.CurActivityId
	}
	return 0
}

func (x *PBActivityInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *PBActivityInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *PBActivityInfo) GetPreActivityId() int32 {
	if x != nil {
		return x.PreActivityId
	}
	return 0
}

func (x *PBActivityInfo) GetCloseTime() int32 {
	if x != nil {
		return x.CloseTime
	}
	return 0
}

func (x *PBActivityInfo) GetNextStartTime() int32 {
	if x != nil {
		return x.NextStartTime
	}
	return 0
}

//其他玩家的基础信息
type PBPlayerBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID       int64  `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"`                   //id
	NickName         string `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`                        //名字
	HeadIcon         string `protobuf:"bytes,3,opt,name=headIcon,proto3" json:"headIcon,omitempty"`                        //头像
	Level            int32  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`                             //等级
	CommValue_0      int64  `protobuf:"varint,5,opt,name=commValue_0,json=commValue0,proto3" json:"commValue_0,omitempty"` //通用的数据信息 目前用于排行的  参数 1
	CommValue_1      int64  `protobuf:"varint,6,opt,name=commValue_1,json=commValue1,proto3" json:"commValue_1,omitempty"` //通用的数据信息 目前用于排行的 参数 1
	DragonSkinId     int32  `protobuf:"varint,7,opt,name=dragonSkinId,proto3" json:"dragonSkinId,omitempty"`               //龙皮肤，默认 0 使用 系统默认
	DressId          int32  `protobuf:"varint,8,opt,name=dressId,proto3" json:"dressId,omitempty"`                         //时装，默认 0 使用 系统默认
	WeaponId         int32  `protobuf:"varint,9,opt,name=weaponId,proto3" json:"weaponId,omitempty"`                       //装备武器 ID，默认 0 使用 系统默认
	Rank             int32  `protobuf:"varint,10,opt,name=rank,proto3" json:"rank,omitempty"`                              //名次
	PromotionId      int32  `protobuf:"varint,11,opt,name=promotionId,proto3" json:"promotionId,omitempty"`                //当前晋升 ID，默认 0 使用 系统默认
	PowCombat        int64  `protobuf:"varint,12,opt,name=powCombat,proto3" json:"powCombat,omitempty"`                    //战力
	Online           int32  `protobuf:"varint,13,opt,name=online,proto3" json:"online,omitempty"`                          //玩家是否在线 1:在线 0:不在线
	OfflineStartTime int32  `protobuf:"varint,14,opt,name=offlineStartTime,proto3" json:"offlineStartTime,omitempty"`      //最后一次离线开始时间戮，单位：秒 在线后会重置为 0
	MainStageId      int32  `protobuf:"varint,15,opt,name=mainStageId,proto3" json:"mainStageId,omitempty"`                //关卡进度
	DressStar        int32  `protobuf:"varint,16,opt,name=dressStar,proto3" json:"dressStar,omitempty"`                    //时装突破
	HeadFrameId      int32  `protobuf:"varint,17,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`                //头像框 id
	TitleId          int32  `protobuf:"varint,18,opt,name=titleId,proto3" json:"titleId,omitempty"`                        //称号 id 0 表示没有称号
}

func (x *PBPlayerBaseInfo) Reset() {
	*x = PBPlayerBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPlayerBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPlayerBaseInfo) ProtoMessage() {}

func (x *PBPlayerBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPlayerBaseInfo.ProtoReflect.Descriptor instead.
func (*PBPlayerBaseInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{30}
}

func (x *PBPlayerBaseInfo) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBPlayerBaseInfo) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBPlayerBaseInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetCommValue_0() int64 {
	if x != nil {
		return x.CommValue_0
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetCommValue_1() int64 {
	if x != nil {
		return x.CommValue_1
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetDragonSkinId() int32 {
	if x != nil {
		return x.DragonSkinId
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetDressId() int32 {
	if x != nil {
		return x.DressId
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetWeaponId() int32 {
	if x != nil {
		return x.WeaponId
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetPromotionId() int32 {
	if x != nil {
		return x.PromotionId
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetOfflineStartTime() int32 {
	if x != nil {
		return x.OfflineStartTime
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetMainStageId() int32 {
	if x != nil {
		return x.MainStageId
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetDressStar() int32 {
	if x != nil {
		return x.DressStar
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

func (x *PBPlayerBaseInfo) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

type PBPlayerOtherInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID      int64  `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"`            //id
	NickName        string `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`                 //名字
	HeadIcon        string `protobuf:"bytes,3,opt,name=headIcon,proto3" json:"headIcon,omitempty"`                 //头像
	Level           int32  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`                      //等级
	DragonSkinId    int32  `protobuf:"varint,5,opt,name=dragonSkinId,proto3" json:"dragonSkinId,omitempty"`        //龙皮肤，默认 0 使用 系统默认
	DressId         int32  `protobuf:"varint,6,opt,name=dressId,proto3" json:"dressId,omitempty"`                  //时装，默认 0 使用 系统默认
	WeaponId        int32  `protobuf:"varint,7,opt,name=weaponId,proto3" json:"weaponId,omitempty"`                //装备武器 ID，默认 0 使用 系统默认
	PromotionId     int32  `protobuf:"varint,8,opt,name=promotionId,proto3" json:"promotionId,omitempty"`          //当前称号 ID，默认 0 使用 系统默认
	PowCombat       int64  `protobuf:"varint,9,opt,name=powCombat,proto3" json:"powCombat,omitempty"`              //战力
	Exp             int64  `protobuf:"varint,10,opt,name=exp,proto3" json:"exp,omitempty"`                         //经验
	DragonSlotLevel int32  `protobuf:"varint,11,opt,name=dragonSlotLevel,proto3" json:"dragonSlotLevel,omitempty"` //龙的基座等级
	TakeOnDragonId  int32  `protobuf:"varint,18,opt,name=takeOnDragonId,proto3" json:"takeOnDragonId,omitempty"`   //上阵龙 ID，默认 0 使用 系统默认
	HasGuild        bool   `protobuf:"varint,19,opt,name=hasGuild,proto3" json:"hasGuild,omitempty"`               //是否有公会 true:有，false:没有
	GuildId         int64  `protobuf:"varint,20,opt,name=guildId,proto3" json:"guildId,omitempty"`                 //公会 ID
	GuildName       string `protobuf:"bytes,21,opt,name=guildName,proto3" json:"guildName,omitempty"`              //公会名称
	GuildLevel      int32  `protobuf:"varint,22,opt,name=guildLevel,proto3" json:"guildLevel,omitempty"`           //公会等级
	GuildIcon       int32  `protobuf:"varint,23,opt,name=guildIcon,proto3" json:"guildIcon,omitempty"`             //公会图标
	ShowInvite      int32  `protobuf:"varint,24,opt,name=showInvite,proto3" json:"showInvite,omitempty"`           //显示邀请按钮 0.不显示 1.发送邀请 2.已发送
	DressStar       int32  `protobuf:"varint,25,opt,name=dressStar,proto3" json:"dressStar,omitempty"`             //时装突破
	HeadFrameId     int32  `protobuf:"varint,26,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`         //头像框 id
	TitleId         int32  `protobuf:"varint,27,opt,name=titleId,proto3" json:"titleId,omitempty"`                 //称号 id 0 表示没有称号
}

func (x *PBPlayerOtherInfo) Reset() {
	*x = PBPlayerOtherInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPlayerOtherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPlayerOtherInfo) ProtoMessage() {}

func (x *PBPlayerOtherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPlayerOtherInfo.ProtoReflect.Descriptor instead.
func (*PBPlayerOtherInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{31}
}

func (x *PBPlayerOtherInfo) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBPlayerOtherInfo) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBPlayerOtherInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetDragonSkinId() int32 {
	if x != nil {
		return x.DragonSkinId
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetDressId() int32 {
	if x != nil {
		return x.DressId
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetWeaponId() int32 {
	if x != nil {
		return x.WeaponId
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetPromotionId() int32 {
	if x != nil {
		return x.PromotionId
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetExp() int64 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetDragonSlotLevel() int32 {
	if x != nil {
		return x.DragonSlotLevel
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetTakeOnDragonId() int32 {
	if x != nil {
		return x.TakeOnDragonId
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetHasGuild() bool {
	if x != nil {
		return x.HasGuild
	}
	return false
}

func (x *PBPlayerOtherInfo) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *PBPlayerOtherInfo) GetGuildLevel() int32 {
	if x != nil {
		return x.GuildLevel
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetGuildIcon() int32 {
	if x != nil {
		return x.GuildIcon
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetShowInvite() int32 {
	if x != nil {
		return x.ShowInvite
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetDressStar() int32 {
	if x != nil {
		return x.DressStar
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

func (x *PBPlayerOtherInfo) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

//======================问卷开始=========================================
//问卷信息
type PBQuestDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //问卷 ID
	Received bool              `protobuf:"varint,2,opt,name=received,proto3" json:"received,omitempty"` //是否填写
	Good     *PBCommonKeyValue `protobuf:"bytes,3,opt,name=good,proto3" json:"good,omitempty"`          //奖励的信息
	Clicked  bool              `protobuf:"varint,4,opt,name=clicked,proto3" json:"clicked,omitempty"`   //是否点击过
	PlayerId uint64            `protobuf:"varint,5,opt,name=playerId,proto3" json:"playerId,omitempty"` // 玩家 ID
}

func (x *PBQuestDataInfo) Reset() {
	*x = PBQuestDataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBQuestDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBQuestDataInfo) ProtoMessage() {}

func (x *PBQuestDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBQuestDataInfo.ProtoReflect.Descriptor instead.
func (*PBQuestDataInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{32}
}

func (x *PBQuestDataInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBQuestDataInfo) GetReceived() bool {
	if x != nil {
		return x.Received
	}
	return false
}

func (x *PBQuestDataInfo) GetGood() *PBCommonKeyValue {
	if x != nil {
		return x.Good
	}
	return nil
}

func (x *PBQuestDataInfo) GetClicked() bool {
	if x != nil {
		return x.Clicked
	}
	return false
}

func (x *PBQuestDataInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

//======================聊天=========================================
//聊天信息
type PBChatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID  int64       `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"` //id
	NickName    string      `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`      //名字
	HeadIcon    string      `protobuf:"bytes,3,opt,name=headIcon,proto3" json:"headIcon,omitempty"`      //头像
	Level       int32       `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`           //等级
	SendTime    int32       `protobuf:"varint,5,opt,name=sendTime,proto3" json:"sendTime,omitempty"`     // 时间戳
	Content     string      `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	MsgID       int64       `protobuf:"varint,7,opt,name=msgID,proto3" json:"msgID,omitempty"`                      //编号
	Read        int32       `protobuf:"varint,8,opt,name=read,proto3" json:"read,omitempty"`                        //已读 1 未读 0
	MsgType     ChatMsgType `protobuf:"varint,9,opt,name=msgType,proto3,enum=ChatMsgType" json:"msgType,omitempty"` //信息类型
	GuildId     int64       `protobuf:"varint,10,opt,name=guildId,proto3" json:"guildId,omitempty"`                 //公会 ID
	GuildName   string      `protobuf:"bytes,11,opt,name=guildName,proto3" json:"guildName,omitempty"`              //公会名称
	GuildLevel  int32       `protobuf:"varint,12,opt,name=guildLevel,proto3" json:"guildLevel,omitempty"`           //公会等级
	GuildIcon   int32       `protobuf:"varint,13,opt,name=guildIcon,proto3" json:"guildIcon,omitempty"`             //公会图标
	HeadFrameId int32       `protobuf:"varint,14,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`         //头像框 id
	TitleId     int32       `protobuf:"varint,15,opt,name=titleId,proto3" json:"titleId,omitempty"`                 //称号 ID 0 表示当前没有称号
}

func (x *PBChatInfo) Reset() {
	*x = PBChatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBChatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBChatInfo) ProtoMessage() {}

func (x *PBChatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBChatInfo.ProtoReflect.Descriptor instead.
func (*PBChatInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{33}
}

func (x *PBChatInfo) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *PBChatInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBChatInfo) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBChatInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBChatInfo) GetSendTime() int32 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *PBChatInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *PBChatInfo) GetMsgID() int64 {
	if x != nil {
		return x.MsgID
	}
	return 0
}

func (x *PBChatInfo) GetRead() int32 {
	if x != nil {
		return x.Read
	}
	return 0
}

func (x *PBChatInfo) GetMsgType() ChatMsgType {
	if x != nil {
		return x.MsgType
	}
	return ChatMsgType_ChatMsgType_Text
}

func (x *PBChatInfo) GetGuildId() int64 {
	if x != nil {
		return x.GuildId
	}
	return 0
}

func (x *PBChatInfo) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *PBChatInfo) GetGuildLevel() int32 {
	if x != nil {
		return x.GuildLevel
	}
	return 0
}

func (x *PBChatInfo) GetGuildIcon() int32 {
	if x != nil {
		return x.GuildIcon
	}
	return 0
}

func (x *PBChatInfo) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

func (x *PBChatInfo) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

//聊天信息
type PBPrivateChatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatPlayerId int64         `protobuf:"varint,1,opt,name=chatPlayerId,proto3" json:"chatPlayerId,omitempty"` //id
	Chatinfo     []*PBChatInfo `protobuf:"bytes,2,rep,name=chatinfo,proto3" json:"chatinfo,omitempty"`
	NickName     string        `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`        //名字
	HeadIcon     string        `protobuf:"bytes,4,opt,name=headIcon,proto3" json:"headIcon,omitempty"`        //头像
	Level        int32         `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`             //等级
	HeadFrameId  int32         `protobuf:"varint,6,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"` //头像框 id
	TitleId      int32         `protobuf:"varint,7,opt,name=titleId,proto3" json:"titleId,omitempty"`         //称号 ID 0 表示当前没有称号
}

func (x *PBPrivateChatInfo) Reset() {
	*x = PBPrivateChatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPrivateChatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPrivateChatInfo) ProtoMessage() {}

func (x *PBPrivateChatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPrivateChatInfo.ProtoReflect.Descriptor instead.
func (*PBPrivateChatInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{34}
}

func (x *PBPrivateChatInfo) GetChatPlayerId() int64 {
	if x != nil {
		return x.ChatPlayerId
	}
	return 0
}

func (x *PBPrivateChatInfo) GetChatinfo() []*PBChatInfo {
	if x != nil {
		return x.Chatinfo
	}
	return nil
}

func (x *PBPrivateChatInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBPrivateChatInfo) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBPrivateChatInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBPrivateChatInfo) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

func (x *PBPrivateChatInfo) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

//======================聊天=========================================
//======================公会系统开始=========================================
//公会成员信息
type PBGuildMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID   int64         `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"`         //id
	NickName     string        `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`              //名字
	HeadIcon     string        `protobuf:"bytes,3,opt,name=headIcon,proto3" json:"headIcon,omitempty"`              //头像
	Level        int32         `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`                   //等级
	DragonSkinId int32         `protobuf:"varint,5,opt,name=dragonSkinId,proto3" json:"dragonSkinId,omitempty"`     //龙皮肤，默认 0 使用 系统默认
	DressId      int32         `protobuf:"varint,6,opt,name=dressId,proto3" json:"dressId,omitempty"`               //时装，默认 0 使用 系统默认
	WeaponId     int32         `protobuf:"varint,7,opt,name=weaponId,proto3" json:"weaponId,omitempty"`             //装备武器 ID，默认 0 使用 系统默认
	PromotionId  int32         `protobuf:"varint,8,opt,name=promotionId,proto3" json:"promotionId,omitempty"`       //当前称号 ID，默认 0 使用 系统默认
	PowCombat    int64         `protobuf:"varint,9,opt,name=powCombat,proto3" json:"powCombat,omitempty"`           //战力
	Online       bool          `protobuf:"varint,10,opt,name=online,proto3" json:"online,omitempty"`                //是否在线 true:在线，false:离线读取下面的离线时间
	OfflineTime  int32         `protobuf:"varint,11,opt,name=offlineTime,proto3" json:"offlineTime,omitempty"`      //累积的离线时长，单位：秒
	Contribution int64         `protobuf:"varint,12,opt,name=contribution,proto3" json:"contribution,omitempty"`    //当前贡献度
	Gpos         GuildPosition `protobuf:"varint,13,opt,name=gpos,proto3,enum=GuildPosition" json:"gpos,omitempty"` //当前身份
	MainStageId  int32         `protobuf:"varint,14,opt,name=mainStageId,proto3" json:"mainStageId,omitempty"`      //当前主线关卡 ID
	TitleId      int32         `protobuf:"varint,15,opt,name=titleId,proto3" json:"titleId,omitempty"`              //称号 id 0 表示没有称号 默认 0 使用 系统默认
	HeadFrameId  int32         `protobuf:"varint,16,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`      //头像框 id 默认 0 使用 系统默认
}

func (x *PBGuildMember) Reset() {
	*x = PBGuildMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildMember) ProtoMessage() {}

func (x *PBGuildMember) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildMember.ProtoReflect.Descriptor instead.
func (*PBGuildMember) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{35}
}

func (x *PBGuildMember) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *PBGuildMember) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBGuildMember) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBGuildMember) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildMember) GetDragonSkinId() int32 {
	if x != nil {
		return x.DragonSkinId
	}
	return 0
}

func (x *PBGuildMember) GetDressId() int32 {
	if x != nil {
		return x.DressId
	}
	return 0
}

func (x *PBGuildMember) GetWeaponId() int32 {
	if x != nil {
		return x.WeaponId
	}
	return 0
}

func (x *PBGuildMember) GetPromotionId() int32 {
	if x != nil {
		return x.PromotionId
	}
	return 0
}

func (x *PBGuildMember) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBGuildMember) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *PBGuildMember) GetOfflineTime() int32 {
	if x != nil {
		return x.OfflineTime
	}
	return 0
}

func (x *PBGuildMember) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *PBGuildMember) GetGpos() GuildPosition {
	if x != nil {
		return x.Gpos
	}
	return GuildPosition_GuildPosition_Normal
}

func (x *PBGuildMember) GetMainStageId() int32 {
	if x != nil {
		return x.MainStageId
	}
	return 0
}

func (x *PBGuildMember) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

func (x *PBGuildMember) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

//公会推荐列表用
type PBGuildRecommend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                           //名称
	Level             int32  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                        //等级
	IconId            int32  `protobuf:"varint,3,opt,name=iconId,proto3" json:"iconId,omitempty"`                      //图标
	ShowId            int32  `protobuf:"varint,4,opt,name=showId,proto3" json:"showId,omitempty"`                      //显示 ID
	Id                int64  `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`                              //ID，提交用
	Notice            string `protobuf:"bytes,6,opt,name=notice,proto3" json:"notice,omitempty"`                       //公会宣言
	PresidentNickName string `protobuf:"bytes,7,opt,name=presidentNickName,proto3" json:"presidentNickName,omitempty"` //公会会长名字
	PowCombat         int64  `protobuf:"varint,8,opt,name=powCombat,proto3" json:"powCombat,omitempty"`                //总战斗力
	MemberCount       int32  `protobuf:"varint,9,opt,name=memberCount,proto3" json:"memberCount,omitempty"`            //当前人数
	MemberMaxCount    int32  `protobuf:"varint,10,opt,name=memberMaxCount,proto3" json:"memberMaxCount,omitempty"`     //人数上限
	FreeJoin          bool   `protobuf:"varint,11,opt,name=freeJoin,proto3" json:"freeJoin,omitempty"`                 //true 自由加入 false 需要审批
	ReqStage          int32  `protobuf:"varint,12,opt,name=reqStage,proto3" json:"reqStage,omitempty"`                 //审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱
	HasApply          bool   `protobuf:"varint,13,opt,name=hasApply,proto3" json:"hasApply,omitempty"`                 //true 表示已申请，false 未申请
}

func (x *PBGuildRecommend) Reset() {
	*x = PBGuildRecommend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildRecommend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildRecommend) ProtoMessage() {}

func (x *PBGuildRecommend) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildRecommend.ProtoReflect.Descriptor instead.
func (*PBGuildRecommend) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{36}
}

func (x *PBGuildRecommend) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PBGuildRecommend) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildRecommend) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *PBGuildRecommend) GetShowId() int32 {
	if x != nil {
		return x.ShowId
	}
	return 0
}

func (x *PBGuildRecommend) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBGuildRecommend) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

func (x *PBGuildRecommend) GetPresidentNickName() string {
	if x != nil {
		return x.PresidentNickName
	}
	return ""
}

func (x *PBGuildRecommend) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBGuildRecommend) GetMemberCount() int32 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

func (x *PBGuildRecommend) GetMemberMaxCount() int32 {
	if x != nil {
		return x.MemberMaxCount
	}
	return 0
}

func (x *PBGuildRecommend) GetFreeJoin() bool {
	if x != nil {
		return x.FreeJoin
	}
	return false
}

func (x *PBGuildRecommend) GetReqStage() int32 {
	if x != nil {
		return x.ReqStage
	}
	return 0
}

func (x *PBGuildRecommend) GetHasApply() bool {
	if x != nil {
		return x.HasApply
	}
	return false
}

//公会申请信息
type PBGuildApply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID   int64  `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"`     //id
	NickName     string `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`          //名字
	HeadIcon     string `protobuf:"bytes,3,opt,name=headIcon,proto3" json:"headIcon,omitempty"`          //头像
	Level        int32  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`               //等级
	DragonSkinId int32  `protobuf:"varint,5,opt,name=dragonSkinId,proto3" json:"dragonSkinId,omitempty"` //龙皮肤，默认 0 使用 系统默认
	DressId      int32  `protobuf:"varint,6,opt,name=dressId,proto3" json:"dressId,omitempty"`           //时装，默认 0 使用 系统默认
	WeaponId     int32  `protobuf:"varint,7,opt,name=weaponId,proto3" json:"weaponId,omitempty"`         //装备武器 ID，默认 0 使用 系统默认
	PromotionId  int32  `protobuf:"varint,8,opt,name=promotionId,proto3" json:"promotionId,omitempty"`   //当前称号 ID，默认 0 使用 系统默认
	PowCombat    int64  `protobuf:"varint,9,opt,name=powCombat,proto3" json:"powCombat,omitempty"`       //战力
	ApplyTime    int64  `protobuf:"varint,10,opt,name=applyTime,proto3" json:"applyTime,omitempty"`      // 申请时间戳 (Unix timestamp, 秒)
	MainStageId  int32  `protobuf:"varint,11,opt,name=mainStageId,proto3" json:"mainStageId,omitempty"`  //当前主线关卡 ID
	TitleId      int32  `protobuf:"varint,12,opt,name=titleId,proto3" json:"titleId,omitempty"`          //称号 id 0 表示没有称号 默认 0 使用 系统默认
	HeadFrameId  int32  `protobuf:"varint,13,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`  //头像框 id 默认 0 使用 系统默认
}

func (x *PBGuildApply) Reset() {
	*x = PBGuildApply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildApply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildApply) ProtoMessage() {}

func (x *PBGuildApply) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildApply.ProtoReflect.Descriptor instead.
func (*PBGuildApply) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{37}
}

func (x *PBGuildApply) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *PBGuildApply) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBGuildApply) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBGuildApply) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildApply) GetDragonSkinId() int32 {
	if x != nil {
		return x.DragonSkinId
	}
	return 0
}

func (x *PBGuildApply) GetDressId() int32 {
	if x != nil {
		return x.DressId
	}
	return 0
}

func (x *PBGuildApply) GetWeaponId() int32 {
	if x != nil {
		return x.WeaponId
	}
	return 0
}

func (x *PBGuildApply) GetPromotionId() int32 {
	if x != nil {
		return x.PromotionId
	}
	return 0
}

func (x *PBGuildApply) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBGuildApply) GetApplyTime() int64 {
	if x != nil {
		return x.ApplyTime
	}
	return 0
}

func (x *PBGuildApply) GetMainStageId() int32 {
	if x != nil {
		return x.MainStageId
	}
	return 0
}

func (x *PBGuildApply) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

func (x *PBGuildApply) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

// 公会详细信息
type PBGuildDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                                          // 名称
	Level             int32  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                                                       // 等级
	IconId            int32  `protobuf:"varint,3,opt,name=icon_id,json=iconId,proto3" json:"icon_id,omitempty"`                                       // 徽章 ID
	ShowId            int32  `protobuf:"varint,4,opt,name=show_id,json=showId,proto3" json:"show_id,omitempty"`                                       // 显示 ID (联盟的唯一数字ID)
	Id                int64  `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`                                                             // 联盟的数据库唯一ID
	Notice            string `protobuf:"bytes,6,opt,name=notice,proto3" json:"notice,omitempty"`                                                      // 联盟宣言
	Announcement      string `protobuf:"bytes,7,opt,name=announcement,proto3" json:"announcement,omitempty"`                                          // 联盟公告
	PresidentNickName string `protobuf:"bytes,8,opt,name=president_nick_name,json=presidentNickName,proto3" json:"president_nick_name,omitempty"`     // 公会会长名字 (可从UserSnap获取后填充)
	PresidentUid      uint64 `protobuf:"varint,20,opt,name=president_uid,json=presidentUid,proto3" json:"president_uid,omitempty"`                    // 会长UID (新增，便于客户端跳转会长信息等)
	PowCombat         int64  `protobuf:"varint,9,opt,name=pow_combat,json=powCombat,proto3" json:"pow_combat,omitempty"`                              // 总战斗力 (需要GuildSystem计算或维护)
	MemberCount       int32  `protobuf:"varint,10,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`                       // 当前人数
	MemberMaxCount    int32  `protobuf:"varint,11,opt,name=member_max_count,json=memberMaxCount,proto3" json:"member_max_count,omitempty"`            // 人数上限
	FreeJoin          bool   `protobuf:"varint,12,opt,name=free_join,json=freeJoin,proto3" json:"free_join,omitempty"`                                // true 自由加入 false 需要审批
	ReqStage          int32  `protobuf:"varint,13,opt,name=req_stage,json=reqStage,proto3" json:"req_stage,omitempty"`                                // 审批时需具备的关卡条件
	Exp               int64  `protobuf:"varint,14,opt,name=exp,proto3" json:"exp,omitempty"`                                                          // 当前经验
	MaxExp            int64  `protobuf:"varint,15,opt,name=max_exp,json=maxExp,proto3" json:"max_exp,omitempty"`                                      // 当前等级升级所需总经验
	TodayJoinedCount  int32  `protobuf:"varint,16,opt,name=today_joined_count,json=todayJoinedCount,proto3" json:"today_joined_count,omitempty"`      // 今日已通过申请/快速加入的人数
	DailyMaxJoinLimit int32  `protobuf:"varint,17,opt,name=daily_max_join_limit,json=dailyMaxJoinLimit,proto3" json:"daily_max_join_limit,omitempty"` // 服务器写死的每日最大入盟人数上限
	Contribution      int64  `protobuf:"varint,18,opt,name=contribution,proto3" json:"contribution,omitempty"`                                        // 贡献度
}

func (x *PBGuildDetailInfo) Reset() {
	*x = PBGuildDetailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildDetailInfo) ProtoMessage() {}

func (x *PBGuildDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildDetailInfo.ProtoReflect.Descriptor instead.
func (*PBGuildDetailInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{38}
}

func (x *PBGuildDetailInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PBGuildDetailInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildDetailInfo) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *PBGuildDetailInfo) GetShowId() int32 {
	if x != nil {
		return x.ShowId
	}
	return 0
}

func (x *PBGuildDetailInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBGuildDetailInfo) GetNotice() string {
	if x != nil {
		return x.Notice
	}
	return ""
}

func (x *PBGuildDetailInfo) GetAnnouncement() string {
	if x != nil {
		return x.Announcement
	}
	return ""
}

func (x *PBGuildDetailInfo) GetPresidentNickName() string {
	if x != nil {
		return x.PresidentNickName
	}
	return ""
}

func (x *PBGuildDetailInfo) GetPresidentUid() uint64 {
	if x != nil {
		return x.PresidentUid
	}
	return 0
}

func (x *PBGuildDetailInfo) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBGuildDetailInfo) GetMemberCount() int32 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

func (x *PBGuildDetailInfo) GetMemberMaxCount() int32 {
	if x != nil {
		return x.MemberMaxCount
	}
	return 0
}

func (x *PBGuildDetailInfo) GetFreeJoin() bool {
	if x != nil {
		return x.FreeJoin
	}
	return false
}

func (x *PBGuildDetailInfo) GetReqStage() int32 {
	if x != nil {
		return x.ReqStage
	}
	return 0
}

func (x *PBGuildDetailInfo) GetExp() int64 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *PBGuildDetailInfo) GetMaxExp() int64 {
	if x != nil {
		return x.MaxExp
	}
	return 0
}

func (x *PBGuildDetailInfo) GetTodayJoinedCount() int32 {
	if x != nil {
		return x.TodayJoinedCount
	}
	return 0
}

func (x *PBGuildDetailInfo) GetDailyMaxJoinLimit() int32 {
	if x != nil {
		return x.DailyMaxJoinLimit
	}
	return 0
}

func (x *PBGuildDetailInfo) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

//公会排行列表用
type PBGuildRankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name              string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                           //名称
	Level             int32  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                        //等级
	IconId            int32  `protobuf:"varint,3,opt,name=iconId,proto3" json:"iconId,omitempty"`                      //图标
	ShowId            int32  `protobuf:"varint,4,opt,name=showId,proto3" json:"showId,omitempty"`                      //显示 ID
	Id                int64  `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`                              //ID，提交用
	PresidentNickName string `protobuf:"bytes,6,opt,name=presidentNickName,proto3" json:"presidentNickName,omitempty"` //公会会长名字
	PowCombat         int64  `protobuf:"varint,7,opt,name=powCombat,proto3" json:"powCombat,omitempty"`                //总战斗力
	IsSelfGuild       bool   `protobuf:"varint,8,opt,name=isSelfGuild,proto3" json:"isSelfGuild,omitempty"`            //true 表示自己所在的公会，false 不是自己公会
	Rank              int32  `protobuf:"varint,9,opt,name=rank,proto3" json:"rank,omitempty"`                          //排名从 1 开始
}

func (x *PBGuildRankInfo) Reset() {
	*x = PBGuildRankInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildRankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildRankInfo) ProtoMessage() {}

func (x *PBGuildRankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildRankInfo.ProtoReflect.Descriptor instead.
func (*PBGuildRankInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{39}
}

func (x *PBGuildRankInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PBGuildRankInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildRankInfo) GetIconId() int32 {
	if x != nil {
		return x.IconId
	}
	return 0
}

func (x *PBGuildRankInfo) GetShowId() int32 {
	if x != nil {
		return x.ShowId
	}
	return 0
}

func (x *PBGuildRankInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBGuildRankInfo) GetPresidentNickName() string {
	if x != nil {
		return x.PresidentNickName
	}
	return ""
}

func (x *PBGuildRankInfo) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBGuildRankInfo) GetIsSelfGuild() bool {
	if x != nil {
		return x.IsSelfGuild
	}
	return false
}

func (x *PBGuildRankInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

//公会科技
type PBGuildTech struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         //科技配表 ID
	Level  int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`   //当前等级
	OwnPro int32 `protobuf:"varint,3,opt,name=OwnPro,proto3" json:"OwnPro,omitempty"` //拥有属性数值（默认值都填为 0）
}

func (x *PBGuildTech) Reset() {
	*x = PBGuildTech{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildTech) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildTech) ProtoMessage() {}

func (x *PBGuildTech) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildTech.ProtoReflect.Descriptor instead.
func (*PBGuildTech) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{40}
}

func (x *PBGuildTech) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBGuildTech) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildTech) GetOwnPro() int32 {
	if x != nil {
		return x.OwnPro
	}
	return 0
}

//公会科技升级信息
type PBGuildTechLevelup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`               //科技配表 ID
	Level     int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`         //当前等级
	OwnPro    int32 `protobuf:"varint,3,opt,name=OwnPro,proto3" json:"OwnPro,omitempty"`       //拥有属性数值（默认值都填为 0）
	OldLevel  int32 `protobuf:"varint,4,opt,name=oldLevel,proto3" json:"oldLevel,omitempty"`   //旧的等级
	OldOwnPro int32 `protobuf:"varint,5,opt,name=OldOwnPro,proto3" json:"OldOwnPro,omitempty"` //旧的属性值
}

func (x *PBGuildTechLevelup) Reset() {
	*x = PBGuildTechLevelup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildTechLevelup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildTechLevelup) ProtoMessage() {}

func (x *PBGuildTechLevelup) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildTechLevelup.ProtoReflect.Descriptor instead.
func (*PBGuildTechLevelup) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{41}
}

func (x *PBGuildTechLevelup) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBGuildTechLevelup) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildTechLevelup) GetOwnPro() int32 {
	if x != nil {
		return x.OwnPro
	}
	return 0
}

func (x *PBGuildTechLevelup) GetOldLevel() int32 {
	if x != nil {
		return x.OldLevel
	}
	return 0
}

func (x *PBGuildTechLevelup) GetOldOwnPro() int32 {
	if x != nil {
		return x.OldOwnPro
	}
	return 0
}

//公会日志
type PBGuildLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       GuildLogType `protobuf:"varint,1,opt,name=type,proto3,enum=GuildLogType" json:"type,omitempty"` //日志类型
	CreateTime int32        `protobuf:"varint,2,opt,name=createTime,proto3" json:"createTime,omitempty"`       //日志产生开始时间戮，单位：秒
	PlatformId int64        `protobuf:"varint,3,opt,name=platformId,proto3" json:"platformId,omitempty"`       //玩家 ID
	NickName   string       `protobuf:"bytes,4,opt,name=nickName,proto3" json:"nickName,omitempty"`            //玩家名字
	Param1     int32        `protobuf:"varint,5,opt,name=param1,proto3" json:"param1,omitempty"`               //参数 1：经验
}

func (x *PBGuildLog) Reset() {
	*x = PBGuildLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildLog) ProtoMessage() {}

func (x *PBGuildLog) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildLog.ProtoReflect.Descriptor instead.
func (*PBGuildLog) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{42}
}

func (x *PBGuildLog) GetType() GuildLogType {
	if x != nil {
		return x.Type
	}
	return GuildLogType_GuildLogType_Leave
}

func (x *PBGuildLog) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PBGuildLog) GetPlatformId() int64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *PBGuildLog) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBGuildLog) GetParam1() int32 {
	if x != nil {
		return x.Param1
	}
	return 0
}

//公会砍价礼包详情
type PBBargainingGift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    int64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                       //唯一 ID，购买和砍价传入
	TableId               int32                 `protobuf:"varint,2,opt,name=tableId,proto3" json:"tableId,omitempty"`                             //商品配表 ID
	CurPrice              int32                 `protobuf:"varint,3,opt,name=curPrice,proto3" json:"curPrice,omitempty"`                           //当前价格
	BargainingTotalPrice  int32                 `protobuf:"varint,4,opt,name=bargainingTotalPrice,proto3" json:"bargainingTotalPrice,omitempty"`   //累计砍价
	BargainingPlayerCount int32                 `protobuf:"varint,5,opt,name=bargainingPlayerCount,proto3" json:"bargainingPlayerCount,omitempty"` //已参与的砍价人数
	BargainingPlayerLimit int32                 `protobuf:"varint,6,opt,name=bargainingPlayerLimit,proto3" json:"bargainingPlayerLimit,omitempty"` //砍价上限人数
	Players               []*PBBargainingPlayer `protobuf:"bytes,7,rep,name=players,proto3" json:"players,omitempty"`                              //已参与人的列表信息，已排好序
	NoBargainingPlayers   []*PBBargainingPlayer `protobuf:"bytes,8,rep,name=noBargainingPlayers,proto3" json:"noBargainingPlayers,omitempty"`      //未参与人的列表信息
	NextNoticeTime        int32                 `protobuf:"varint,9,opt,name=nextNoticeTime,proto3" json:"nextNoticeTime,omitempty"`               //下一次通知未砍价的时间戮，单位：秒，默认为 0，表示可以立即通知。
	BuyGift               bool                  `protobuf:"varint,10,opt,name=buyGift,proto3" json:"buyGift,omitempty"`                            //是否已购买过，True：已购买 False:未购买
	BargainingGift        bool                  `protobuf:"varint,11,opt,name=bargainingGift,proto3" json:"bargainingGift,omitempty"`              //是否已砍价过，True：已砍价 False:未砍价
}

func (x *PBBargainingGift) Reset() {
	*x = PBBargainingGift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBargainingGift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBargainingGift) ProtoMessage() {}

func (x *PBBargainingGift) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBargainingGift.ProtoReflect.Descriptor instead.
func (*PBBargainingGift) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{43}
}

func (x *PBBargainingGift) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBBargainingGift) GetTableId() int32 {
	if x != nil {
		return x.TableId
	}
	return 0
}

func (x *PBBargainingGift) GetCurPrice() int32 {
	if x != nil {
		return x.CurPrice
	}
	return 0
}

func (x *PBBargainingGift) GetBargainingTotalPrice() int32 {
	if x != nil {
		return x.BargainingTotalPrice
	}
	return 0
}

func (x *PBBargainingGift) GetBargainingPlayerCount() int32 {
	if x != nil {
		return x.BargainingPlayerCount
	}
	return 0
}

func (x *PBBargainingGift) GetBargainingPlayerLimit() int32 {
	if x != nil {
		return x.BargainingPlayerLimit
	}
	return 0
}

func (x *PBBargainingGift) GetPlayers() []*PBBargainingPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *PBBargainingGift) GetNoBargainingPlayers() []*PBBargainingPlayer {
	if x != nil {
		return x.NoBargainingPlayers
	}
	return nil
}

func (x *PBBargainingGift) GetNextNoticeTime() int32 {
	if x != nil {
		return x.NextNoticeTime
	}
	return 0
}

func (x *PBBargainingGift) GetBuyGift() bool {
	if x != nil {
		return x.BuyGift
	}
	return false
}

func (x *PBBargainingGift) GetBargainingGift() bool {
	if x != nil {
		return x.BargainingGift
	}
	return false
}

//公会砍价人详情
type PBBargainingPlayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformId      int64  `protobuf:"varint,1,opt,name=platformId,proto3" json:"platformId,omitempty"`           //玩家 ID
	NickName        string `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`                //玩家名字
	BargainingPrice int32  `protobuf:"varint,3,opt,name=bargainingPrice,proto3" json:"bargainingPrice,omitempty"` //砍掉的价格数（根据需求读取对应字段）
	Online          bool   `protobuf:"varint,4,opt,name=online,proto3" json:"online,omitempty"`                   //是否在线 true:在线，false:离线读取下面的离线时间（根据需求读取对应字段）
	OfflineTime     int32  `protobuf:"varint,5,opt,name=offlineTime,proto3" json:"offlineTime,omitempty"`         //累积的离线时长，单位：秒（根据需求读取对应字段）
}

func (x *PBBargainingPlayer) Reset() {
	*x = PBBargainingPlayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBargainingPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBargainingPlayer) ProtoMessage() {}

func (x *PBBargainingPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBargainingPlayer.ProtoReflect.Descriptor instead.
func (*PBBargainingPlayer) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{44}
}

func (x *PBBargainingPlayer) GetPlatformId() int64 {
	if x != nil {
		return x.PlatformId
	}
	return 0
}

func (x *PBBargainingPlayer) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBBargainingPlayer) GetBargainingPrice() int32 {
	if x != nil {
		return x.BargainingPrice
	}
	return 0
}

func (x *PBBargainingPlayer) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *PBBargainingPlayer) GetOfflineTime() int32 {
	if x != nil {
		return x.OfflineTime
	}
	return 0
}

//公会 BOSs 排行成员信息
type PBGuildBossRankPlayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlatformID  int64  `protobuf:"varint,1,opt,name=platformID,proto3" json:"platformID,omitempty"`    //id
	NickName    string `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`         //名字
	HeadIcon    string `protobuf:"bytes,3,opt,name=headIcon,proto3" json:"headIcon,omitempty"`         //头像
	Level       int32  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`              //等级
	PromotionId int32  `protobuf:"varint,5,opt,name=promotionId,proto3" json:"promotionId,omitempty"`  //当前称号 ID，默认 0 使用 系统默认
	PowCombat   int64  `protobuf:"varint,6,opt,name=powCombat,proto3" json:"powCombat,omitempty"`      //战力
	TotalDamage int64  `protobuf:"varint,7,opt,name=totalDamage,proto3" json:"totalDamage,omitempty"`  //总伤害值
	Rank        int32  `protobuf:"varint,8,opt,name=rank,proto3" json:"rank,omitempty"`                //排名从 1 开始
	TitleId     int32  `protobuf:"varint,9,opt,name=titleId,proto3" json:"titleId,omitempty"`          //称号 id 0 表示没有称号 默认 0 使用 系统默认
	HeadFrameId int32  `protobuf:"varint,10,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"` //头像框 id 默认 0 使用 系统默认
}

func (x *PBGuildBossRankPlayer) Reset() {
	*x = PBGuildBossRankPlayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildBossRankPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildBossRankPlayer) ProtoMessage() {}

func (x *PBGuildBossRankPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildBossRankPlayer.ProtoReflect.Descriptor instead.
func (*PBGuildBossRankPlayer) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{45}
}

func (x *PBGuildBossRankPlayer) GetPlatformID() int64 {
	if x != nil {
		return x.PlatformID
	}
	return 0
}

func (x *PBGuildBossRankPlayer) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *PBGuildBossRankPlayer) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBGuildBossRankPlayer) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBGuildBossRankPlayer) GetPromotionId() int32 {
	if x != nil {
		return x.PromotionId
	}
	return 0
}

func (x *PBGuildBossRankPlayer) GetPowCombat() int64 {
	if x != nil {
		return x.PowCombat
	}
	return 0
}

func (x *PBGuildBossRankPlayer) GetTotalDamage() int64 {
	if x != nil {
		return x.TotalDamage
	}
	return 0
}

func (x *PBGuildBossRankPlayer) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *PBGuildBossRankPlayer) GetTitleId() int32 {
	if x != nil {
		return x.TitleId
	}
	return 0
}

func (x *PBGuildBossRankPlayer) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

//公会购买记录
type PBGuildShopOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FreeCount   int32 `protobuf:"varint,2,opt,name=freeCount,proto3" json:"freeCount,omitempty"`
	FreeAdCount int32 `protobuf:"varint,3,opt,name=freeAdCount,proto3" json:"freeAdCount,omitempty"`
	BuyCount    int32 `protobuf:"varint,4,opt,name=buyCount,proto3" json:"buyCount,omitempty"`
}

func (x *PBGuildShopOrder) Reset() {
	*x = PBGuildShopOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGuildShopOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGuildShopOrder) ProtoMessage() {}

func (x *PBGuildShopOrder) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGuildShopOrder.ProtoReflect.Descriptor instead.
func (*PBGuildShopOrder) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{46}
}

func (x *PBGuildShopOrder) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBGuildShopOrder) GetFreeCount() int32 {
	if x != nil {
		return x.FreeCount
	}
	return 0
}

func (x *PBGuildShopOrder) GetFreeAdCount() int32 {
	if x != nil {
		return x.FreeAdCount
	}
	return 0
}

func (x *PBGuildShopOrder) GetBuyCount() int32 {
	if x != nil {
		return x.BuyCount
	}
	return 0
}

//======================好友=========================================
// 领取礼物界面的每条信息
type PBGiftItemData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerUid int64  `protobuf:"varint,1,opt,name=playerUid,proto3" json:"playerUid,omitempty"` //玩家 Uid
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`            // 玩家名字
	PlayerLv  int32  `protobuf:"varint,3,opt,name=playerLv,proto3" json:"playerLv,omitempty"`   // 玩家等级
	Time      int64  `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`           //送礼时间戳
	HeadIcon  string `protobuf:"bytes,5,opt,name=headIcon,proto3" json:"headIcon,omitempty"`    //头像
	ItemId    int32  `protobuf:"varint,6,opt,name=itemId,proto3" json:"itemId,omitempty"`       //领取礼物道具 id（策划说现在是钻石，这个字段可以留以后用）
	ItemNum   int32  `protobuf:"varint,7,opt,name=itemNum,proto3" json:"itemNum,omitempty"`     //领取礼物的数量
}

func (x *PBGiftItemData) Reset() {
	*x = PBGiftItemData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGiftItemData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGiftItemData) ProtoMessage() {}

func (x *PBGiftItemData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGiftItemData.ProtoReflect.Descriptor instead.
func (*PBGiftItemData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{47}
}

func (x *PBGiftItemData) GetPlayerUid() int64 {
	if x != nil {
		return x.PlayerUid
	}
	return 0
}

func (x *PBGiftItemData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PBGiftItemData) GetPlayerLv() int32 {
	if x != nil {
		return x.PlayerLv
	}
	return 0
}

func (x *PBGiftItemData) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *PBGiftItemData) GetHeadIcon() string {
	if x != nil {
		return x.HeadIcon
	}
	return ""
}

func (x *PBGiftItemData) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *PBGiftItemData) GetItemNum() int32 {
	if x != nil {
		return x.ItemNum
	}
	return 0
}

//======================好友=========================================
//======================头像开始==========================================
//头像信息
type PBHeadIconDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeadIconId int32 `protobuf:"varint,1,opt,name=headIconId,proto3" json:"headIconId,omitempty"` //头像 id
	Status     int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`         //拥有状态：0:等待解锁 1：已解锁，可正常使用。
}

func (x *PBHeadIconDataInfo) Reset() {
	*x = PBHeadIconDataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBHeadIconDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBHeadIconDataInfo) ProtoMessage() {}

func (x *PBHeadIconDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBHeadIconDataInfo.ProtoReflect.Descriptor instead.
func (*PBHeadIconDataInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{48}
}

func (x *PBHeadIconDataInfo) GetHeadIconId() int32 {
	if x != nil {
		return x.HeadIconId
	}
	return 0
}

func (x *PBHeadIconDataInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

//======================头像结束==========================================
//======================头像框开始==========================================
//头像框信息
type PBHeadFrameInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeadFrameId     int32 `protobuf:"varint,1,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`         //头像 id
	Status          int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`                   //拥有状态：0:等待解锁 1：已解锁，可正常使用。
	FrameExpireTime int64 `protobuf:"varint,3,opt,name=frameExpireTime,proto3" json:"frameExpireTime,omitempty"` //到期时间戳  0 代表不过期
}

func (x *PBHeadFrameInfo) Reset() {
	*x = PBHeadFrameInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBHeadFrameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBHeadFrameInfo) ProtoMessage() {}

func (x *PBHeadFrameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBHeadFrameInfo.ProtoReflect.Descriptor instead.
func (*PBHeadFrameInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{49}
}

func (x *PBHeadFrameInfo) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

func (x *PBHeadFrameInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PBHeadFrameInfo) GetFrameExpireTime() int64 {
	if x != nil {
		return x.FrameExpireTime
	}
	return 0
}

//======================七日签到开始==========================================
// 七日签到信息
type PBSevenSignInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignInDay   int32 `protobuf:"varint,1,opt,name=signInDay,proto3" json:"signInDay,omitempty"`     // 签到天数
	SignInState int32 `protobuf:"varint,2,opt,name=signInState,proto3" json:"signInState,omitempty"` // 签到状态 [0-未签到; 1-已签到未领取; 2-已签到已领取]
}

func (x *PBSevenSignInfo) Reset() {
	*x = PBSevenSignInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBSevenSignInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBSevenSignInfo) ProtoMessage() {}

func (x *PBSevenSignInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBSevenSignInfo.ProtoReflect.Descriptor instead.
func (*PBSevenSignInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{50}
}

func (x *PBSevenSignInfo) GetSignInDay() int32 {
	if x != nil {
		return x.SignInDay
	}
	return 0
}

func (x *PBSevenSignInfo) GetSignInState() int32 {
	if x != nil {
		return x.SignInState
	}
	return 0
}

//======================首冲礼包=========================================
type PBFirstChargeGiftData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Days      int32 `protobuf:"varint,1,opt,name=days,proto3" json:"days,omitempty"`           //天数ID
	GiftState int32 `protobuf:"varint,2,opt,name=giftState,proto3" json:"giftState,omitempty"` //领取状态 0不能领取（未购买或领取时间未到），1 可领取，2已领取
}

func (x *PBFirstChargeGiftData) Reset() {
	*x = PBFirstChargeGiftData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBFirstChargeGiftData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBFirstChargeGiftData) ProtoMessage() {}

func (x *PBFirstChargeGiftData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBFirstChargeGiftData.ProtoReflect.Descriptor instead.
func (*PBFirstChargeGiftData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{51}
}

func (x *PBFirstChargeGiftData) GetDays() int32 {
	if x != nil {
		return x.Days
	}
	return 0
}

func (x *PBFirstChargeGiftData) GetGiftState() int32 {
	if x != nil {
		return x.GiftState
	}
	return 0
}

type PBFirstChargeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId    int32                    `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"`
	IsBuy     bool                     `protobuf:"varint,2,opt,name=isBuy,proto3" json:"isBuy,omitempty"`
	GiftInfos []*PBFirstChargeGiftData `protobuf:"bytes,3,rep,name=giftInfos,proto3" json:"giftInfos,omitempty"`
}

func (x *PBFirstChargeData) Reset() {
	*x = PBFirstChargeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBFirstChargeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBFirstChargeData) ProtoMessage() {}

func (x *PBFirstChargeData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBFirstChargeData.ProtoReflect.Descriptor instead.
func (*PBFirstChargeData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{52}
}

func (x *PBFirstChargeData) GetGiftId() int32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *PBFirstChargeData) GetIsBuy() bool {
	if x != nil {
		return x.IsBuy
	}
	return false
}

func (x *PBFirstChargeData) GetGiftInfos() []*PBFirstChargeGiftData {
	if x != nil {
		return x.GiftInfos
	}
	return nil
}

//======================充值返利开始==========================================
// 充值返利信息
type PBTopupRebateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId    int32 `protobuf:"varint,1,opt,name=taskId,proto3" json:"taskId,omitempty"`       // 任务Id
	TaskState int32 `protobuf:"varint,2,opt,name=taskState,proto3" json:"taskState,omitempty"` // 任务状态[-1-未解锁;0-未领取; 1-已领取]
}

func (x *PBTopupRebateInfo) Reset() {
	*x = PBTopupRebateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBTopupRebateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBTopupRebateInfo) ProtoMessage() {}

func (x *PBTopupRebateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBTopupRebateInfo.ProtoReflect.Descriptor instead.
func (*PBTopupRebateInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{53}
}

func (x *PBTopupRebateInfo) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *PBTopupRebateInfo) GetTaskState() int32 {
	if x != nil {
		return x.TaskState
	}
	return 0
}

//======================月卡开始==========================================
type PBMonthlyCardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpirationTime int32 `protobuf:"varint,1,opt,name=expirationTime,proto3" json:"expirationTime,omitempty"` // 过期时间
}

func (x *PBMonthlyCardInfo) Reset() {
	*x = PBMonthlyCardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBMonthlyCardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBMonthlyCardInfo) ProtoMessage() {}

func (x *PBMonthlyCardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBMonthlyCardInfo.ProtoReflect.Descriptor instead.
func (*PBMonthlyCardInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{54}
}

func (x *PBMonthlyCardInfo) GetExpirationTime() int32 {
	if x != nil {
		return x.ExpirationTime
	}
	return 0
}

//======================月卡2.0开始==========================================
type PBMonthlyCardNewInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonthlyCardId  int32 `protobuf:"varint,1,opt,name=monthlyCardId,proto3" json:"monthlyCardId,omitempty"`   // 月卡2.0类型[0-超值月卡; 1-至尊月卡]
	ExpirationTime int32 `protobuf:"varint,2,opt,name=expirationTime,proto3" json:"expirationTime,omitempty"` // 过期时间
}

func (x *PBMonthlyCardNewInfo) Reset() {
	*x = PBMonthlyCardNewInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBMonthlyCardNewInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBMonthlyCardNewInfo) ProtoMessage() {}

func (x *PBMonthlyCardNewInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBMonthlyCardNewInfo.ProtoReflect.Descriptor instead.
func (*PBMonthlyCardNewInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{55}
}

func (x *PBMonthlyCardNewInfo) GetMonthlyCardId() int32 {
	if x != nil {
		return x.MonthlyCardId
	}
	return 0
}

func (x *PBMonthlyCardNewInfo) GetExpirationTime() int32 {
	if x != nil {
		return x.ExpirationTime
	}
	return 0
}

//======================等级基金开始==========================================
type PBGradedFunds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LevelStageIdx  int32 `protobuf:"varint,1,opt,name=levelStageIdx,proto3" json:"levelStageIdx,omitempty"`   // 等级阶段索引
	ComWealState   int32 `protobuf:"varint,2,opt,name=comWealState,proto3" json:"comWealState,omitempty"`     // 普通福利状态[0-未领取; 1-已领取]
	SuperWealState int32 `protobuf:"varint,3,opt,name=superWealState,proto3" json:"superWealState,omitempty"` // 超级福利状态[-1-未解锁;0-未领取; 1-已领取]
}

func (x *PBGradedFunds) Reset() {
	*x = PBGradedFunds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGradedFunds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGradedFunds) ProtoMessage() {}

func (x *PBGradedFunds) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGradedFunds.ProtoReflect.Descriptor instead.
func (*PBGradedFunds) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{56}
}

func (x *PBGradedFunds) GetLevelStageIdx() int32 {
	if x != nil {
		return x.LevelStageIdx
	}
	return 0
}

func (x *PBGradedFunds) GetComWealState() int32 {
	if x != nil {
		return x.ComWealState
	}
	return 0
}

func (x *PBGradedFunds) GetSuperWealState() int32 {
	if x != nil {
		return x.SuperWealState
	}
	return 0
}

type PBGradedFundInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GradedFundIdx   int32            `protobuf:"varint,1,opt,name=gradedFundIdx,proto3" json:"gradedFundIdx,omitempty"`     // 基金阶段索引
	GradedFundState int32            `protobuf:"varint,2,opt,name=gradedFundState,proto3" json:"gradedFundState,omitempty"` // 基金阶段状态[0-未购买; 1-已购买]
	GradedFundInfo  []*PBGradedFunds `protobuf:"bytes,3,rep,name=gradedFundInfo,proto3" json:"gradedFundInfo,omitempty"`    // 基金阶段信息
}

func (x *PBGradedFundInfo) Reset() {
	*x = PBGradedFundInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBGradedFundInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBGradedFundInfo) ProtoMessage() {}

func (x *PBGradedFundInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBGradedFundInfo.ProtoReflect.Descriptor instead.
func (*PBGradedFundInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{57}
}

func (x *PBGradedFundInfo) GetGradedFundIdx() int32 {
	if x != nil {
		return x.GradedFundIdx
	}
	return 0
}

func (x *PBGradedFundInfo) GetGradedFundState() int32 {
	if x != nil {
		return x.GradedFundState
	}
	return 0
}

func (x *PBGradedFundInfo) GetGradedFundInfo() []*PBGradedFunds {
	if x != nil {
		return x.GradedFundInfo
	}
	return nil
}

//======================任务开始==========================================
type PBMissionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MissionId     int32        `protobuf:"varint,1,opt,name=missionId,proto3" json:"missionId,omitempty"`                         // 任务Id
	MissionState  MissionState `protobuf:"varint,2,opt,name=missionState,proto3,enum=MissionState" json:"missionState,omitempty"` // 任务状态
	MissionParams []int32      `protobuf:"varint,3,rep,packed,name=missionParams,proto3" json:"missionParams,omitempty"`          // 任务参数列表，（第一个默认是进度）
}

func (x *PBMissionInfo) Reset() {
	*x = PBMissionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBMissionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBMissionInfo) ProtoMessage() {}

func (x *PBMissionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBMissionInfo.ProtoReflect.Descriptor instead.
func (*PBMissionInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{58}
}

func (x *PBMissionInfo) GetMissionId() int32 {
	if x != nil {
		return x.MissionId
	}
	return 0
}

func (x *PBMissionInfo) GetMissionState() MissionState {
	if x != nil {
		return x.MissionState
	}
	return MissionState_MissionState_Unfinished
}

func (x *PBMissionInfo) GetMissionParams() []int32 {
	if x != nil {
		return x.MissionParams
	}
	return nil
}

//======================商城礼包=========================================
type PBShopGiftData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId    int32 `protobuf:"varint,1,opt,name=giftId,proto3" json:"giftId,omitempty"`       //礼包ID
	GiftState bool  `protobuf:"varint,2,opt,name=giftState,proto3" json:"giftState,omitempty"` //购买状态（可购买/不可购买）
	BuyCount  int32 `protobuf:"varint,3,opt,name=buyCount,proto3" json:"buyCount,omitempty"`   //已购买次数
	GiftType  int32 `protobuf:"varint,4,opt,name=giftType,proto3" json:"giftType,omitempty"`   //礼包类型
}

func (x *PBShopGiftData) Reset() {
	*x = PBShopGiftData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBShopGiftData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBShopGiftData) ProtoMessage() {}

func (x *PBShopGiftData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBShopGiftData.ProtoReflect.Descriptor instead.
func (*PBShopGiftData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{59}
}

func (x *PBShopGiftData) GetGiftId() int32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *PBShopGiftData) GetGiftState() bool {
	if x != nil {
		return x.GiftState
	}
	return false
}

func (x *PBShopGiftData) GetBuyCount() int32 {
	if x != nil {
		return x.BuyCount
	}
	return 0
}

func (x *PBShopGiftData) GetGiftType() int32 {
	if x != nil {
		return x.GiftType
	}
	return 0
}

//======================限时商城礼包=========================================
type PBTimeGiftData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeGiftId   int32 `protobuf:"varint,1,opt,name=timeGiftId,proto3" json:"timeGiftId,omitempty"`     //TimeGiftPacks礼包ID
	GiftState    bool  `protobuf:"varint,2,opt,name=giftState,proto3" json:"giftState,omitempty"`       //购买状态（可购买/不可购买）
	BuyCount     int32 `protobuf:"varint,3,opt,name=buyCount,proto3" json:"buyCount,omitempty"`         //已购买次数
	LifeBuyCount int32 `protobuf:"varint,4,opt,name=lifeBuyCount,proto3" json:"lifeBuyCount,omitempty"` //累计购买次数
}

func (x *PBTimeGiftData) Reset() {
	*x = PBTimeGiftData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBTimeGiftData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBTimeGiftData) ProtoMessage() {}

func (x *PBTimeGiftData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBTimeGiftData.ProtoReflect.Descriptor instead.
func (*PBTimeGiftData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{60}
}

func (x *PBTimeGiftData) GetTimeGiftId() int32 {
	if x != nil {
		return x.TimeGiftId
	}
	return 0
}

func (x *PBTimeGiftData) GetGiftState() bool {
	if x != nil {
		return x.GiftState
	}
	return false
}

func (x *PBTimeGiftData) GetBuyCount() int32 {
	if x != nil {
		return x.BuyCount
	}
	return 0
}

func (x *PBTimeGiftData) GetLifeBuyCount() int32 {
	if x != nil {
		return x.LifeBuyCount
	}
	return 0
}

//======================奖励通用展示开始==========================================
type PBCommonAwardDisplay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId  int32 `protobuf:"varint,1,opt,name=itemId,proto3" json:"itemId,omitempty"`   // 道具Id
	ItemNum int32 `protobuf:"varint,2,opt,name=itemNum,proto3" json:"itemNum,omitempty"` // 道具Num
}

func (x *PBCommonAwardDisplay) Reset() {
	*x = PBCommonAwardDisplay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBCommonAwardDisplay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBCommonAwardDisplay) ProtoMessage() {}

func (x *PBCommonAwardDisplay) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBCommonAwardDisplay.ProtoReflect.Descriptor instead.
func (*PBCommonAwardDisplay) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{61}
}

func (x *PBCommonAwardDisplay) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *PBCommonAwardDisplay) GetItemNum() int32 {
	if x != nil {
		return x.ItemNum
	}
	return 0
}

//==========================测试===================
type PBTest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *PBTest) Reset() {
	*x = PBTest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBTest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBTest) ProtoMessage() {}

func (x *PBTest) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBTest.ProtoReflect.Descriptor instead.
func (*PBTest) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{62}
}

func (x *PBTest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBTest) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

//======================支付=========================================
// 订单信息
type PBOrderData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderKey        string `protobuf:"bytes,1,opt,name=orderKey,proto3" json:"orderKey,omitempty"`               // 订单 ID，主键
	GoodsRegisterId string `protobuf:"bytes,2,opt,name=goodsRegisterId,proto3" json:"goodsRegisterId,omitempty"` // 商品注册ID
	ModuleType      int32  `protobuf:"varint,3,opt,name=moduleType,proto3" json:"moduleType,omitempty"`          // 支付对应模块
	GoodsPrice      int32  `protobuf:"varint,4,opt,name=goodsPrice,proto3" json:"goodsPrice,omitempty"`          // 产品实际支付价格（RMB级别为元，此参数不带引号，请用数字类型处理）
	ChannelId       string `protobuf:"bytes,5,opt,name=channelId,proto3" json:"channelId,omitempty"`             // 渠道标识
	GameGoodsId     string `protobuf:"bytes,6,opt,name=gameGoodsId,proto3" json:"gameGoodsId,omitempty"`         // 游戏商品ID
	PlayerId        uint64 `protobuf:"varint,7,opt,name=playerId,proto3" json:"playerId,omitempty"`              // 玩家 ID
}

func (x *PBOrderData) Reset() {
	*x = PBOrderData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBOrderData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBOrderData) ProtoMessage() {}

func (x *PBOrderData) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBOrderData.ProtoReflect.Descriptor instead.
func (*PBOrderData) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{63}
}

func (x *PBOrderData) GetOrderKey() string {
	if x != nil {
		return x.OrderKey
	}
	return ""
}

func (x *PBOrderData) GetGoodsRegisterId() string {
	if x != nil {
		return x.GoodsRegisterId
	}
	return ""
}

func (x *PBOrderData) GetModuleType() int32 {
	if x != nil {
		return x.ModuleType
	}
	return 0
}

func (x *PBOrderData) GetGoodsPrice() int32 {
	if x != nil {
		return x.GoodsPrice
	}
	return 0
}

func (x *PBOrderData) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *PBOrderData) GetGameGoodsId() string {
	if x != nil {
		return x.GameGoodsId
	}
	return ""
}

func (x *PBOrderData) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 商品信息
type PBPayGoodsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId    uint64 `protobuf:"varint,1,opt,name=playerId,proto3" json:"playerId,omitempty"`      // 玩家 ID
	ModuleType  int32  `protobuf:"varint,2,opt,name=moduleType,proto3" json:"moduleType,omitempty"`  // 支付对应模块类型
	GameGoodsId string `protobuf:"bytes,3,opt,name=gameGoodsId,proto3" json:"gameGoodsId,omitempty"` // 游戏商品ID
	GoodsPrice  int32  `protobuf:"varint,4,opt,name=goodsPrice,proto3" json:"goodsPrice,omitempty"`  // 产品实际支付价格
	Time        int64  `protobuf:"varint,5,opt,name=time,proto3" json:"time,omitempty"`              // 时间
	OrderId     string `protobuf:"bytes,6,opt,name=orderId,proto3" json:"orderId,omitempty"`         // 订单编号
	ChannelId   string `protobuf:"bytes,7,opt,name=channelId,proto3" json:"channelId,omitempty"`     // 渠道id
	UserId      string `protobuf:"bytes,8,opt,name=userId,proto3" json:"userId,omitempty"`           // 用户标识
	State       int32  `protobuf:"varint,9,opt,name=state,proto3" json:"state,omitempty"`            // 订单状态
}

func (x *PBPayGoodsInfo) Reset() {
	*x = PBPayGoodsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPayGoodsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPayGoodsInfo) ProtoMessage() {}

func (x *PBPayGoodsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPayGoodsInfo.ProtoReflect.Descriptor instead.
func (*PBPayGoodsInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{64}
}

func (x *PBPayGoodsInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PBPayGoodsInfo) GetModuleType() int32 {
	if x != nil {
		return x.ModuleType
	}
	return 0
}

func (x *PBPayGoodsInfo) GetGameGoodsId() string {
	if x != nil {
		return x.GameGoodsId
	}
	return ""
}

func (x *PBPayGoodsInfo) GetGoodsPrice() int32 {
	if x != nil {
		return x.GoodsPrice
	}
	return 0
}

func (x *PBPayGoodsInfo) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *PBPayGoodsInfo) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PBPayGoodsInfo) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *PBPayGoodsInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PBPayGoodsInfo) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

type UserSnapUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid            uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                                               //玩家 ID
	HeadIcon       int32  `protobuf:"varint,2,opt,name=head_icon,json=headIcon,proto3" json:"head_icon,omitempty"`                     //头像
	IsOnline       bool   `protobuf:"varint,3,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`                     //是否在线
	Name           string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                              //名字
	Level          int32  `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`                                           //等级
	LastLoginTime  int64  `protobuf:"varint,6,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`    //最后登录时间
	LastLogoutTime int32  `protobuf:"varint,7,opt,name=last_logout_time,json=lastLogoutTime,proto3" json:"last_logout_time,omitempty"` //最后登出时间
}

func (x *UserSnapUserInfo) Reset() {
	*x = UserSnapUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSnapUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSnapUserInfo) ProtoMessage() {}

func (x *UserSnapUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSnapUserInfo.ProtoReflect.Descriptor instead.
func (*UserSnapUserInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{65}
}

func (x *UserSnapUserInfo) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *UserSnapUserInfo) GetHeadIcon() int32 {
	if x != nil {
		return x.HeadIcon
	}
	return 0
}

func (x *UserSnapUserInfo) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *UserSnapUserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserSnapUserInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *UserSnapUserInfo) GetLastLoginTime() int64 {
	if x != nil {
		return x.LastLoginTime
	}
	return 0
}

func (x *UserSnapUserInfo) GetLastLogoutTime() int32 {
	if x != nil {
		return x.LastLogoutTime
	}
	return 0
}

type UserSnapUsers struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*UserSnapUserInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *UserSnapUsers) Reset() {
	*x = UserSnapUsers{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSnapUsers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSnapUsers) ProtoMessage() {}

func (x *UserSnapUsers) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSnapUsers.ProtoReflect.Descriptor instead.
func (*UserSnapUsers) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{66}
}

func (x *UserSnapUsers) GetUsers() []*UserSnapUserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

//================================功能预告====================================
type PBFunctionPreview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuncId      int32            `protobuf:"varint,1,opt,name=FuncId,proto3" json:"FuncId,omitempty"`                                 // 功能 ID
	RewardState FuncPreviewstate `protobuf:"varint,2,opt,name=RewardState,proto3,enum=FuncPreviewstate" json:"RewardState,omitempty"` // 奖励状态 未解锁 未领取 已领取
}

func (x *PBFunctionPreview) Reset() {
	*x = PBFunctionPreview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBFunctionPreview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBFunctionPreview) ProtoMessage() {}

func (x *PBFunctionPreview) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBFunctionPreview.ProtoReflect.Descriptor instead.
func (*PBFunctionPreview) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{67}
}

func (x *PBFunctionPreview) GetFuncId() int32 {
	if x != nil {
		return x.FuncId
	}
	return 0
}

func (x *PBFunctionPreview) GetRewardState() FuncPreviewstate {
	if x != nil {
		return x.RewardState
	}
	return FuncPreviewstate_Lock
}

//===============================功能预告======================================
//================================好友邀请====================================
type PBInviteTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                           // 任务id
	Cnt    int32        `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`                         // 当前进度
	Status RewardStatus `protobuf:"varint,3,opt,name=status,proto3,enum=RewardStatus" json:"status,omitempty"` // 领奖状态
}

func (x *PBInviteTask) Reset() {
	*x = PBInviteTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBInviteTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBInviteTask) ProtoMessage() {}

func (x *PBInviteTask) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBInviteTask.ProtoReflect.Descriptor instead.
func (*PBInviteTask) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{68}
}

func (x *PBInviteTask) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBInviteTask) GetCnt() int32 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

func (x *PBInviteTask) GetStatus() RewardStatus {
	if x != nil {
		return x.Status
	}
	return RewardStatus_RewardStatus_Doing
}

//===============================好友邀请======================================
//====================竞技场开始=======================
type PBArenaRivalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                          //名字
	Level     uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                       //等级
	Score     uint32 `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`                       //积分
	HeadIcon  int32  `protobuf:"varint,4,opt,name=headIcon,proto3" json:"headIcon,omitempty"`                 // 头像
	HeadFrame int32  `protobuf:"varint,5,opt,name=headFrame,proto3" json:"headFrame,omitempty"`               // 头像框
	PlayerId  uint64 `protobuf:"varint,6,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家 ID
}

func (x *PBArenaRivalInfo) Reset() {
	*x = PBArenaRivalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBArenaRivalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBArenaRivalInfo) ProtoMessage() {}

func (x *PBArenaRivalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBArenaRivalInfo.ProtoReflect.Descriptor instead.
func (*PBArenaRivalInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{69}
}

func (x *PBArenaRivalInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PBArenaRivalInfo) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBArenaRivalInfo) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *PBArenaRivalInfo) GetHeadIcon() int32 {
	if x != nil {
		return x.HeadIcon
	}
	return 0
}

func (x *PBArenaRivalInfo) GetHeadFrame() int32 {
	if x != nil {
		return x.HeadFrame
	}
	return 0
}

func (x *PBArenaRivalInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

//===============================体力开始======================================
// 一个可领取的体力奖励包
type PBPowerRewardDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime  int64 `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime,omitempty"`   // 体力奖励包生成的时间（Unix 时间戳，单位：秒）
	PowerCount int32 `protobuf:"varint,2,opt,name=powerCount,proto3" json:"powerCount,omitempty"` // 该奖励包包含的体力数量
}

func (x *PBPowerRewardDataInfo) Reset() {
	*x = PBPowerRewardDataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPowerRewardDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPowerRewardDataInfo) ProtoMessage() {}

func (x *PBPowerRewardDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPowerRewardDataInfo.ProtoReflect.Descriptor instead.
func (*PBPowerRewardDataInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{70}
}

func (x *PBPowerRewardDataInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *PBPowerRewardDataInfo) GetPowerCount() int32 {
	if x != nil {
		return x.PowerCount
	}
	return 0
}

//===============================体力结束======================================
//=================== 赛季buff开始 ========================
//赛季buff信息
type PBSeasonBuff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BuffId  int32 `protobuf:"varint,1,opt,name=buffId,proto3" json:"buffId,omitempty"`   //buffId
	EndTime int64 `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"` //结束时间
}

func (x *PBSeasonBuff) Reset() {
	*x = PBSeasonBuff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBSeasonBuff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBSeasonBuff) ProtoMessage() {}

func (x *PBSeasonBuff) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBSeasonBuff.ProtoReflect.Descriptor instead.
func (*PBSeasonBuff) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{71}
}

func (x *PBSeasonBuff) GetBuffId() int32 {
	if x != nil {
		return x.BuffId
	}
	return 0
}

func (x *PBSeasonBuff) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

//=================== 战斗开始 ========================
//玩家战斗信息
type PBBattleHeroInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 // 英雄资源ID
	Level      int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`           // 英雄等级(战斗外)
	StarLevel  int32 `protobuf:"varint,3,opt,name=starLevel,proto3" json:"starLevel,omitempty"`   // 星级(战斗内)
	AwakeLevel int32 `protobuf:"varint,4,opt,name=awakeLevel,proto3" json:"awakeLevel,omitempty"` // 英雄觉醒等级(战斗外)
}

func (x *PBBattleHeroInfo) Reset() {
	*x = PBBattleHeroInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBattleHeroInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBattleHeroInfo) ProtoMessage() {}

func (x *PBBattleHeroInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBattleHeroInfo.ProtoReflect.Descriptor instead.
func (*PBBattleHeroInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{72}
}

func (x *PBBattleHeroInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBBattleHeroInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBBattleHeroInfo) GetStarLevel() int32 {
	if x != nil {
		return x.StarLevel
	}
	return 0
}

func (x *PBBattleHeroInfo) GetAwakeLevel() int32 {
	if x != nil {
		return x.AwakeLevel
	}
	return 0
}

//玩家战斗信息
type PBBattleTeamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Heros []*PBBattleHeroInfo `protobuf:"bytes,1,rep,name=heros,proto3" json:"heros,omitempty"`
}

func (x *PBBattleTeamInfo) Reset() {
	*x = PBBattleTeamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBattleTeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBattleTeamInfo) ProtoMessage() {}

func (x *PBBattleTeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBattleTeamInfo.ProtoReflect.Descriptor instead.
func (*PBBattleTeamInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{73}
}

func (x *PBBattleTeamInfo) GetHeros() []*PBBattleHeroInfo {
	if x != nil {
		return x.Heros
	}
	return nil
}

//玩家战斗信息
type PBBattlePlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid          uint64            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                // 玩家ID
	Level        int32             `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`            // 玩家等级
	Name         string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`               // 玩家名字
	Buffers      []int32           `protobuf:"varint,4,rep,packed,name=buffers,proto3" json:"buffers,omitempty"` //buffer
	Kills        int32             `protobuf:"varint,5,opt,name=kills,proto3" json:"kills,omitempty"`            // 击杀数
	WinCount     int32             `protobuf:"varint,6,opt,name=winCount,proto3" json:"winCount,omitempty"`      // 连胜数
	ServerId     string            `protobuf:"bytes,7,opt,name=serverId,proto3" json:"serverId,omitempty"`
	Throphy      int32             `protobuf:"varint,8,opt,name=throphy,proto3" json:"throphy,omitempty"`           // 奖杯
	Hp           int32             `protobuf:"varint,9,opt,name=hp,proto3" json:"hp,omitempty"`                     // 血量
	TreasureList []*PBTreasureInfo `protobuf:"bytes,10,rep,name=treasureList,proto3" json:"treasureList,omitempty"` // 玩家拥有的宝物列表
}

func (x *PBBattlePlayerInfo) Reset() {
	*x = PBBattlePlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBattlePlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBattlePlayerInfo) ProtoMessage() {}

func (x *PBBattlePlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBattlePlayerInfo.ProtoReflect.Descriptor instead.
func (*PBBattlePlayerInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{74}
}

func (x *PBBattlePlayerInfo) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PBBattlePlayerInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBBattlePlayerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PBBattlePlayerInfo) GetBuffers() []int32 {
	if x != nil {
		return x.Buffers
	}
	return nil
}

func (x *PBBattlePlayerInfo) GetKills() int32 {
	if x != nil {
		return x.Kills
	}
	return 0
}

func (x *PBBattlePlayerInfo) GetWinCount() int32 {
	if x != nil {
		return x.WinCount
	}
	return 0
}

func (x *PBBattlePlayerInfo) GetServerId() string {
	if x != nil {
		return x.ServerId
	}
	return ""
}

func (x *PBBattlePlayerInfo) GetThrophy() int32 {
	if x != nil {
		return x.Throphy
	}
	return 0
}

func (x *PBBattlePlayerInfo) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PBBattlePlayerInfo) GetTreasureList() []*PBTreasureInfo {
	if x != nil {
		return x.TreasureList
	}
	return nil
}

//战斗棋盘信息
type PBCheckerBoard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GridID int32             `protobuf:"varint,1,opt,name=gridID,proto3" json:"gridID,omitempty"` // 棋盘格子ID
	Hero   *PBBattleHeroInfo `protobuf:"bytes,2,opt,name=hero,proto3" json:"hero,omitempty"`      // 英雄
}

func (x *PBCheckerBoard) Reset() {
	*x = PBCheckerBoard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBCheckerBoard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBCheckerBoard) ProtoMessage() {}

func (x *PBCheckerBoard) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBCheckerBoard.ProtoReflect.Descriptor instead.
func (*PBCheckerBoard) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{75}
}

func (x *PBCheckerBoard) GetGridID() int32 {
	if x != nil {
		return x.GridID
	}
	return 0
}

func (x *PBCheckerBoard) GetHero() *PBBattleHeroInfo {
	if x != nil {
		return x.Hero
	}
	return nil
}

//玩家战斗状态
type PBBattlePlayerState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`     // 玩家ID
	State int32  `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"` // 玩家状态，0 空闲 1 以准备 2 战斗中
}

func (x *PBBattlePlayerState) Reset() {
	*x = PBBattlePlayerState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBattlePlayerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBattlePlayerState) ProtoMessage() {}

func (x *PBBattlePlayerState) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBattlePlayerState.ProtoReflect.Descriptor instead.
func (*PBBattlePlayerState) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{76}
}

func (x *PBBattlePlayerState) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PBBattlePlayerState) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

//阵容数据
type PBBattleCampInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BoardInfo []*PBCheckerBoard   `protobuf:"bytes,1,rep,name=boardInfo,proto3" json:"boardInfo,omitempty"` //英雄数据
	Player    *PBBattlePlayerInfo `protobuf:"bytes,2,opt,name=player,proto3" json:"player,omitempty"`       //玩家数据
}

func (x *PBBattleCampInfo) Reset() {
	*x = PBBattleCampInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBBattleCampInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBBattleCampInfo) ProtoMessage() {}

func (x *PBBattleCampInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBBattleCampInfo.ProtoReflect.Descriptor instead.
func (*PBBattleCampInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{77}
}

func (x *PBBattleCampInfo) GetBoardInfo() []*PBCheckerBoard {
	if x != nil {
		return x.BoardInfo
	}
	return nil
}

func (x *PBBattleCampInfo) GetPlayer() *PBBattlePlayerInfo {
	if x != nil {
		return x.Player
	}
	return nil
}

//单次移动操作
type PBMoveOperation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromGridId int32 `protobuf:"varint,1,opt,name=fromGridId,proto3" json:"fromGridId,omitempty"` // 移动起始格子ID
	ToGridId   int32 `protobuf:"varint,2,opt,name=toGridId,proto3" json:"toGridId,omitempty"`     // 移动目标格子ID
}

func (x *PBMoveOperation) Reset() {
	*x = PBMoveOperation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBMoveOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBMoveOperation) ProtoMessage() {}

func (x *PBMoveOperation) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBMoveOperation.ProtoReflect.Descriptor instead.
func (*PBMoveOperation) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{78}
}

func (x *PBMoveOperation) GetFromGridId() int32 {
	if x != nil {
		return x.FromGridId
	}
	return 0
}

func (x *PBMoveOperation) GetToGridId() int32 {
	if x != nil {
		return x.ToGridId
	}
	return 0
}

// 单个玩家的棋盘信息
type PBPlayerBoard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid       uint64            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`            // 玩家ID
	BoardInfo []*PBCheckerBoard `protobuf:"bytes,2,rep,name=boardInfo,proto3" json:"boardInfo,omitempty"` // 该玩家的棋盘格子信息
}

func (x *PBPlayerBoard) Reset() {
	*x = PBPlayerBoard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBPlayerBoard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBPlayerBoard) ProtoMessage() {}

func (x *PBPlayerBoard) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBPlayerBoard.ProtoReflect.Descriptor instead.
func (*PBPlayerBoard) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{79}
}

func (x *PBPlayerBoard) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PBPlayerBoard) GetBoardInfo() []*PBCheckerBoard {
	if x != nil {
		return x.BoardInfo
	}
	return nil
}

//=================== 阵容开始 ========================
// 单个阵容的完整信息
type PBLineupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                 // 阵容槽位id
	Name    string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                              // 阵容名称, e.g., "自定义1"
	HeroIds []int32 `protobuf:"varint,3,rep,packed,name=hero_ids,json=heroIds,proto3" json:"hero_ids,omitempty"` // 阵容中的英雄ID列表
}

func (x *PBLineupInfo) Reset() {
	*x = PBLineupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBLineupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBLineupInfo) ProtoMessage() {}

func (x *PBLineupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBLineupInfo.ProtoReflect.Descriptor instead.
func (*PBLineupInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{80}
}

func (x *PBLineupInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PBLineupInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PBLineupInfo) GetHeroIds() []int32 {
	if x != nil {
		return x.HeroIds
	}
	return nil
}

//=================== 赛季开始 ========================
// 段位奖励状态信息
type PBRankRewardState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RankId    int32 `protobuf:"varint,1,opt,name=rank_id,json=rankId,proto3" json:"rank_id,omitempty"`          // 段位ID
	IsClaimed bool  `protobuf:"varint,2,opt,name=is_claimed,json=isClaimed,proto3" json:"is_claimed,omitempty"` // 是否已领取
}

func (x *PBRankRewardState) Reset() {
	*x = PBRankRewardState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBRankRewardState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBRankRewardState) ProtoMessage() {}

func (x *PBRankRewardState) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBRankRewardState.ProtoReflect.Descriptor instead.
func (*PBRankRewardState) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{81}
}

func (x *PBRankRewardState) GetRankId() int32 {
	if x != nil {
		return x.RankId
	}
	return 0
}

func (x *PBRankRewardState) GetIsClaimed() bool {
	if x != nil {
		return x.IsClaimed
	}
	return false
}

// 历史赛季的奖杯信息
type SeasonTrophyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeasonId    int32 `protobuf:"varint,1,opt,name=season_id,json=seasonId,proto3" json:"season_id,omitempty"`
	FinalTrophy int32 `protobuf:"varint,2,opt,name=final_trophy,json=finalTrophy,proto3" json:"final_trophy,omitempty"`
}

func (x *SeasonTrophyInfo) Reset() {
	*x = SeasonTrophyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeasonTrophyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeasonTrophyInfo) ProtoMessage() {}

func (x *SeasonTrophyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeasonTrophyInfo.ProtoReflect.Descriptor instead.
func (*SeasonTrophyInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{82}
}

func (x *SeasonTrophyInfo) GetSeasonId() int32 {
	if x != nil {
		return x.SeasonId
	}
	return 0
}

func (x *SeasonTrophyInfo) GetFinalTrophy() int32 {
	if x != nil {
		return x.FinalTrophy
	}
	return 0
}

//=================== 宝物系统开始 ========================
// 单个宝物的信息
type PBTreasureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TreasureId int32 `protobuf:"varint,1,opt,name=treasureId,proto3" json:"treasureId,omitempty"` // 宝物ID
	Level      int32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`           // 当前等级
	Star       int32 `protobuf:"varint,3,opt,name=star,proto3" json:"star,omitempty"`             // 当前星级
	Count      int32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`           // 当前拥有的该宝物【数量】(用于升星消耗)
}

func (x *PBTreasureInfo) Reset() {
	*x = PBTreasureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_PublicMessage_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBTreasureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBTreasureInfo) ProtoMessage() {}

func (x *PBTreasureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMessage_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBTreasureInfo.ProtoReflect.Descriptor instead.
func (*PBTreasureInfo) Descriptor() ([]byte, []int) {
	return file_PublicMessage_proto_rawDescGZIP(), []int{83}
}

func (x *PBTreasureInfo) GetTreasureId() int32 {
	if x != nil {
		return x.TreasureId
	}
	return 0
}

func (x *PBTreasureInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PBTreasureInfo) GetStar() int32 {
	if x != nil {
		return x.Star
	}
	return 0
}

func (x *PBTreasureInfo) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_PublicMessage_proto protoreflect.FileDescriptor

var file_PublicMessage_proto_rawDesc = []byte{
	0x0a, 0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x09, 0x0a, 0x0c, 0x50, 0x42, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x34, 0x0a, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x6e, 0x61,
	0x69, 0x72, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x6e, 0x61, 0x69, 0x72, 0x65,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x45, 0x78, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x45, 0x78, 0x70, 0x12, 0x30, 0x0a, 0x09, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x44, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x50, 0x42, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x0f,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x43,
	0x68, 0x61, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x43, 0x68, 0x61, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x26, 0x0a, 0x0e,
	0x70, 0x43, 0x68, 0x61, 0x74, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x43, 0x68, 0x61, 0x74, 0x43, 0x6c, 0x65, 0x61, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x53, 0x68,
	0x61, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x53, 0x68, 0x61, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x41, 0x72, 0x65, 0x61, 0x49, 0x44, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x41, 0x72, 0x65, 0x61, 0x49, 0x44, 0x12, 0x3e, 0x0a, 0x13, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x42, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x13, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x50, 0x42, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x77,
	0x53, 0x44, 0x4b, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x53,
	0x44, 0x4b, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x4b, 0x4c, 0x43, 0x44, 0x4b, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x4b, 0x4c, 0x43, 0x44, 0x4b, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x14, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x15,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x50, 0x42, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x14, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0a,
	0x74, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x50, 0x42, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0a, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x71, 0x75, 0x69, 0x70, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0a, 0x65, 0x71, 0x75, 0x69, 0x70, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x68,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x18, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0c, 0x68, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x65, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x0b, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x50, 0x42, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a,
	0x0c, 0x62, 0x6f, 0x72, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x62, 0x6f, 0x72, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x76, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x53, 0x76, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x6f,
	0x70, 0x65, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x45, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22,
	0xd6, 0x01, 0x0a, 0x0c, 0x50, 0x42, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x65, 0x74, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10,
	0x73, 0x70, 0x61, 0x72, 0x65, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x73, 0x70, 0x61, 0x72, 0x65, 0x54, 0x61, 0x6c,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x13, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x54, 0x61, 0x6c, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x50, 0x42, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x13, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x61,
	0x6c, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x2f, 0x0a, 0x0d, 0x50, 0x42, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x53,
	0x68, 0x6f, 0x77, 0x50, 0x75, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x75, 0x73, 0x68, 0x22, 0x74, 0x0a, 0x0a, 0x50, 0x42, 0x48,
	0x65, 0x72, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x68, 0x65, 0x72, 0x6f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x68, 0x65, 0x72, 0x6f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x78, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12,
	0x1e, 0x0a, 0x0a, 0x61, 0x77, 0x61, 0x6b, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x77, 0x61, 0x6b, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22,
	0xa7, 0x01, 0x0a, 0x11, 0x50, 0x42, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x67, 0x6f, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x69, 0x61,
	0x6d, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x64, 0x69, 0x61, 0x6d, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x68, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x67,
	0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x69, 0x6e, 0x22, 0x57, 0x0a, 0x11, 0x50, 0x42, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xdc, 0x05, 0x0a, 0x0c, 0x50, 0x42, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x48, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x65, 0x6c,
	0x65, 0x63, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x54, 0x65, 0x6c, 0x65, 0x63, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x48, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x48, 0x69, 0x67, 0x68, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07, 0x44, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x70, 0x75, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61,
	0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x70, 0x75, 0x48, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x47, 0x4c, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x47, 0x4c, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x47, 0x4c, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x47, 0x4c,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x52,
	0x65, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x52, 0x65, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12,
	0x24, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55,
	0x44, 0x49, 0x44, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x55, 0x44, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x45, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x49, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x64, 0x66, 0x76, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x49, 0x64, 0x66, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x41, 0x43, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x41, 0x43, 0x12, 0x12, 0x0a, 0x04, 0x4f, 0x41, 0x49,
	0x44, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4f, 0x41, 0x49, 0x44, 0x12, 0x12, 0x0a,
	0x04, 0x43, 0x41, 0x49, 0x44, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x41, 0x49,
	0x44, 0x22, 0x7e, 0x0a, 0x0c, 0x50, 0x42, 0x41, 0x53, 0x41, 0x49, 0x61, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x61, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x49, 0x61, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x49, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x49, 0x61, 0x64, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x61, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x49, 0x61, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x22, 0x54, 0x0a, 0x0c, 0x50, 0x42, 0x5f, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x70,
	0x66, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x66, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x70, 0x66, 0x6b, 0x65, 0x79, 0x22, 0x4e, 0x0a, 0x10, 0x50, 0x42, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x52, 0x0a, 0x14, 0x50, 0x42, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x4c, 0x6f, 0x6e, 0x67, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x39, 0x0a, 0x0f, 0x50,
	0x42, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x33, 0x0a, 0x09, 0x50, 0x42, 0x49, 0x6e, 0x74, 0x50,
	0x61, 0x69, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x96, 0x01, 0x0a, 0x12,
	0x50, 0x42, 0x44, 0x72, 0x6f, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x74,
	0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69,
	0x74, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x69, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x41, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x68, 0x69, 0x67, 0x68, 0x45, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x22, 0x80, 0x01, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65,
	0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x75, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x0b, 0x63, 0x68, 0x6f,
	0x6f, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x50, 0x42, 0x49, 0x6e, 0x74, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0b, 0x63, 0x68, 0x6f, 0x6f,
	0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x48, 0x0a, 0x0e, 0x50, 0x42, 0x46, 0x75, 0x6e,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x75, 0x6e,
	0x63, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x66, 0x75, 0x6e, 0x63, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x75, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x75, 0x6e, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x4e, 0x0a, 0x10, 0x50, 0x42, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x46, 0x6c, 0x61,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x46, 0x6c, 0x61,
	0x67, 0x22, 0x48, 0x0a, 0x0c, 0x50, 0x42, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x1a, 0x0a, 0x08, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x53, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x22, 0x9f, 0x01, 0x0a, 0x0f,
	0x50, 0x42, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x34, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x60, 0x0a,
	0x0e, 0x50, 0x42, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x16, 0x0a, 0x06, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x74, 0x65, 0x6d, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x49, 0x74, 0x65, 0x6d,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x77, 0x46, 0x6c, 0x61, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6e, 0x65, 0x77, 0x46, 0x6c, 0x61, 0x67, 0x22,
	0x64, 0x0a, 0x0d, 0x50, 0x42, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x20, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c,
	0x2e, 0x42, 0x61, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x49, 0x74, 0x65, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x4e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x6e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x54, 0x0a, 0x10, 0x50, 0x42, 0x4e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69,
	0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x0f,
	0x50, 0x42, 0x45, 0x71, 0x75, 0x69, 0x70, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0a, 0x2e, 0x45, 0x71, 0x75, 0x69,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4f,
	0x77, 0x6e, 0x50, 0x72, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x4f, 0x77, 0x6e,
	0x50, 0x72, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x31, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x4f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x31, 0x12, 0x1a, 0x0a,
	0x08, 0x44, 0x72, 0x65, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x44, 0x72, 0x65, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x72, 0x65,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x31, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x44, 0x72,
	0x65, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x31, 0x22, 0xa0, 0x03, 0x0a, 0x12, 0x50, 0x42, 0x45, 0x71,
	0x75, 0x69, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x6f, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x6f, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0a, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44,
	0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x44, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x77,
	0x6e, 0x50, 0x72, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x4f, 0x77, 0x6e, 0x50,
	0x72, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x31, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x4f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x31, 0x12, 0x1a, 0x0a, 0x08,
	0x44, 0x72, 0x65, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x44, 0x72, 0x65, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x72, 0x65, 0x65,
	0x73, 0x50, 0x72, 0x6f, 0x31, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x44, 0x72, 0x65,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x31, 0x12, 0x1c, 0x0a, 0x09, 0x4f, 0x6c, 0x64, 0x4f, 0x77, 0x6e,
	0x50, 0x72, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x4f, 0x6c, 0x64, 0x4f, 0x77,
	0x6e, 0x50, 0x72, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x4f, 0x6c, 0x64, 0x4f, 0x77, 0x6e, 0x50, 0x72,
	0x6f, 0x31, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x4f, 0x6c, 0x64, 0x4f, 0x77, 0x6e,
	0x50, 0x72, 0x6f, 0x31, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x6c, 0x64, 0x44, 0x72, 0x65, 0x65, 0x73,
	0x50, 0x72, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x4f, 0x6c, 0x64, 0x44, 0x72,
	0x65, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x4f, 0x6c, 0x64, 0x44, 0x72, 0x65,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x31, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x4f, 0x6c,
	0x64, 0x44, 0x72, 0x65, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x31, 0x22, 0x91, 0x03, 0x0a, 0x06, 0x50,
	0x42, 0x4d, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x09, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4b, 0x65,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x26, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e,
	0x4d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x65,
	0x72, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65,
	0x63, 0x65, 0x72, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x63, 0x65,
	0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x63, 0x65, 0x72, 0x22, 0x57,
	0x0a, 0x17, 0x50, 0x42, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x52, 0x61, 0x73, 0x69, 0x6e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd0, 0x01, 0x0a, 0x18, 0x50, 0x42, 0x46, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x55, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x55, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x6c, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x22, 0x5d, 0x0a, 0x1d, 0x50, 0x42,
	0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7d, 0x0a, 0x12, 0x50, 0x42, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x42, 0x6f, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x15, 0x0a, 0x06, 0x62, 0x6f, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x62, 0x6f, 0x78, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x6f, 0x78, 0x5f, 0x65, 0x78,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x62, 0x6f, 0x78, 0x45, 0x78, 0x70, 0x12,
	0x37, 0x0a, 0x0e, 0x62, 0x6f, 0x78, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x62, 0x6f, 0x78, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc4, 0x02, 0x0a, 0x0e, 0x50, 0x42, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x0c, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0d, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37,
	0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x63, 0x75, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x72,
	0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x78,
	0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0xb0, 0x04, 0x0a, 0x10, 0x50, 0x42, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x30, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x30, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x5f, 0x31, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x31, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x53, 0x6b,
	0x69, 0x6e, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x72, 0x61, 0x67,
	0x6f, 0x6e, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x65, 0x61, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x77, 0x65, 0x61, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61,
	0x6e, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62,
	0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x69,
	0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x53, 0x74, 0x61, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72,
	0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x65, 0x61,
	0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x49, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x49, 0x64, 0x22, 0x8b, 0x05, 0x0a, 0x11, 0x50, 0x42, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4f,
	0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e,
	0x53, 0x6b, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x72,
	0x61, 0x67, 0x6f, 0x6e, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x65, 0x61, 0x70, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x77, 0x65, 0x61, 0x70, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65,
	0x78, 0x70, 0x12, 0x28, 0x0a, 0x0f, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x53, 0x6c, 0x6f, 0x74,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x64, 0x72, 0x61,
	0x67, 0x6f, 0x6e, 0x53, 0x6c, 0x6f, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e,
	0x74, 0x61, 0x6b, 0x65, 0x4f, 0x6e, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x61, 0x6b, 0x65, 0x4f, 0x6e, 0x44, 0x72, 0x61, 0x67,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x75, 0x69, 0x6c,
	0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x6c,
	0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x75, 0x69,
	0x6c, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x68, 0x6f, 0x77,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53,
	0x74, 0x61, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x53, 0x74, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49,
	0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x64,
	0x22, 0x9a, 0x01, 0x0a, 0x0f, 0x50, 0x42, 0x51, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64,
	0x12, 0x25, 0x0a, 0x04, 0x67, 0x6f, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x50, 0x42, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x04, 0x67, 0x6f, 0x6f, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x69, 0x63, 0x6b,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb4, 0x03,
	0x0a, 0x0a, 0x50, 0x42, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x6d, 0x73, 0x67, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x61, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x65, 0x61, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x6d, 0x73,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x43, 0x68,
	0x61, 0x74, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6d, 0x73, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x67, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x67, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x75,
	0x69, 0x6c, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67,
	0x75, 0x69, 0x6c, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68,
	0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x49, 0x64, 0x22, 0xea, 0x01, 0x0a, 0x11, 0x50, 0x42, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x63, 0x68, 0x61, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27,
	0x0a, 0x08, 0x63, 0x68, 0x61, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x50, 0x42, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63,
	0x68, 0x61, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0xf7, 0x03, 0x0a, 0x0d, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x53, 0x6b, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x53,
	0x6b, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x77, 0x65, 0x61, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x77, 0x65, 0x61, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x04, 0x67, 0x70, 0x6f,
	0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x67, 0x70, 0x6f, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61,
	0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x22, 0xfe, 0x02, 0x0a, 0x10,
	0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x63,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x63, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x70, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4e,
	0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70,
	0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x65, 0x65,
	0x4a, 0x6f, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x72, 0x65, 0x65,
	0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x22, 0x92, 0x03, 0x0a,
	0x0c, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x12, 0x1e, 0x0a,
	0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x12, 0x1a, 0x0a,
	0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61,
	0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61,
	0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x64,
	0x72, 0x61, 0x67, 0x6f, 0x6e, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x64, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x53, 0x6b, 0x69, 0x6e, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x65, 0x61,
	0x70, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x77, 0x65, 0x61,
	0x70, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f,
	0x6d, 0x62, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x43,
	0x6f, 0x6d, 0x62, 0x61, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49,
	0x64, 0x22, 0xe4, 0x04, 0x0a, 0x11, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x68, 0x6f,
	0x77, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x2e, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x69, 0x63,
	0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72,
	0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x69, 0x64,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x55, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6d, 0x62,
	0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d,
	0x62, 0x61, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x66, 0x72, 0x65, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x72, 0x65, 0x71, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78,
	0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x17, 0x0a, 0x07,
	0x6d, 0x61, 0x78, 0x5f, 0x65, 0x78, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d,
	0x61, 0x78, 0x45, 0x78, 0x70, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x5f, 0x6a,
	0x6f, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x65, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x6d, 0x61, 0x78,
	0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x11, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x61, 0x78, 0x4a, 0x6f, 0x69, 0x6e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xfd, 0x01, 0x0a, 0x0f, 0x50, 0x42, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x70, 0x72, 0x65, 0x73, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x70, 0x72, 0x65, 0x73, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x69, 0x63, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x62,
	0x61, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x66, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x66, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0x4b, 0x0a, 0x0b, 0x50, 0x42, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x54, 0x65, 0x63, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x4f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x4f,
	0x77, 0x6e, 0x50, 0x72, 0x6f, 0x22, 0x8c, 0x01, 0x0a, 0x12, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x54, 0x65, 0x63, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x4f, 0x77, 0x6e, 0x50, 0x72, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x6c,
	0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x6c,
	0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x4f, 0x6c, 0x64, 0x4f, 0x77, 0x6e,
	0x50, 0x72, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x4f, 0x6c, 0x64, 0x4f, 0x77,
	0x6e, 0x50, 0x72, 0x6f, 0x22, 0xa3, 0x01, 0x0a, 0x0a, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x4c, 0x6f, 0x67, 0x12, 0x21, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0d, 0x2e, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x31, 0x22, 0xd8, 0x03, 0x0a, 0x10, 0x50,
	0x42, 0x42, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x47, 0x69, 0x66, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x62, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x14, 0x62, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x62, 0x61, 0x72,
	0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x62, 0x61, 0x72, 0x67, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x34, 0x0a, 0x15, 0x62, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x62, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2d, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x72, 0x67, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x73, 0x12, 0x45, 0x0a, 0x13, 0x6e, 0x6f, 0x42, 0x61, 0x72, 0x67, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x13, 0x6e, 0x6f, 0x42, 0x61, 0x72, 0x67, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x6e,
	0x65, 0x78, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x6e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x75, 0x79, 0x47, 0x69, 0x66, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x62, 0x75, 0x79, 0x47, 0x69, 0x66, 0x74, 0x12, 0x26, 0x0a,
	0x0e, 0x62, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x47, 0x69, 0x66, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x62, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x47, 0x69, 0x66, 0x74, 0x22, 0xb4, 0x01, 0x0a, 0x12, 0x50, 0x42, 0x42, 0x61, 0x72, 0x67,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x62, 0x61, 0x72, 0x67,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x62, 0x61, 0x72, 0x67, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb7, 0x02, 0x0a,
	0x15, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x42, 0x6f, 0x73, 0x73, 0x52, 0x61, 0x6e, 0x6b,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f, 0x6d,
	0x62, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6f, 0x77, 0x43, 0x6f,
	0x6d, 0x62, 0x61, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x61, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d,
	0x65, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x22, 0x7e, 0x0a, 0x10, 0x50, 0x42, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x53, 0x68, 0x6f, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72,
	0x65, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66,
	0x72, 0x65, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x72, 0x65, 0x65,
	0x41, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66,
	0x72, 0x65, 0x65, 0x41, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75,
	0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x75,
	0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc0, 0x01, 0x0a, 0x0e, 0x50, 0x42, 0x47, 0x69, 0x66,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x55, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x22, 0x4c, 0x0a, 0x12, 0x50, 0x42, 0x48,
	0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x75, 0x0a, 0x0f, 0x50, 0x42, 0x48, 0x65, 0x61,
	0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65,
	0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x51,
	0x0a, 0x0f, 0x50, 0x42, 0x53, 0x65, 0x76, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x44, 0x61, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x44, 0x61, 0x79, 0x12,
	0x20, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x22, 0x49, 0x0a, 0x15, 0x50, 0x42, 0x46, 0x69, 0x72, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x47, 0x69, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x77, 0x0a, 0x11,
	0x50, 0x42, 0x46, 0x69, 0x72, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x42,
	0x75, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x42, 0x75, 0x79, 0x12,
	0x34, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x50, 0x42, 0x46, 0x69, 0x72, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x47, 0x69, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x49, 0x0a, 0x11, 0x50, 0x42, 0x54, 0x6f, 0x70, 0x75, 0x70,
	0x52, 0x65, 0x62, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x22, 0x3b, 0x0a, 0x11, 0x50, 0x42, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x64, 0x0a,
	0x14, 0x50, 0x42, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x65,
	0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x0d, 0x50, 0x42, 0x47, 0x72, 0x61, 0x64, 0x65, 0x64,
	0x46, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x78, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x6f, 0x6d, 0x57, 0x65, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x57, 0x65, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x73, 0x75, 0x70, 0x65, 0x72, 0x57, 0x65, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x75, 0x70, 0x65, 0x72, 0x57, 0x65,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x9a, 0x01, 0x0a, 0x10, 0x50, 0x42, 0x47, 0x72,
	0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x78, 0x12, 0x28, 0x0a, 0x0f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x0e,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x50, 0x42, 0x47, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46,
	0x75, 0x6e, 0x64, 0x73, 0x52, 0x0e, 0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x46, 0x75, 0x6e, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x86, 0x01, 0x0a, 0x0d, 0x50, 0x42, 0x4d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x4d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x7e, 0x0a,
	0x0e, 0x50, 0x42, 0x53, 0x68, 0x6f, 0x70, 0x47, 0x69, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x16, 0x0a, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8e, 0x01,
	0x0a, 0x0e, 0x50, 0x42, 0x54, 0x69, 0x6d, 0x65, 0x47, 0x69, 0x66, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x47, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x47, 0x69, 0x66, 0x74, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x62, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x62, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x69,
	0x66, 0x65, 0x42, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x6c, 0x69, 0x66, 0x65, 0x42, 0x75, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x48,
	0x0a, 0x14, 0x50, 0x42, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x44,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x75, 0x6d, 0x22, 0x2a, 0x0a, 0x06, 0x50, 0x42, 0x54, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x22, 0xef, 0x01, 0x0a, 0x0b, 0x50, 0x42, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4b, 0x65, 0x79,
	0x12, 0x28, 0x0a, 0x0f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x61, 0x6d, 0x65,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67,
	0x61, 0x6d, 0x65, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x88, 0x02, 0x0a, 0x0e, 0x50, 0x42, 0x50, 0x61, 0x79,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x61, 0x6d, 0x65,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x22, 0xda, 0x01, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x64,
	0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68, 0x65, 0x61,
	0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67,
	0x6f, 0x75, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x38,
	0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x27, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x6e, 0x61, 0x70, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x60, 0x0a, 0x11, 0x50, 0x42, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x16, 0x0a,
	0x06, 0x46, 0x75, 0x6e, 0x63, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x46,
	0x75, 0x6e, 0x63, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x46, 0x75, 0x6e,
	0x63, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x73, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x57, 0x0a, 0x0c, 0x50, 0x42,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xa9, 0x01, 0x0a, 0x10, 0x50, 0x42, 0x41, 0x72, 0x65, 0x6e, 0x61, 0x52,
	0x69, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x65, 0x61, 0x64,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68, 0x65, 0x61, 0x64,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x46, 0x72, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x55, 0x0a, 0x15, 0x50, 0x42, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x40, 0x0a, 0x0c, 0x50, 0x42, 0x53, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x66, 0x66, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x62, 0x75, 0x66, 0x66, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x76, 0x0a, 0x10, 0x50, 0x42, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x77, 0x61, 0x6b, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x77, 0x61, 0x6b, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x22, 0x3b, 0x0a, 0x10, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x05, 0x68, 0x65, 0x72, 0x6f, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x48, 0x65,
	0x72, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x68, 0x65, 0x72, 0x6f, 0x73, 0x22, 0x97, 0x02,
	0x0a, 0x12, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x07, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6b, 0x69,
	0x6c, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6b, 0x69, 0x6c, 0x6c, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x77, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x77, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x68, 0x72, 0x6f,
	0x70, 0x68, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x68, 0x72, 0x6f, 0x70,
	0x68, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x68, 0x70, 0x12, 0x33, 0x0a, 0x0c, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50, 0x42, 0x54, 0x72, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x74, 0x72, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4f, 0x0a, 0x0e, 0x50, 0x42, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x72, 0x69,
	0x64, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x72, 0x69, 0x64, 0x49,
	0x44, 0x12, 0x25, 0x0a, 0x04, 0x68, 0x65, 0x72, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x68, 0x65, 0x72, 0x6f, 0x22, 0x3d, 0x0a, 0x13, 0x50, 0x42, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x6e, 0x0a, 0x10, 0x50, 0x42, 0x42, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a, 0x09, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x50, 0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x52,
	0x09, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x06, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x50, 0x42, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x22, 0x4d, 0x0a, 0x0f, 0x50, 0x42, 0x4d, 0x6f, 0x76,
	0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x72,
	0x6f, 0x6d, 0x47, 0x72, 0x69, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x66, 0x72, 0x6f, 0x6d, 0x47, 0x72, 0x69, 0x64, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f,
	0x47, 0x72, 0x69, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x74, 0x6f,
	0x47, 0x72, 0x69, 0x64, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x0d, 0x50, 0x42, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50,
	0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x09, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4d, 0x0a, 0x0c, 0x50, 0x42, 0x4c, 0x69,
	0x6e, 0x65, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x68, 0x65, 0x72, 0x6f, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07,
	0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x73, 0x22, 0x4b, 0x0a, 0x11, 0x50, 0x42, 0x52, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72,
	0x61, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x6c, 0x61, 0x69,
	0x6d, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x65, 0x64, 0x22, 0x52, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x72,
	0x6f, 0x70, 0x68, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74,
	0x72, 0x6f, 0x70, 0x68, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x54, 0x72, 0x6f, 0x70, 0x68, 0x79, 0x22, 0x70, 0x0a, 0x0e, 0x50, 0x42, 0x54, 0x72,
	0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x72,
	0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x35, 0x5a, 0x27, 0x6c, 0x69,
	0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0xaa, 0x02, 0x09, 0x47, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x72,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_PublicMessage_proto_rawDescOnce sync.Once
	file_PublicMessage_proto_rawDescData = file_PublicMessage_proto_rawDesc
)

func file_PublicMessage_proto_rawDescGZIP() []byte {
	file_PublicMessage_proto_rawDescOnce.Do(func() {
		file_PublicMessage_proto_rawDescData = protoimpl.X.CompressGZIP(file_PublicMessage_proto_rawDescData)
	})
	return file_PublicMessage_proto_rawDescData
}

var file_PublicMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 84)
var file_PublicMessage_proto_goTypes = []interface{}{
	(*PBPlayerInfo)(nil),                  // 0: PBPlayerInfo
	(*PBTalentData)(nil),                  // 1: PBTalentData
	(*PBSettingData)(nil),                 // 2: PBSettingData
	(*PBHeroInfo)(nil),                    // 3: PBHeroInfo
	(*PBPlayerMoneyInfo)(nil),             // 4: PBPlayerMoneyInfo
	(*PBPlayerTitleData)(nil),             // 5: PBPlayerTitleData
	(*PBDeviceInfo)(nil),                  // 6: PBDeviceInfo
	(*PBASAIadData)(nil),                  // 7: PBASAIadData
	(*PB_MidasInfo)(nil),                  // 8: PB_MidasInfo
	(*PBCommonKeyValue)(nil),              // 9: PBCommonKeyValue
	(*PBCommonLongKeyValue)(nil),          // 10: PBCommonLongKeyValue
	(*PBCommonIntBool)(nil),               // 11: PBCommonIntBool
	(*PBIntPair)(nil),                     // 12: PBIntPair
	(*PBDropItemDataInfo)(nil),            // 13: PBDropItemDataInfo
	(*UseItemParam)(nil),                  // 14: UseItemParam
	(*PBFunctionData)(nil),                // 15: PBFunctionData
	(*PBBranchFlagData)(nil),              // 16: PBBranchFlagData
	(*PBRedDotData)(nil),                  // 17: PBRedDotData
	(*PBAttributeInfo)(nil),               // 18: PBAttributeInfo
	(*PBItemDataInfo)(nil),                // 19: PBItemDataInfo
	(*PBBagItemInfo)(nil),                 // 20: PBBagItemInfo
	(*PBNormalItemInfo)(nil),              // 21: PBNormalItemInfo
	(*PBEquipDataInfo)(nil),               // 22: PBEquipDataInfo
	(*PBEquipLevelupInfo)(nil),            // 23: PBEquipLevelupInfo
	(*PBMail)(nil),                        // 24: PBMail
	(*PBFriendRasinRewardData)(nil),       // 25: PBFriendRasinRewardData
	(*PBFriendGrowthRewardData)(nil),      // 26: PBFriendGrowthRewardData
	(*PBFriendGrowthQuestRewardData)(nil), // 27: PBFriendGrowthQuestRewardData
	(*PBCommonExpBoxInfo)(nil),            // 28: PBCommonExpBoxInfo
	(*PBActivityInfo)(nil),                // 29: PBActivityInfo
	(*PBPlayerBaseInfo)(nil),              // 30: PBPlayerBaseInfo
	(*PBPlayerOtherInfo)(nil),             // 31: PBPlayerOtherInfo
	(*PBQuestDataInfo)(nil),               // 32: PBQuestDataInfo
	(*PBChatInfo)(nil),                    // 33: PBChatInfo
	(*PBPrivateChatInfo)(nil),             // 34: PBPrivateChatInfo
	(*PBGuildMember)(nil),                 // 35: PBGuildMember
	(*PBGuildRecommend)(nil),              // 36: PBGuildRecommend
	(*PBGuildApply)(nil),                  // 37: PBGuildApply
	(*PBGuildDetailInfo)(nil),             // 38: PBGuildDetailInfo
	(*PBGuildRankInfo)(nil),               // 39: PBGuildRankInfo
	(*PBGuildTech)(nil),                   // 40: PBGuildTech
	(*PBGuildTechLevelup)(nil),            // 41: PBGuildTechLevelup
	(*PBGuildLog)(nil),                    // 42: PBGuildLog
	(*PBBargainingGift)(nil),              // 43: PBBargainingGift
	(*PBBargainingPlayer)(nil),            // 44: PBBargainingPlayer
	(*PBGuildBossRankPlayer)(nil),         // 45: PBGuildBossRankPlayer
	(*PBGuildShopOrder)(nil),              // 46: PBGuildShopOrder
	(*PBGiftItemData)(nil),                // 47: PBGiftItemData
	(*PBHeadIconDataInfo)(nil),            // 48: PBHeadIconDataInfo
	(*PBHeadFrameInfo)(nil),               // 49: PBHeadFrameInfo
	(*PBSevenSignInfo)(nil),               // 50: PBSevenSignInfo
	(*PBFirstChargeGiftData)(nil),         // 51: PBFirstChargeGiftData
	(*PBFirstChargeData)(nil),             // 52: PBFirstChargeData
	(*PBTopupRebateInfo)(nil),             // 53: PBTopupRebateInfo
	(*PBMonthlyCardInfo)(nil),             // 54: PBMonthlyCardInfo
	(*PBMonthlyCardNewInfo)(nil),          // 55: PBMonthlyCardNewInfo
	(*PBGradedFunds)(nil),                 // 56: PBGradedFunds
	(*PBGradedFundInfo)(nil),              // 57: PBGradedFundInfo
	(*PBMissionInfo)(nil),                 // 58: PBMissionInfo
	(*PBShopGiftData)(nil),                // 59: PBShopGiftData
	(*PBTimeGiftData)(nil),                // 60: PBTimeGiftData
	(*PBCommonAwardDisplay)(nil),          // 61: PBCommonAwardDisplay
	(*PBTest)(nil),                        // 62: PBTest
	(*PBOrderData)(nil),                   // 63: PBOrderData
	(*PBPayGoodsInfo)(nil),                // 64: PBPayGoodsInfo
	(*UserSnapUserInfo)(nil),              // 65: UserSnapUserInfo
	(*UserSnapUsers)(nil),                 // 66: UserSnapUsers
	(*PBFunctionPreview)(nil),             // 67: PBFunctionPreview
	(*PBInviteTask)(nil),                  // 68: PBInviteTask
	(*PBArenaRivalInfo)(nil),              // 69: PBArenaRivalInfo
	(*PBPowerRewardDataInfo)(nil),         // 70: PBPowerRewardDataInfo
	(*PBSeasonBuff)(nil),                  // 71: PBSeasonBuff
	(*PBBattleHeroInfo)(nil),              // 72: PBBattleHeroInfo
	(*PBBattleTeamInfo)(nil),              // 73: PBBattleTeamInfo
	(*PBBattlePlayerInfo)(nil),            // 74: PBBattlePlayerInfo
	(*PBCheckerBoard)(nil),                // 75: PBCheckerBoard
	(*PBBattlePlayerState)(nil),           // 76: PBBattlePlayerState
	(*PBBattleCampInfo)(nil),              // 77: PBBattleCampInfo
	(*PBMoveOperation)(nil),               // 78: PBMoveOperation
	(*PBPlayerBoard)(nil),                 // 79: PBPlayerBoard
	(*PBLineupInfo)(nil),                  // 80: PBLineupInfo
	(*PBRankRewardState)(nil),             // 81: PBRankRewardState
	(*SeasonTrophyInfo)(nil),              // 82: SeasonTrophyInfo
	(*PBTreasureInfo)(nil),                // 83: PBTreasureInfo
	(LoginByType)(0),                      // 84: LoginByType
	(EGenderType)(0),                      // 85: EGenderType
	(AwardItemType)(0),                    // 86: AwardItemType
	(AttributeType)(0),                    // 87: AttributeType
	(BagItemType)(0),                      // 88: BagItemType
	(EquipType)(0),                        // 89: EquipType
	(MailType)(0),                         // 90: MailType
	(MailStateType)(0),                    // 91: MailStateType
	(MailReasonType)(0),                   // 92: MailReasonType
	(ActivityType)(0),                     // 93: ActivityType
	(ActivityStatus)(0),                   // 94: ActivityStatus
	(ChatMsgType)(0),                      // 95: ChatMsgType
	(GuildPosition)(0),                    // 96: GuildPosition
	(GuildLogType)(0),                     // 97: GuildLogType
	(MissionState)(0),                     // 98: MissionState
	(FuncPreviewstate)(0),                 // 99: FuncPreviewstate
	(RewardStatus)(0),                     // 100: RewardStatus
}
var file_PublicMessage_proto_depIdxs = []int32{
	4,   // 0: PBPlayerInfo.moneyData:type_name -> PBPlayerMoneyInfo
	84,  // 1: PBPlayerInfo.gameCenterLoginType:type_name -> LoginByType
	5,   // 2: PBPlayerInfo.titleData:type_name -> PBPlayerTitleData
	18,  // 3: PBPlayerInfo.attributeLevelupList:type_name -> PBAttributeInfo
	1,   // 4: PBPlayerInfo.talentData:type_name -> PBTalentData
	2,   // 5: PBPlayerInfo.settingData:type_name -> PBSettingData
	85,  // 6: PBPlayerInfo.gender:type_name -> EGenderType
	18,  // 7: PBTalentData.attributeTalentList:type_name -> PBAttributeInfo
	86,  // 8: PBDropItemDataInfo.itemType:type_name -> AwardItemType
	12,  // 9: UseItemParam.chooseInfos:type_name -> PBIntPair
	87,  // 10: PBAttributeInfo.attributeType:type_name -> AttributeType
	88,  // 11: PBBagItemInfo.type:type_name -> BagItemType
	21,  // 12: PBBagItemInfo.normalItem:type_name -> PBNormalItemInfo
	89,  // 13: PBEquipDataInfo.type:type_name -> EquipType
	89,  // 14: PBEquipLevelupInfo.type:type_name -> EquipType
	90,  // 15: PBMail.type:type_name -> MailType
	9,   // 16: PBMail.goods:type_name -> PBCommonKeyValue
	91,  // 17: PBMail.status:type_name -> MailStateType
	92,  // 18: PBMail.reason:type_name -> MailReasonType
	9,   // 19: PBCommonExpBoxInfo.box_index_list:type_name -> PBCommonKeyValue
	93,  // 20: PBActivityInfo.activityType:type_name -> ActivityType
	94,  // 21: PBActivityInfo.activityStatus:type_name -> ActivityStatus
	9,   // 22: PBQuestDataInfo.good:type_name -> PBCommonKeyValue
	95,  // 23: PBChatInfo.msgType:type_name -> ChatMsgType
	33,  // 24: PBPrivateChatInfo.chatinfo:type_name -> PBChatInfo
	96,  // 25: PBGuildMember.gpos:type_name -> GuildPosition
	97,  // 26: PBGuildLog.type:type_name -> GuildLogType
	44,  // 27: PBBargainingGift.players:type_name -> PBBargainingPlayer
	44,  // 28: PBBargainingGift.noBargainingPlayers:type_name -> PBBargainingPlayer
	51,  // 29: PBFirstChargeData.giftInfos:type_name -> PBFirstChargeGiftData
	56,  // 30: PBGradedFundInfo.gradedFundInfo:type_name -> PBGradedFunds
	98,  // 31: PBMissionInfo.missionState:type_name -> MissionState
	65,  // 32: UserSnapUsers.users:type_name -> UserSnapUserInfo
	99,  // 33: PBFunctionPreview.RewardState:type_name -> FuncPreviewstate
	100, // 34: PBInviteTask.status:type_name -> RewardStatus
	72,  // 35: PBBattleTeamInfo.heros:type_name -> PBBattleHeroInfo
	83,  // 36: PBBattlePlayerInfo.treasureList:type_name -> PBTreasureInfo
	72,  // 37: PBCheckerBoard.hero:type_name -> PBBattleHeroInfo
	75,  // 38: PBBattleCampInfo.boardInfo:type_name -> PBCheckerBoard
	74,  // 39: PBBattleCampInfo.player:type_name -> PBBattlePlayerInfo
	75,  // 40: PBPlayerBoard.boardInfo:type_name -> PBCheckerBoard
	41,  // [41:41] is the sub-list for method output_type
	41,  // [41:41] is the sub-list for method input_type
	41,  // [41:41] is the sub-list for extension type_name
	41,  // [41:41] is the sub-list for extension extendee
	0,   // [0:41] is the sub-list for field type_name
}

func init() { file_PublicMessage_proto_init() }
func file_PublicMessage_proto_init() {
	if File_PublicMessage_proto != nil {
		return
	}
	file_PublicEnum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_PublicMessage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBTalentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBSettingData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBHeroInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPlayerMoneyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPlayerTitleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBDeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBASAIadData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PB_MidasInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBCommonKeyValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBCommonLongKeyValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBCommonIntBool); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBIntPair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBDropItemDataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UseItemParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBFunctionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBranchFlagData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBRedDotData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBAttributeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBItemDataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBagItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBNormalItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBEquipDataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBEquipLevelupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBMail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBFriendRasinRewardData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBFriendGrowthRewardData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBFriendGrowthQuestRewardData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBCommonExpBoxInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBActivityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPlayerBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPlayerOtherInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBQuestDataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBChatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPrivateChatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildRecommend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildApply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildDetailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildRankInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildTech); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildTechLevelup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBargainingGift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBargainingPlayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildBossRankPlayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGuildShopOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGiftItemData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBHeadIconDataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBHeadFrameInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBSevenSignInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBFirstChargeGiftData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBFirstChargeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBTopupRebateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBMonthlyCardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBMonthlyCardNewInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGradedFunds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBGradedFundInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBMissionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBShopGiftData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBTimeGiftData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBCommonAwardDisplay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBTest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBOrderData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPayGoodsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSnapUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSnapUsers); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBFunctionPreview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBInviteTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBArenaRivalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPowerRewardDataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBSeasonBuff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBattleHeroInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBattleTeamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBattlePlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBCheckerBoard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBattlePlayerState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBBattleCampInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBMoveOperation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBPlayerBoard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBLineupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBRankRewardState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeasonTrophyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_PublicMessage_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBTreasureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_PublicMessage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   84,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicMessage_proto_goTypes,
		DependencyIndexes: file_PublicMessage_proto_depIdxs,
		MessageInfos:      file_PublicMessage_proto_msgTypes,
	}.Build()
	File_PublicMessage_proto = out.File
	file_PublicMessage_proto_rawDesc = nil
	file_PublicMessage_proto_goTypes = nil
	file_PublicMessage_proto_depIdxs = nil
}
