#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityHeroRoad
	{

		public static readonly string TName="ActivityHeroRoad.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 等级 
		/// </summary> 
		public int Lv {get; set;}
		/// <summary> 
		/// 获得英雄个数 
		/// </summary> 
		public int HeroNumber {get; set;}
		/// <summary> 
		/// 免费奖励 
		/// </summary> 
		public int[][] FreeDrop {get; set;}
		/// <summary> 
		/// 付费金额 
		/// </summary> 
		public int Price {get; set;}
		/// <summary> 
		/// 付费奖励 
		/// </summary> 
		public int[][] PayDrop {get; set;}
		#endregion

		public static TableActivityHeroRoad GetData(int ID)
		{
			return TableManager.ActivityHeroRoadData.Get(ID);
		}

		public static List<TableActivityHeroRoad> GetAllData()
		{
			return TableManager.ActivityHeroRoadData.GetAll();
		}

	}
	public sealed partial class TableActivityHeroRoadData
	{
		private Dictionary<int, TableActivityHeroRoad> dict = new Dictionary<int, TableActivityHeroRoad>();
		private List<TableActivityHeroRoad> dataList = new List<TableActivityHeroRoad>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityHeroRoad.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityHeroRoad>>(jsonContent);
			foreach (TableActivityHeroRoad config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityHeroRoad Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityHeroRoad item))
				return item;
			return null;
		}

		public List<TableActivityHeroRoad> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
