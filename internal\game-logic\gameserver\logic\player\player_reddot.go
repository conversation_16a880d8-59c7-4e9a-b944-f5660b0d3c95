package player

import (
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/rpc_def"
	"liteframe/pkg/log"
)

// RedDot 红点系统模块
type RedDot struct {
	player  *Player
	db      *dbstruct.RedDot
	redDots map[public.RedDotType]bool // 红点状态缓存
	dirty   bool                       // 是否需要同步到客户端
}

// NewRedDot 创建红点模块
func NewRedDot(p *Player) *RedDot {
	return &RedDot{
		player:  p,
		redDots: make(map[public.RedDotType]bool),
		dirty:   false,
	}
}

// InitDB 初始化模块数据
func (r *RedDot) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.RedDot == nil {
		db.Game.RedDot = &dbstruct.RedDot{
			RedDotStates: make(map[int32]bool),
		}
	}

	r.db = db.Game.RedDot
	if r.db.RedDotStates == nil {
		r.db.RedDotStates = make(map[int32]bool)
	}

	// 从数据库加载红点状态到缓存
	r.redDots = make(map[public.RedDotType]bool)
	for redDotTypeInt, state := range r.db.RedDotStates {
		redDotType := public.RedDotType(redDotTypeInt)
		r.redDots[redDotType] = state
	}

	log.Info("RedDot InitDB", log.Kv("uid", r.player.Uid()), log.Kv("redDotCount", len(r.redDots)))
}

// OnCrossDay 处理跨天逻辑
func (r *RedDot) OnCrossDay(natural bool, nowUnix int64) {
	// 跨天时刷新所有红点状态
	r.RefreshAllRedDots()
}

// UpdateRedDot 更新红点状态
// @Export
func (r *RedDot) UpdateRedDot(redDotType public.RedDotType, show bool) {
	// 检查状态是否有变化
	if r.redDots[redDotType] == show {
		return
	}

	// 更新缓存和数据库
	r.redDots[redDotType] = show
	r.db.RedDotStates[int32(redDotType)] = show
	r.dirty = true
	r.RefreshAllRedDots()

	log.Info("UpdateRedDot",
		log.Kv("uid", r.player.Uid()),
		log.Kv("redDotType", int32(redDotType)),
		log.Kv("show", show))
}

// GetRedDotState 获取红点状态
// @Export
func (r *RedDot) GetRedDotState(redDotType public.RedDotType) bool {
	return r.redDots[redDotType]
}

// SyncToClient 同步红点数据到客户端
// @Export
func (r *RedDot) SyncToClient() {
	if !r.dirty {
		return
	}

	msg := &cs.LCSyncRedDotData{
		Reddata: make([]*public.PBRedDotData, 0),
	}

	// 构建红点数据
	for redDotType, show := range r.redDots {
		syncData := int32(0)
		if show {
			syncData = 1
		}
		msg.Reddata = append(msg.Reddata, &public.PBRedDotData{
			SyncIndex: int32(redDotType),
			SyncData:  syncData,
		})
	}

	// 发送到客户端
	r.player.SendToClient(rpc_def.LCSyncRedDotData, msg, false)
	r.dirty = false

	log.Info("SyncToClient",
		log.Kv("uid", r.player.Uid()),
		log.Kv("redDotCount", len(msg.Reddata)))
}

// RefreshAllRedDots 刷新所有红点状态
// @Export
func (r *RedDot) RefreshAllRedDots() {
	// 刷新各个功能模块的红点状态
	r.refreshBagRedDot()
	r.refreshMailRedDot()
	r.refreshSkillRedDot()
	r.refreshFriendRedDot()
	r.refreshHeavenlyDaoRedDot()
	r.refreshDailyPlusGiftRedDot()
	r.refreshFuncPreviewRedDot()

	// 同步到客户端
	r.SyncToClient()
}

// refreshBagRedDot 刷新背包红点
func (r *RedDot) refreshBagRedDot() {
	// 检查是否有可使用的道具
	hasUsableItem := r.checkBagUsableItems()
	r.UpdateRedDot(public.RedDotType_RedDotType_Bag, hasUsableItem)
}

// checkBagUsableItems 检查背包中是否有可使用的道具
func (r *RedDot) checkBagUsableItems() bool {
	// 这里需要与背包模块协作，检查是否有可使用的道具
	// 暂时返回false，后续需要实现具体逻辑
	return false
}

// checkUnreadMail 检查是否有未读邮件
func (r *RedDot) checkUnreadMail() bool {
	mailData := r.player.Mail().MailDataList
	for _, mail := range mailData {
		if mail.Status == int32(public.MailStateType_MailStateType_UnRead) {
			return true
		}
	}
	return false
}

// refreshMailRedDot 刷新邮件红点
func (r *RedDot) refreshMailRedDot() {
	// 检查是否有未读邮件或可领取的邮件
	hasUnreadMail := r.checkUnreadMail()
	r.UpdateRedDot(public.RedDotType_RedDotType_Mail, hasUnreadMail)
}

// refreshSkillRedDot 刷新技能红点
func (r *RedDot) refreshSkillRedDot() {
	// 检查是否有可升级的技能
	// 暂时返回false，后续需要实现具体逻辑
	r.UpdateRedDot(public.RedDotType_RedDotType_Skill, false)
}

// refreshFriendRedDot 刷新好友红点
func (r *RedDot) refreshFriendRedDot() {
	// 检查是否有好友申请或其他好友相关事件
	// 暂时返回false，后续需要实现具体逻辑
	r.UpdateRedDot(public.RedDotType_RedDotType_Friend, false)
}

// refreshHeavenlyDaoRedDot 刷新天道修为红点
func (r *RedDot) refreshHeavenlyDaoRedDot() {
	// 检查是否可以提升天道修为
	// 暂时返回false，后续需要实现具体逻辑
	r.UpdateRedDot(public.RedDotType_RedDotType_HeavenlyDao, false)
}

// refreshDailyPlusGiftRedDot 刷新每日特惠礼包红点
func (r *RedDot) refreshDailyPlusGiftRedDot() {
	// 检查是否有可购买的每日特惠礼包
	// 暂时返回false，后续需要实现具体逻辑
	r.UpdateRedDot(public.RedDotType_RedDotType_DailyPlusGift, false)
}

// refreshFuncPreviewRedDot 刷新功能预告红点
func (r *RedDot) refreshFuncPreviewRedDot() {
	// 检查是否有新的功能预告
	// 暂时返回false，后续需要实现具体逻辑
	r.UpdateRedDot(public.RedDotType_RedDotType_FuncPreview, false)
}

// SaveDB 保存数据到数据库
func (r *RedDot) SaveDB() {
	// 数据已经在UpdateRedDot中同步到r.db，这里不需要额外操作
	log.Debug("RedDot SaveDB", log.Kv("uid", r.player.Uid()))
}
