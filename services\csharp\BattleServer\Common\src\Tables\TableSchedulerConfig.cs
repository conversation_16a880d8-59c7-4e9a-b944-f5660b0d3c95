#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableSchedulerConfig
	{

		public static readonly string TName="SchedulerConfig.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 相对于第一次执行0点的秒 
		/// </summary> 
		public int Second {get; set;}
		#endregion

		public static TableSchedulerConfig GetData(int ID)
		{
			return TableManager.SchedulerConfigData.Get(ID);
		}

		public static List<TableSchedulerConfig> GetAllData()
		{
			return TableManager.SchedulerConfigData.GetAll();
		}

	}
	public sealed partial class TableSchedulerConfigData
	{
		private Dictionary<int, TableSchedulerConfig> dict = new Dictionary<int, TableSchedulerConfig>();
		private List<TableSchedulerConfig> dataList = new List<TableSchedulerConfig>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableSchedulerConfig.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableSchedulerConfig>>(jsonContent);
			foreach (TableSchedulerConfig config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableSchedulerConfig Get(int id)
		{
			if (dict.TryGetValue(id, out TableSchedulerConfig item))
				return item;
			return null;
		}

		public List<TableSchedulerConfig> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
