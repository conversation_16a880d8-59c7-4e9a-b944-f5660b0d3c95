#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableCommonBoxReward
	{

		public static readonly string TName="CommonBoxReward.json";

		#region 属性定义
		/// <summary> 
		/// 任务idID：通告任务，需要在ShowConfig表里配任务id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 宝箱类型（对应枚举CommonBoxRewardType） 
		/// </summary> 
		public int BoxType {get; set;}
		/// <summary> 
		/// 宝箱进度 
		/// </summary> 
		public int[] BoxProgress {get; set;}
		/// <summary> 
		/// 宝箱奖励 
		/// </summary> 
		public int[] BoxReward {get; set;}
		/// <summary> 
		/// 宝箱掉落显示奖励（问程序应该配几） 
		/// </summary> 
		public int[] BoxShowType {get; set;}
		/// <summary> 
		/// 宝箱经验道具 
		/// </summary> 
		public int BoxExpItemId {get; set;}
		#endregion

		public static TableCommonBoxReward GetData(int ID)
		{
			return TableManager.CommonBoxRewardData.Get(ID);
		}

		public static List<TableCommonBoxReward> GetAllData()
		{
			return TableManager.CommonBoxRewardData.GetAll();
		}

	}
	public sealed partial class TableCommonBoxRewardData
	{
		private Dictionary<int, TableCommonBoxReward> dict = new Dictionary<int, TableCommonBoxReward>();
		private List<TableCommonBoxReward> dataList = new List<TableCommonBoxReward>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableCommonBoxReward.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableCommonBoxReward>>(jsonContent);
			foreach (TableCommonBoxReward config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableCommonBoxReward Get(int id)
		{
			if (dict.TryGetValue(id, out TableCommonBoxReward item))
				return item;
			return null;
		}

		public List<TableCommonBoxReward> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
