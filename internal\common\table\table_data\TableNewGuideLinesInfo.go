/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableNewGuideLinesInfo struct {
	// ============= 变量定义 =============
	// 引导步骤ID，ID不是连续的，每一段连续的ID则是一个引导组
	ID int32
	// 下一步新手的id    （-1自动结束    0不一定结束，取决于trigger表EndParameter列结束条件
	NextId int32
	// 点击按钮（填写路径）1、点击全屏继续。3、路径有则为gameObject绝对路径
	ButtonPathType int32
	// 点击按钮
	ButtonPath string
	// 打开界面
	UIOpen []int32
	// 校验ID
	CheckFunId int32
	// 断线回退引导步骤ID（-1为直接完成当前引导组）
	GoBackId int32
	// 是否显示半透蒙版，-1不显示 1显示  2、透明蒙版
	Masking int32
	// 滑动列表   1不可滑动    -1无
	ScrollView int32
	// 查找延迟(除以100)
	FindDelay int32
	// 是否有光圈（-1没有，1显示手 2、没有手，挖空的3、拖动合成 4 拖动上阵 5、第一次拖动龙上阵）
	IsHighlight int32
	// 手方向（1.右下2.左下3.左上4.右上）
	HandDir int32
	// 光圈缩放比例（真实值是除以100的小数）
	HighlightScaleX int32
	// 光圈缩放比例（真实值是除以100的小数）
	HighlightScaleY int32
	// 偏移X( 比例偏移  除以10000） 例如填写2500 为4分之一
	OffsetX int32
	// 偏移Y( 比例偏移  除以10000）
	OffsetY int32
	// 对话id 字典id
	DialogID int32
	// 对话位置类型0中间1上面2下面
	DialogPosType int32
	// 对话偏移正数向上 负数向下
	DialogOffsetY int32
	// 龙岛建筑定位id（DragonBuildAreaId）只有龙岛的引导能用
	AreaId int32
	// 战斗是否暂停
	IsBattleStop int32
}




// TableNewGuideLinesInfoData 表格
type TableNewGuideLinesInfoData struct {
	file    string
	dataMap map[int32]*TableNewGuideLinesInfo
	Data    []*TableNewGuideLinesInfo
	md5     string
}

// load 加载
func (tb *TableNewGuideLinesInfoData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableNewGuideLinesInfo{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableNewGuideLinesInfo, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableNewGuideLinesInfo)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableNewGuideLinesInfo, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableNewGuideLinesInfoData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableNewGuideLinesInfo{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableNewGuideLinesInfo, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableNewGuideLinesInfo)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableNewGuideLinesInfoData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableNewGuideLinesInfoData) GetById(id int32) *TableNewGuideLinesInfo {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableNewGuideLinesInfoData) GetCloneById(id int32) *TableNewGuideLinesInfo {
	v := tb.dataMap[id]
	out := &TableNewGuideLinesInfo{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableNewGuideLinesInfoData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableNewGuideLinesInfoData) Foreach(call func(*TableNewGuideLinesInfo) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableNewGuideLinesInfoData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableNewGuideLinesInfoData) Clone() ITable {
	ntb := &TableNewGuideLinesInfoData{
		file:    tb.file,
		dataMap: make(map[int32]*TableNewGuideLinesInfo),
		Data:    make([]*TableNewGuideLinesInfo, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableNewGuideLinesInfo{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
