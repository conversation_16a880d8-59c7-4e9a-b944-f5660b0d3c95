﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-15
//*********************************************************

using System;
using System.IO;
using System.Net;
using System.Net.Sockets;

namespace Aurora.Framework
{
    public sealed class TChannel : AChannel
    {
        private Socket m_Socket;
        private SocketAsyncEventArgs m_InArgs = new SocketAsyncEventArgs();
        private SocketAsyncEventArgs m_OutArgs = new SocketAsyncEventArgs();

        private ThreadSyncContext m_SyncContext = new ThreadSyncContext();

        private readonly CircularBuffer m_RecvBuffer = new CircularBuffer();
        private readonly CircularBuffer m_SendBuffer = new CircularBuffer();

        private readonly PacketParser m_Parser;

        private bool m_Connected;
        private bool m_Sending;
        private long m_LastSendTime;
        private long m_SendTimeout;

        private MemoryStream m_TempRecvStream;
        private byte[] m_TempSend;

        public Action m_ConnectCallback;

		public Action m_ErrorCallback;
		public Action<Packet> m_ReadCallback;

        public long SendBufferLength
        {
            get
            {
                if (m_SendBuffer != null)
                {
                    return m_SendBuffer.Length;
                }
                else
                {
                    return 0;
                }
            }
        }

		public TChannel(Socket socket)
        {
            if (socket == null) return;
			ChannelType = ChannelType.Accept;
			m_Socket = socket;
			m_Socket.NoDelay = true;
			m_Socket.ReceiveBufferSize = 64 * 1024;
			m_Socket.SendBufferSize = 64 * 1024;
			m_InArgs.Completed += this.OnComplete;
			m_OutArgs.Completed += this.OnComplete;
			m_Connected = true;
			m_Sending = false;
            m_SendTimeout = 10 * 60 * 1000;
			m_TempRecvStream = new MemoryStream();
			m_TempSend = new byte[64];
			m_Parser = new PacketParser(m_RecvBuffer);
			RemoteAddress = (IPEndPoint)socket.RemoteEndPoint;
			// 下一帧再开始读写
			m_SyncContext.Post(() =>
			{
				StartRecv();
				StartSend();
			});
		}

		public TChannel(IPEndPoint ipEndPoint)
		{
			ChannelType = ChannelType.Connect;
			
			try
			{
				// 记录Socket客户端连接开始
				SocketLogHelper.LogSocketClientStart("TChannel", ipEndPoint);
				
				m_Socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
				m_Socket.NoDelay = true;
				m_Socket.ReceiveBufferSize = 64 * 1024;
				m_Socket.SendBufferSize = 64 * 1024;
				m_InArgs.Completed += this.OnComplete;
				m_OutArgs.Completed += this.OnComplete;
				RemoteAddress = ipEndPoint;
				m_Connected = false;
				m_Sending = false;
				m_SendTimeout = 0;
				m_TempRecvStream = new MemoryStream();
				m_TempSend = new byte[64];
				m_Parser = new PacketParser(m_RecvBuffer);
				m_SyncContext.Post(ConnectAsync);
			}
			catch (Exception ex)
			{
				// 记录Socket客户端连接失败
				SocketLogHelper.LogSocketClientFailed("TChannel", ipEndPoint, ex.Message);
				throw;
			}
		}

        public void Update()
        {
            if (m_Connected)
            {
                if (m_Socket != null)
                {
                    if (!m_Socket.Connected)
                    {
                        OnNetError();
                        return;
                    }
                }
            }

            m_SyncContext.Update();

            StartSend();
        }
        //主线程调用，发送包体
        public void Send(Packet pkt)
        {
            if (pkt == null) 
            {
                return;
            }
           
            if (!pkt.IsSerialized) 
            {
                pkt.Serialize();
            }
            
            int messageSize = pkt.PktSize;
            if (messageSize > ushort.MaxValue)
            {
                throw new FrameworkException($"send packet too large: {messageSize}");
            }
                     
#if PRINT_PACKET
            Log.Info($"[PacketSize] Send,{pkt.MsgID},{pkt.PktSize},{messageSize}");
#endif
            
            pkt.Seek(0, SeekOrigin.Begin);
            m_SendBuffer.Write(pkt);

            StartSend();
        }
        //主线程调用，异步开始连接
        private void ConnectAsync()
        {
            m_OutArgs.RemoteEndPoint = RemoteAddress;
            if(m_Socket.ConnectAsync(m_OutArgs))
            {
                //异步挂起，通过Args返回完成事件
                return;
            }
            //同步完成，直接调用
            OnConnectComplete(m_OutArgs);
        }
        //主线程调用，异步开始接收
        private void StartRecv()
        {
            while(true)
            {
                try
                {
                    if (m_Socket == null)
                    {
                        return;
                    }
                    int size = CircularBuffer.ChunkSize - m_RecvBuffer.TailIndex;
                    m_InArgs.SetBuffer(m_RecvBuffer.Tail, m_RecvBuffer.TailIndex, size);
                }
                catch (Exception e)
                {
                    Log.Exception($"TChannel StartRecv error:{e.Message}");
                    return;
                }
                //异步执行
                if (m_Socket.ReceiveAsync(m_InArgs))
                {
                    return;
                }
                //同步执行
                HandleRecv(m_InArgs);
            }
        }
        //主线程调用，异步开始发送
        private void StartSend()
        {
            if (!m_Connected)
                return;

            if (m_Sending)
            {
                if (m_SendTimeout > 0)
                {
                    if ((ATimer.Instance.SystemTicks - m_LastSendTime) > m_SendTimeout)
                    {
                        OnNetError();
                    }
                }
                return;
            }

            while (true)
            {
                try
                {
                    if (m_Socket == null)
                    {
                        m_Sending = false;
                        return;
                    }

                    // 没有数据需要发送
                    if (m_SendBuffer.Length == 0)
                    {
                        m_Sending = false;
                        return;
                    }

                    m_Sending = true;
                    if (m_SendTimeout > 0)
                    {
                        m_LastSendTime = ATimer.Instance.SystemTicks;
                    }

                    int sendSize = CircularBuffer.ChunkSize - m_SendBuffer.HeadIndex;
                    if (sendSize > m_SendBuffer.Length)
                    {
                        sendSize = (int)m_SendBuffer.Length;
                    }
                    m_OutArgs.SetBuffer(m_SendBuffer.Head, m_SendBuffer.HeadIndex, sendSize);
                    if(m_Socket.SendAsync(m_OutArgs))
                    {
                        //异步发送
                        return;
                    }
                    //这是同步发送的
                    HandleSend(m_OutArgs);
                }
                catch(Exception e)
                {
                    Log.Exception($"TChannel start send error:{e.Message}\nStackTrace:\n{e.StackTrace}");
                }
            }
        }
        public override void Clear()
        {
            m_Socket?.Close();
            m_InArgs?.Dispose();
            m_OutArgs?.Dispose();
            m_InArgs = null;
            m_OutArgs = null;
            m_Socket = null;
            m_TempRecvStream = null;
            m_TempSend = null;
            m_Connected = false;
            m_Sending = false;
        }

        //主线程调用，连接成功
        private void OnConnectComplete(object o)
        {
            if (m_Socket == null)
                return;
            SocketAsyncEventArgs sargs = (SocketAsyncEventArgs)o;
            if(sargs.SocketError != SocketError.Success)
            {
                //出错了
                OnNetError();
                return;
            }
            
            // 记录Socket客户端连接成功
            SocketLogHelper.LogSocketClientSuccess("TChannel", RemoteAddress);
            
            Log.Debug($"Connect Adress:{sargs.RemoteEndPoint.ToString()} successful!");
            sargs.RemoteEndPoint = null;
            m_Connected = true;
            m_ConnectCallback.Invoke();
            StartRecv();
            StartSend();
        }

        //主线程调用，连接成功
        private void OnRecvComplete(object o)
        {
            HandleRecv(o);
            if (m_Socket == null)
                return;

            StartRecv();
        }

        //主线程调用，发送成功
        private void OnSendComplete(object o)
        {
            HandleSend(o);
            m_Sending = false;

            if (m_Socket == null)
                return;
            //调用异步继续发送
            StartSend();
        }

        //主线程调用，断开成功
        private void OnDisconnectComplete(object o)
        {
            SocketAsyncEventArgs sArgs = (SocketAsyncEventArgs)o;
            OnNetError();
        }
        //主线程调用，处理网络接收->Packet
        private void HandleRecv(object o)
        {
            if (m_Socket == null)
                return;

            SocketAsyncEventArgs sArgs = (SocketAsyncEventArgs)o;

            if (sArgs.SocketError != SocketError.Success)
            {
                OnNetError();
                return;
            }

            if (sArgs.BytesTransferred == 0)
            {
                OnNetError();
                return;
            }

            m_RecvBuffer.TailIndex += sArgs.BytesTransferred;
            if (m_RecvBuffer.TailIndex == CircularBuffer.ChunkSize)
            {
                m_RecvBuffer.AddTail();
                m_RecvBuffer.TailIndex = 0;
            }

            while (true)
            {
                if (m_Socket == null)
                    return;
                try
                {
                    //通过Buff解包，发送给上层Service，绑定到NetComponent派发处理
                    bool ret = m_Parser.Parse();
                    if (!ret)
                    {
                        break;
                    }
                    OnRead(m_Parser.Pkt);
                }
                catch (Exception e)
                {
                    Log.Exception($"TChannel handle recv error:{e.Message}\nStackTrace:\n{e.StackTrace}");
                    OnNetError();
                }
            }
        }

        //主线程调用，处理已发送
        private void HandleSend(object o)
        {
            if (m_Socket == null)
                return;

            SocketAsyncEventArgs sArgs = (SocketAsyncEventArgs)o;

            if (sArgs.SocketError != SocketError.Success)
            {
                OnNetError();
                return;
            }

            if (sArgs.BytesTransferred == 0)
            {
                OnNetError();
                return;
            }

            m_SendBuffer.HeadIndex += sArgs.BytesTransferred;
            if (m_SendBuffer.HeadIndex == CircularBuffer.ChunkSize)
            {
                m_SendBuffer.HeadIndex = 0;
                m_SendBuffer.RemoveHead();
            }
        }
        private void OnNetError()
        {
            string ClientIp = "";
            if (RemoteAddress != null)
            {
                if (RemoteAddress.Address != null)
                {
                    ClientIp = RemoteAddress.Address.ToString();
                }
            }
            Log.Error($"TChannel OnError!, ClientIp:{ClientIp}");
            m_ErrorCallback.Invoke();
        }
        private void OnRead(Packet pkt)
        {
            try
            {
                m_ReadCallback.Invoke(pkt);
            }
            catch (Exception e)
            {
                Log.Error($"Channel Error:{RemoteAddress.ToString()} {pkt.PktSize} {e.Message}");
                // 出现任何消息解析异常都要断开Session，防止客户端伪造消息
                OnNetError();
            }
        }

        //网络线程池的线程回调
        private void OnComplete(object sender, SocketAsyncEventArgs e)
        {
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.Connect:
                    m_SyncContext.Post(() => OnConnectComplete(e));
                    break;
                case SocketAsyncOperation.Receive:
					m_SyncContext.Post(() => OnRecvComplete(e));
                    break;
                case SocketAsyncOperation.Send:
					m_SyncContext.Post(() => OnSendComplete(e));
                    break;
                case SocketAsyncOperation.Disconnect:
					m_SyncContext.Post(() => OnDisconnectComplete(e));
                    break;
                default:
                    throw new FrameworkException($"TChannel socket error: {e.LastOperation.ToString()}");
            }
        }

    }
}
