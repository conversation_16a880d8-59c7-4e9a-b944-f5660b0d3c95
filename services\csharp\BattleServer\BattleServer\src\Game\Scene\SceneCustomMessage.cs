﻿using BattleServer.Service;
using Game.Core;
using Aurora.Framework;

namespace BattleServer.Game
{
    [MessageID((ushort)CustomMessageType.CreateSceneReq)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(CreateSceneResp))]
    public class CreateSceneReq : IRequest
    {
        public ushort SceneID;
        public List<PBBattlePlayerInfo> players;
        public List<PBBattleTeamInfo> teams;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.CreateSceneResp)]
    [MessageType(MessageClass.IResponse)]
    public class CreateSceneResp : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public ushort SceneID;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerEnterSceneReq)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(PlayerEnterSceneResp))]
    public class PlayerEnterSceneReq : IRequest
    {
        public ulong Uid;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerEnterSceneResp)]
    [MessageType(MessageClass.IResponse)]
    public class PlayerEnterSceneResp : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerSlectBufferReq)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(PlayerSelectBufferResp))]
    public class PlayerSelectBufferReq : IRequest
    {
        public ulong Uid;
        public int BufferID;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerSlectBufferResp)]
    [MessageType(MessageClass.IResponse)]
    public class PlayerSelectBufferResp : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public int Code;
        public List<PBCheckerBoard> NewBoards;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerMergeHeroReq)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(PlayerMergeHeroResp))]
    public class PlayerMergeHeroReq : IRequest
    {
        public MergeHeroReq pbMsg;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerMergeHeroResp)]
    [MessageType(MessageClass.IResponse)]
    public class PlayerMergeHeroResp : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public MergeHeroResp pbMsg;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerReadyReq)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(PlayerReadyResp))]
    public class PlayerReadyReq : IRequest
    {
        public ulong Uid;
        public List<PBMoveOperation> moveOperation;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerReadyResp)]
    [MessageType(MessageClass.IResponse)]
    public class PlayerReadyResp : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public int code;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerEndBattleReq)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(PlayerEndBattleResp))]
    public class PlayerEndBattleReq : IRequest
    {
        public ulong Uid;
        public bool Win;

        public int PrecheckType
        {
            get { return 1; }
        }
    }

    [MessageID((ushort)CustomMessageType.PlayerEndBattleResp)]
    [MessageType(MessageClass.IResponse)]
    public class PlayerEndBattleResp : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public int code;

        public int PrecheckType
        {
            get { return 1; }
        }
    }
}