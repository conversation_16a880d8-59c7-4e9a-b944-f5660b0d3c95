﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-2-22
//*********************************************************


using System;
using System.Collections.Generic;
using System.Linq;
using MemoryPack;

namespace Aurora.Framework
{
    public enum ListDicSyncOperateType
    {
        eOpNone = -1,
        eOpAdd,
        eOpRemove,
        eOpUpdate,
    }

    [MemoryPackable]
    public partial class PoolListSyncData
    {
        [MemoryPackOrder(0)]
        public ListDicSyncOperateType eOp { get; set; }

        [MemoryPackOrder(1)]
        public int index { get; set; }
        [MemoryPackOrder(2)]
        public EParamIncrementType eParentIncType { get; set; }
        [MemoryPackOrder(3)]
        public int parentIndex { get; set; }

        [MemoryPackOrder(4)]
        public byte[] Data { get; set; }

    }


    [MemoryPackable]
    public partial class PoolDicSyncData
    {
        [MemoryPackOrder(0)]
        public ListDicSyncOperateType eOp { get; set; }

        [MemoryPackOrder(1)]
        public long key { get; set; }

        [MemoryPackOrder(2)]
        public EParamIncrementType eParentIncType { get; set; }

        [MemoryPackOrder(3)]
        public int parentIndex { get; set; }

        [MemoryPackOrder(4)]
        public byte[] Data { get; set; }

    }

    //[ProtoContract]
    //public class PoolQueueSyncData
    //{
    //    [ProtoMember(1)]
    //    public ListDicSyncOperateType eOp { get; set; }

    //    //[ProtoMember(2)]
    //    //public int index { get; set; }
    //    [ProtoMember(2)]
    //    public EParamIncrementType eParentIncType { get; set; }
    //    [ProtoMember(3)]
    //    public int parentIndex { get; set; }

    //    [ProtoMember(4)]
    //    public byte[] Data { get; set; }

    //}

    [MemoryPackable]
    public partial class PoolData
    {
        [MemoryPackOrder(0)]
        public int PoolType { get; set; }

        [MemoryPackOrder(1)]
        public byte[] Data { get; set; }

    }
    
    [MemoryPackable]
    public partial class ParamData
    {
        [MemoryPackOrder(0)]
        public int PoolType { get; set; }

        [MemoryPackOrder(1)]
        public int Index { get; set; }

        [MemoryPackOrder(2)]
        public byte[] Data { get; set; }

    }

    [MemoryPackable]
    public partial class SubParamData
    {
        [MemoryPackOrder(0)]
        public int ParentPoolType { get; set; }

        [MemoryPackOrder(1)]
        public int ParentIndex { get; set; }

        [MemoryPackOrder(2)]
        public int PoolType { get; set; }

        [MemoryPackOrder(3)]
        public int Index { get; set; }

        [MemoryPackOrder(4)]
        public SubEntityKey SubEntityId { get; set; }

        [MemoryPackOrder(5)]
        public byte[] Data { get; set; }

    }
    [MemoryPackable]
    public partial class SubCompStruct
    {
        [MemoryPackOrder(0)]
        public int PoolType { get; set; }

        [MemoryPackOrder(1)]
        public List<SubEntityKey> SubCompSubKeys { get; set; }

    }

    public class PoolEntity : Entity, IPoolOwner
    {
        private bool m_HasDirty = true;
        public bool HasDirty()
        {
            return m_HasDirty;
        }
        public void MarkDirty()
        {
            m_HasDirty = true;
            if (m_Pool != null && m_Pool.PoolDefine.IsSubComponent)
            {
                SubEntitiesComponent subEntitiesComp = Parent as SubEntitiesComponent;
                if(subEntitiesComp != null)
                {
                    subEntitiesComp.MarkDirty();
                }
            }
        }
        public void ClearDirty()
        {
            m_HasDirty = false;
            foreach (ParamComponent paramCom in m_ParamComs.Values)
            {
                if(null != paramCom)
                {
                    if(paramCom.m_HasDirty)
                    {
                        paramCom.ClearDirty();
                    }
                }    
            }
        }
        public void ClearSelfDirty()
        {
            m_HasDirty = false;
        }
        private IDataSourceAccessor m_DataSourceAccessor;
        public IDataSourceAccessor m_DBA { get { return m_DataSourceAccessor; } }
        public void SetDB(IDataSourceAccessor dataSourceAccessor)
        {
            m_DataSourceAccessor = dataSourceAccessor;
        }

        public delegate void DelegateOnDBInit();
        public DelegateOnDBInit DBInitHandler;

        public delegate void DelegateOnSubDBInit();
        public DelegateOnSubDBInit SubDBInitHandler;
        
        public delegate void DelegateOnSyncSubCompStruct(List<SubCompStruct> rSubCompStructList);
        public DelegateOnSyncSubCompStruct AutoSyncSubCompStruct;
        
        public delegate void DelegateOnSyncInit2Self(List<PoolData> rPoolList);
        public DelegateOnSyncInit2Self AutoSyncInit2Self;

        public delegate void DelegateOnSyncInit2Other(List<PoolData> rPoolList);
        public DelegateOnSyncInit2Other AutoSyncInit2Other;


        public delegate void DelegateOnSyncParam(List<ParamData> rParamList, List<SubParamData> rSubParamList, uint paramFlag, uint syncFlag);
        public DelegateOnSyncParam AutoSyncParam;

        public delegate void DelegateOnSyncSubParam(List<SubParamData> rSubParamList, uint paramFlag, uint syncFlag);
        public DelegateOnSyncSubParam AutoSyncSubParam;

        public delegate void DelegateOnSyncParamClient(List<ParamData> rParamList);
        //public DelegateOnSyncParam AutoSyncParamClient;

        public delegate void DelegateOnSyncParam2PlayerInfo(BaseComponent baseComp);
        public DelegateOnSyncParam2PlayerInfo AutoSyncParam2PlayerInfo;

        public Dictionary<int, ParamComponent> m_ParamComs = new Dictionary<int, ParamComponent>();

        public bool IsCallBackActive = true;

        // 接收端专用,用于解析更新的属性
        public void AutoSyncInitClient(List<PoolData> rPoolList, bool bSelf)
        {
            if (m_ParamComs.Count == 0)
                return;
            
            foreach(ParamComponent paramCom in m_ParamComs.Values)
            {
                if(null != paramCom)
                {
                    paramCom.m_HasDirty = true;
                }    
            }

            if (rPoolList != null && rPoolList.Count > 0)
            {
                PoolData param = null;
                //SubParamData subParam = null;
                ParamComponent pC = null;
                for (int i = 0; i < rPoolList.Count; ++i)
                {
                    param = rPoolList[i];
                    if (!m_ParamComs.TryGetValue(param.PoolType, out pC))
                    {
                        continue;
                    }
                    if (null == pC)
                    {
                        continue;
                    }
                    pC.SyncInitClient(param, bSelf);
                    pC.m_HasDirty = false;
                }
            }

            foreach(ParamComponent paramCom in m_ParamComs.Values)
            {
                if(paramCom.m_HasDirty)
                {  
                    paramCom.SyncInitClient(null, bSelf);
                    paramCom.m_HasDirty = false;
                }
            }
        }

        public void AutoSyncParamClient(List<ParamData> rParamList)
        {
            if (rParamList == null) return;
            if (rParamList.Count == 0)
                return;
            if (m_ParamComs.Count == 0)
                return;
            // 接收端专用,用于解析更新的属性
            ParamData param = null;
            //SubParamData subParam = null;
            ParamComponent pC = null;
            for (int i = 0; i < rParamList.Count; ++i)
            {
                param = rParamList[i];
                if (!m_ParamComs.ContainsKey(param.PoolType))
                {
                    continue;
                }
                pC = m_ParamComs[param.PoolType];
                if (null == pC)
                    continue;
                pC.SyncParamClient(param);
            }
        }
        public void AutoSyncSubParamClient(List<SubParamData> rSubParamList)
        {
            if (rSubParamList == null || rSubParamList.Count == 0)
                return;
            if (m_ParamComs.Count == 0)
                return;
            // 接收端专用,用于解析更新的属性
            SubParamData subParam = null;
            ParamComponent pC = null;

            for (int i = 0; i < rSubParamList.Count; ++i)
            {
                subParam = rSubParamList[i];
                pC = m_ParamComs[subParam.ParentPoolType];
                if (null == pC)
                    continue;
                SubEntitiesComponent subComps = pC.GetComponent<SubEntitiesComponent>();
                if (null == subComps)
                    continue;
                SubEntityKey subKey = subParam.SubEntityId;
                PoolEntity sE = subComps.GetSubEntity(subKey) as PoolEntity;
                if (null == sE)
                    continue;
                ParamComponent sC = sE.m_ParamComs[subParam.PoolType];
                sC.SyncSubParamClient(subParam);
            }
        }
        public void ListSyncParamClient(int nPoolType,int nPoolIndex, int nParentPoolType, int nParentPoolIndex, SubEntityKey sSubEntityId, List<PoolListSyncData> rParamList)
        {
            if (rParamList == null) return;
            if (rParamList.Count == 0)
                return;
            if (m_ParamComs.Count == 0)
                return;
            // 接收端专用,用于解析更新的属性
            PoolListSyncData param = null;
            ParamComponent pC = null;
            ParamComponent ppC = null;
            if (nParentPoolType != 0 && nParentPoolType != -1 && nParentPoolIndex != -1)
            {
                ppC = m_ParamComs[nParentPoolType];
                SubEntitiesComponent subEntitiesComp = ppC.GetComponent<SubEntitiesComponent>();
                if (null == subEntitiesComp)
                    return;
                PoolEntity sE = subEntitiesComp.GetSubEntity(sSubEntityId);
                pC = sE.m_ParamComs[nPoolType];
            }
            else
                pC = m_ParamComs[nPoolType];
            for (int i = 0; i < rParamList.Count; ++i)
            {
                param = rParamList[i];
                if (null == pC)
                    continue;
                pC.SyncParamListClient(nPoolIndex, param);

            }
        }

        public void DicSyncParamClient(int nPoolType, int nPoolIndex, int nParentPoolType, int nParentPoolIndex, SubEntityKey sSubEntityId, List<PoolDicSyncData> rParamList)
        {
            if (rParamList == null) return;
            if (rParamList.Count == 0)
                return;
            if (m_ParamComs.Count == 0)
                return;
            // 接收端专用,用于解析更新的属性
            PoolDicSyncData param = null;
            ParamComponent pC = null;
            ParamComponent ppC = null;
            if (nParentPoolType != 0 && nParentPoolType != -1 && nParentPoolIndex != -1)
            {
                ppC = m_ParamComs[nParentPoolType];
                SubEntitiesComponent subEntitiesComp = ppC.GetComponent<SubEntitiesComponent>();
                if (null == subEntitiesComp)
                    return;
                PoolEntity sE = subEntitiesComp.GetSubEntity(sSubEntityId);
                pC = sE.m_ParamComs[nPoolType];
            }
            else
                pC = m_ParamComs[nPoolType];
            for (int i = 0; i < rParamList.Count; ++i)
            {
                param = rParamList[i];
                if (null == pC)
                    continue;
                pC.SyncParamDicClient(nPoolIndex, param);
            }
        }

        private ParamPool m_Pool;
        
        private Dictionary<int, ParamPool> m_ChildPolls = new Dictionary<int, ParamPool>();
        public int PoolType { 
            get { 
                if(m_Pool == null) return 0;
                return m_Pool.PoolType;
            } 
        }
        //PoolConfigData的ID，不唯一，比如物品可堆叠
        public int DataID {
            get {
                if (m_Pool == null) return ParamPool.INVALID_ID;
                return m_Pool.DataID; 
            } 
        }

        public ParamPool GetPool()
        {
            return m_Pool;
        }

        public void SetPool(ParamPool pool)
        {
            if (pool == null)
                return;

            m_Pool = pool;

            m_Pool.ParamChangeHandler += this.OnHandleParamChanged;
        }

        public Dictionary<int, ParamPool> GetChildPoolList()
        {
            return m_ChildPolls;
        }
        public void AddChildPool(ParamPool pool)
        {
            if (pool == null) return;
            if (m_ChildPolls.ContainsKey(pool.PoolDefine.PoolType))
            {
                Log.Error($"PoolEntity AddChildPool failed! Duplicated child pool type[{pool.PoolDefine.PoolType}] name[{pool.PoolDefine.PoolName}]");
                return;
            }
            m_ChildPolls.Add(pool.PoolDefine.PoolType, pool);
        }

        public override void Clear()
        {
            if(m_Pool != null)
            {
                m_Pool.ParamChangeHandler -= this.OnHandleParamChanged;
                ReferencePool.Release(m_Pool);
            }
            m_Pool = null;
            // ID = 0;
            m_ChildPolls.Clear();
            m_ParamComs.Clear();
            base.Clear();
            IsCallBackActive = true;
            m_HasDirty = true;
        }

        private void OnHandleParamChanged(int index, object oldVal, object newVal)
        {
            //任何的数据变化，都通知绑定的静态函数，作为一类数据变化处理
            //todo: 如果要多线程化,这里类似的代码在生成的组件分体类里也有
            PoolCallbackSystem.Instance.Call(PoolType, index, this);
        }
        
    }
}
