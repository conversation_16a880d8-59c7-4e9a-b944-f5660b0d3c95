/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableHeadFrame struct {
	// ============= 变量定义 =============
	// Id
	ID int32
	// 类型（0：永久 1：限时）
	Type int32
	// 限时过期时间戳(单位：秒)
	ExpireTime int32
	// 品质
	Quality int32
	// 名字
	Name int32
	// 头像框描述
	Des int32
	// 道具圆icon
	RoundIcon int32
	// 道具方icon(用于道具掉落展示)
	Icon int32
	// 道具资源
	Assets int32
	// 获取途径
	GetStr int32
	// 是否显示（0：显示 1：解锁后显示）
	Show int32
	// 指定日期类型（0：开服时间戳 1：指定日期）
	ShowTimeType int32
	// 指定日期显示（1、指定固定日期2、开服多久开）分钟数或指定日期
	ShowTime string
	// 掉落表id
	DropGroupId int32
	// 显示顺序
	SortOrder int32
}




// TableHeadFrameData 表格
type TableHeadFrameData struct {
	file    string
	dataMap map[int32]*TableHeadFrame
	Data    []*TableHeadFrame
	md5     string
}

// load 加载
func (tb *TableHeadFrameData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableHeadFrame{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableHeadFrame, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableHeadFrame)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableHeadFrame, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableHeadFrameData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableHeadFrame{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableHeadFrame, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableHeadFrame)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableHeadFrameData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableHeadFrameData) GetById(id int32) *TableHeadFrame {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableHeadFrameData) GetCloneById(id int32) *TableHeadFrame {
	v := tb.dataMap[id]
	out := &TableHeadFrame{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableHeadFrameData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableHeadFrameData) Foreach(call func(*TableHeadFrame) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableHeadFrameData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableHeadFrameData) Clone() ITable {
	ntb := &TableHeadFrameData{
		file:    tb.file,
		dataMap: make(map[int32]*TableHeadFrame),
		Data:    make([]*TableHeadFrame, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableHeadFrame{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
