// Code generated by rpcparse. DO NOT EDIT.

package rpcgen

import (
	"context"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/logic/player"
)

type TreasureServiceInterface interface {
	RPCHandler
	// treasurelist
	TreasureList(ctx context.Context, sender *player.Player, in *cs.CLTreasureListReq) (out *cs.LCTreasureListResp)
	// treasurelevelup
	TreasureLevelUp(ctx context.Context, sender *player.Player, in *cs.CLTreasureLevelUpReq) (out *cs.LCTreasureLevelUpResp)
	// treasurestarup
	TreasureStarUp(ctx context.Context, sender *player.Player, in *cs.CLTreasureStarUpReq) (out *cs.LCTreasureStarUpResp)
	// treasuregacha
	TreasureGacha(ctx context.Context, sender *player.Player, in *cs.CLTreasureGachaReq) (out *cs.LCTreasureGachaResp)
}
