﻿////*********************************************************
//// Framework
//// Author:  Jasen
//// Date  :  2022-11-29
////*********************************************************

//using MongoDB.Bson.Serialization.Conventions;
//using System;
//using System.Collections.Generic;
//using System.Threading.Tasks;
//using static Aurora.Framework.CoroutineLockComponent;

//namespace Aurora.Framework
//{
//    public enum CoroutineLockType
//    {
//        None = 0,
//        GameActorSender = 1,  //Game Actor 发送器使用
//        Mailbox = 2,          //Mailbox的消息队列，用于Actor消息
//        DB = 3
//    }

//    //协程Lock对象
//    public sealed class CoroutineLock : IDisposable, IReference
//    {
//        private int m_Type;
//        private long m_Key;
//        private int m_Count;

//        public int CoroutineType
//        {
//            get { return m_Type; }
//        }
//        public long Key
//        {
//            get { return m_Key; }
//        }
//        public static CoroutineLock Create(int type, long k, int count)
//        {
//            CoroutineLock coroutineLock = ReferencePool.Acquire<CoroutineLock>();
//            coroutineLock.m_Type = type;
//            coroutineLock.m_Key = k;
//            coroutineLock.m_Count = count;
//            return coroutineLock;
//        }

//        public void Clear()
//        {
//            m_Type = (int)CoroutineLockType.None;
//            m_Key = 0;
//            m_Count = 0;
//        }

//        public void Dispose()
//        {
//            CoroutineLockComponent.Instance.RunNextCoroutine(m_Type, m_Key, m_Count + 1);
//            ReferencePool.Release(this);
//        }
//    }

//    //包装TCS
//    public class WaitCoroutineLock
//    {
//        public static WaitCoroutineLock Create()
//        {
//            WaitCoroutineLock waitCoroutineLock = new WaitCoroutineLock();
//            waitCoroutineLock.m_Tcs = new ATask<CoroutineLock>();
//            return waitCoroutineLock;
//        }

//        private ATask<CoroutineLock> m_Tcs;

//        public void SetResult(CoroutineLock coroutineLock)
//        {
//            if (m_Tcs == null)
//            {
//                throw new NullReferenceException("WaitCoroutineLock SetResult tcs is null");
//            }
//            var t = m_Tcs;
//            m_Tcs = null;
//            t.SetResult(coroutineLock);
//        }

//        public void SetException(Exception exception)
//        {
//            if (m_Tcs == null)
//            {
//                throw new NullReferenceException("WaitCoroutineLock SetException tcs is null");
//            }
//            var t = m_Tcs;
//            m_Tcs = null;
//            t.SetException(exception);
//        }

//        public bool IsDisposed()
//        {
//            return m_Tcs == null;
//        }

//        public async ATask<CoroutineLock> Wait()
//        {
//            return await m_Tcs;
//        }
//    }

//    //Lock队列
//    public class CoroutineLockQueue : IReference
//    {
//        private int m_Type;
//        private long m_Key;

//        public static CoroutineLockQueue Create(int type, long key)
//        {
//            CoroutineLockQueue coroutineLockQueue = ReferencePool.Acquire<CoroutineLockQueue>();
//            coroutineLockQueue.m_Type = type;
//            coroutineLockQueue.m_Key = key;
//            return coroutineLockQueue;
//        }

//        private CoroutineLock m_CurrentCoroutineLock;

//        private readonly Queue<WaitCoroutineLock> m_Queue = new Queue<WaitCoroutineLock>();

//        public int Count
//        {
//            get
//            {
//                return m_Queue.Count;
//            }
//        }

//        public async ATask<CoroutineLock> Wait(int time)
//        {
//            if (m_CurrentCoroutineLock == null)
//            {
//                m_CurrentCoroutineLock = CoroutineLock.Create(m_Type, m_Key, 1);
//                return m_CurrentCoroutineLock;
//            }

//            WaitCoroutineLock waitCoroutineLock = WaitCoroutineLock.Create();
//            m_Queue.Enqueue(waitCoroutineLock);
//            if (time > 0)
//            {
//                long tillTime = ATimer.Instance.SystemTicks + time;
//                //TimerComponent.Instance.AddOnceTimerObject<WaitCoroutineLock>(tillTime, OnCoroutineLockTimer, waitCoroutineLock);
//            }
//            if (m_Queue.Count > 100)
//            {
//                //单个队列太多，记录一下
//                Log.Warning($"CoroutineLockQueue m_Queue too much coroutine count: {m_Type} {m_Key} {m_Queue.Count}");
//            }
//            m_CurrentCoroutineLock = await waitCoroutineLock.Wait();
//            return m_CurrentCoroutineLock;
//        }

//        //超时处理
//        public static void OnCoroutineLockTimer(WaitCoroutineLock waitCoroutineLock)
//        {
//            if (waitCoroutineLock.IsDisposed())
//            {
//                return;
//            }
//            waitCoroutineLock.SetException(new FrameworkException($"coroutine is timeout"));
//        }

//        public void Notify(int count)
//        {
//            // 有可能WaitCoroutineLock已经超时抛出异常，所以要找到一个未处理的WaitCoroutineLock
//            while (m_Queue.Count > 0)
//            {
//                WaitCoroutineLock waitCoroutineLock = m_Queue.Dequeue();

//                if (waitCoroutineLock.IsDisposed())
//                {
//                    continue;
//                }

//                CoroutineLock coroutineLock = CoroutineLock.Create(m_Type, m_Key, count);

//                waitCoroutineLock.SetResult(coroutineLock);
//                break;
//            }
//        }

//        public void Clear()
//        {
//            m_Queue.Clear();
//            m_Key = 0;
//            m_Type = 0;
//            m_CurrentCoroutineLock = null;

//        }
//        public void Release()
//        {
//            ReferencePool.Release(this);
//        }
//    }

//    public class CoroutineLockQueueType
//    {
//        private readonly int m_Type;

//        private readonly Dictionary<long, CoroutineLockQueue> m_CoroutineLockQueues = new Dictionary<long, CoroutineLockQueue>();

//        public CoroutineLockQueueType(int type)
//        {
//            m_Type = type;
//        }

//        private CoroutineLockQueue Get(long key)
//        {
//            m_CoroutineLockQueues.TryGetValue(key, out CoroutineLockQueue queue);
//            return queue;
//        }

//        private CoroutineLockQueue New(long key)
//        {
//            CoroutineLockQueue queue = CoroutineLockQueue.Create(m_Type, key);
//            m_CoroutineLockQueues.Add(key, queue);
//            return queue;
//        }

//        private void Remove(long key)
//        {
//            if (m_CoroutineLockQueues.Remove(key, out CoroutineLockQueue queue))
//            {
//                queue.Release();
//            }
//        }

//        public async ATask<CoroutineLock> Wait(long key, int time)
//        {
//            CoroutineLockQueue queue = this.Get(key) ?? this.New(key);
//            return await queue.Wait(time);
//        }

//        public void Notify(long key, int count)
//        {
//            CoroutineLockQueue queue = this.Get(key);
//            if (queue == null)
//            {
//                return;
//            }

//            if (queue.Count == 0)
//            {
//                this.Remove(key);
//                return;
//            }

//            queue.Notify(count);
//        }
//    }

//    public class CoroutineLockComponent : BaseComponent, IAwake, IUpdate
//    {
//        public static CoroutineLockComponent Instance { get; set; }
//        public Dictionary<int, CoroutineLockQueueType> TypeCoroutineDic;
//        public readonly Queue<QueueInfo> m_NextRunInfo = new Queue<QueueInfo>();

//        public struct QueueInfo
//        {
//            public int Type;
//            public int Count;
//            public long Key;
//        }
//    }

//    [ComponentSystem(typeof(CoroutineLockComponent))]
//    public static class CoroutineLockComponentSystem
//    {
//        [MethodAwake]
//        public static void OnAwake(CoroutineLockComponent self)
//        {
//            CoroutineLockComponent.Instance = self;
//            self.TypeCoroutineDic = new Dictionary<int, CoroutineLockQueueType>();
//        }

//        [MethodUpdate]
//        public static void OnUpdate(CoroutineLockComponent self)
//        {
//            while (self.m_NextRunInfo.Count > 0)
//            {
//                QueueInfo info = self.m_NextRunInfo.Dequeue();
//                self.Notify(info.Type, info.Key, info.Count);
//            }
//        }

//        private static void Notify(this CoroutineLockComponent self, int lockType, long key, int count)
//        {
//            CoroutineLockQueueType coroutineLockQueueType = self.TypeCoroutineDic[lockType];
//            if (coroutineLockQueueType == null)
//            {
//                return;
//            }
//            coroutineLockQueueType.Notify(key, count);
//        }
//        public static void RunNextCoroutine(this CoroutineLockComponent self, int lockType, long key, int count)
//        {
//            // 一个协程队列处理超过100个,说明比较多了,打个warning,检查一下是否够正常
//            if (count >= 100)
//            {
//                Log.Warning($"too much coroutine count: {lockType} {key} {count}");
//            }
//            self.m_NextRunInfo.Enqueue(new QueueInfo() { Type = lockType, Key = key, Count = count });
//        }
//        public static async ATask<CoroutineLock> Wait(this CoroutineLockComponent self, int lockType, long id, int time = 60000)
//        {
//            CoroutineLockQueueType coroutineLockQueueType = null;
//            if (!self.TypeCoroutineDic.TryGetValue(lockType, out coroutineLockQueueType))
//            {
//                coroutineLockQueueType = new CoroutineLockQueueType(lockType);
//                self.TypeCoroutineDic[lockType] = coroutineLockQueueType;
//            }

//            return await coroutineLockQueueType.Wait(id, time);
//        }
//    }
//}
