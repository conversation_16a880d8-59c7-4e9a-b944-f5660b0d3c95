// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v4.23.2
// source: BattleService.proto

package natsrpc

import (
	context "context"
)

// BattleServiceClient is the client API for BattleService service.
type BattleServiceClient interface {
	CreateBattle(ctx context.Context, req *CreateBattleReq) (*CreateBattleResp, error)
	AsyncCreateBattle(ctx context.Context, req *CreateBattleReq, cb func(*CreateBattleResp, error))
	EnterBattle(ctx context.Context, req *EnterBattleReq, serverId string) (*EnterBattleResp, error)
	AsyncEnterBattle(ctx context.Context, req *EnterBattleReq, serverId string, cb func(*EnterBattleResp, error))
	SelectBuffer(ctx context.Context, req *SelectBufferReq, serverId string) (*SelectBufferResp, error)
	AsyncSelectBuffer(ctx context.Context, req *SelectBufferReq, serverId string, cb func(*SelectBufferResp, error))
	MergeHero(ctx context.Context, req *MergeHeroReq, serverId string) (*MergeHeroResp, error)
	AsyncMergeHero(ctx context.Context, req *MergeHeroReq, serverId string, cb func(*MergeHeroResp, error))
	BattleReady(ctx context.Context, req *ReadyBattleReq, serverId string) (*ReadyBattleResp, error)
	AsyncBattleReady(ctx context.Context, req *ReadyBattleReq, serverId string, cb func(*ReadyBattleResp, error))
	EndBattle(ctx context.Context, req *EndBattleReq, serverId string) (*EndBattleResp, error)
	AsyncEndBattle(ctx context.Context, req *EndBattleReq, serverId string, cb func(*EndBattleResp, error))
	LeaveBattle(ctx context.Context, req *LeaveBattleReq, serverId string) (*LeaveBattleResp, error)
	AsyncLeaveBattle(ctx context.Context, req *LeaveBattleReq, serverId string, cb func(*LeaveBattleResp, error))
}

// BattleServiceServer is the server API for BattleService service.
type BattleServiceServer interface {
	CreateBattle(context.Context, *CreateBattleReq) (*CreateBattleResp, error)
	EnterBattle(context.Context, *EnterBattleReq) (*EnterBattleResp, error)
	SelectBuffer(context.Context, *SelectBufferReq) (*SelectBufferResp, error)
	MergeHero(context.Context, *MergeHeroReq) (*MergeHeroResp, error)
	BattleReady(context.Context, *ReadyBattleReq) (*ReadyBattleResp, error)
	EndBattle(context.Context, *EndBattleReq) (*EndBattleResp, error)
	LeaveBattle(context.Context, *LeaveBattleReq) (*LeaveBattleResp, error)
}
