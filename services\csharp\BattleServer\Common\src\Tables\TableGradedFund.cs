#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGradedFund
	{

		public static readonly string TName="GradedFund.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 下阶段基金 
		/// </summary> 
		public int NextId {get; set;}
		/// <summary> 
		/// 超值道具消耗id 
		/// </summary> 
		public int ExpendItemId {get; set;}
		/// <summary> 
		/// 超值道具消耗num 
		/// </summary> 
		public int ExpendItemNum {get; set;}
		#endregion

		public static TableGradedFund GetData(int ID)
		{
			return TableManager.GradedFundData.Get(ID);
		}

		public static List<TableGradedFund> GetAllData()
		{
			return TableManager.GradedFundData.GetAll();
		}

	}
	public sealed partial class TableGradedFundData
	{
		private Dictionary<int, TableGradedFund> dict = new Dictionary<int, TableGradedFund>();
		private List<TableGradedFund> dataList = new List<TableGradedFund>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGradedFund.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGradedFund>>(jsonContent);
			foreach (TableGradedFund config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGradedFund Get(int id)
		{
			if (dict.TryGetValue(id, out TableGradedFund item))
				return item;
			return null;
		}

		public List<TableGradedFund> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
