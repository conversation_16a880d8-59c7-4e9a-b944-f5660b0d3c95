// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v4.23.2
// source: GameService.proto

package natsrpc

type gameserviceClientWrap struct {
	GameServiceClient
}

func NewGameserviceClientWrap(gameserviceclient GameServiceClient) *gameserviceClientWrap {
	return &gameserviceClientWrap{
		GameServiceClient: gameserviceclient,
	}
}
