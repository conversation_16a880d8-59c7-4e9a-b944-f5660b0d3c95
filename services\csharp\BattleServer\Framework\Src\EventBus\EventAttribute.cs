﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************

using System;

namespace Aurora.Framework
{
    // 非异步型事件处理函数标签
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class EventInvokerAttribute : AuroraAttribute
    {
        public Type ParamType { get; }
        public EventInvokerAttribute(Type paramType)
        {
            ParamType = paramType;
        }
    }
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class EventDynamicListenerAttribute : AuroraAttribute
    {
        public Type ParamType { get; }
        public EventDynamicListenerAttribute(Type paramType)
        {
            ParamType = paramType;
        }
    }
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = true)]
    public sealed class EventAttribute : AuroraAttribute
    {
        public long EventId { get; }
        public EventAttribute(long eventId)
        {
            EventId = eventId;
        }
    }
}
