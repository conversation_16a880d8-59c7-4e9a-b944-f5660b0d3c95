#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityGame
	{

		public static readonly string TName="ActivityGame.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 游戏币ID 
		/// </summary> 
		public int GameCoinId {get; set;}
		/// <summary> 
		/// 道具显示ID1 
		/// </summary> 
		public int PlayDropGroupId {get; set;}
		/// <summary> 
		/// 道具显示ID2 
		/// </summary> 
		public int PlayDropGroupId2 {get; set;}
		/// <summary> 
		/// 游戏一次的消耗 
		/// </summary> 
		public int PlayCost {get; set;}
		/// <summary> 
		/// 时间掉落ID 
		/// </summary> 
		public int[] TimeDropGroupId {get; set;}
		/// <summary> 
		/// 时间掉落间隔 
		/// </summary> 
		public int[] TimeDropLimit {get; set;}
		/// <summary> 
		/// 阶段奖励 
		/// </summary> 
		public int[] StageRewardExp {get; set;}
		/// <summary> 
		/// 阶段奖励 
		/// </summary> 
		public int[] StageRewardDropGroupId {get; set;}
		/// <summary> 
		/// 扩展参数（倍率） 
		/// </summary> 
		public int[] para1 {get; set;}
		/// <summary> 
		/// 扩展参数（倍率概率） 
		/// </summary> 
		public int[] para2 {get; set;}
		/// <summary> 
		/// 道具显示ID1 
		/// </summary> 
		public int FirstPlayDropGroupId {get; set;}
		/// <summary> 
		/// 道具显示ID2 
		/// </summary> 
		public int FirstPlayDropGroupId2 {get; set;}
		#endregion

		public static TableActivityGame GetData(int ID)
		{
			return TableManager.ActivityGameData.Get(ID);
		}

		public static List<TableActivityGame> GetAllData()
		{
			return TableManager.ActivityGameData.GetAll();
		}

	}
	public sealed partial class TableActivityGameData
	{
		private Dictionary<int, TableActivityGame> dict = new Dictionary<int, TableActivityGame>();
		private List<TableActivityGame> dataList = new List<TableActivityGame>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityGame.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityGame>>(jsonContent);
			foreach (TableActivityGame config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityGame Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityGame item))
				return item;
			return null;
		}

		public List<TableActivityGame> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
