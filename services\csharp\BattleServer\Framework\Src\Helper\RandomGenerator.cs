﻿using System;
using System.Collections.Generic;
using System.Linq;
using Random = System.Random;

namespace Aurora.Framework
{
    // 支持多线程
    public static class RandomGenerator
    {
        [StaticField]
        [ThreadStatic]
        private static Random random;

        public static Random GetRandom()
        {
            return random ??= new Random(Guid.NewGuid().GetHashCode());
        }

        public static ulong RandUInt64()
        {
            int r1 = RandInt32();
            int r2 = RandInt32();
            
            return ((ulong)r1 << 32) & (ulong)r2;
        }

        public static int RandInt32()
        {
            return GetRandom().Next();
        }

        public static uint RandUInt32()
        {
            return (uint) GetRandom().Next();
        }

        public static long RandInt64()
        {
            uint r1 = RandUInt32();
            uint r2 = RandUInt32();
            return (long)(((ulong)r1 << 32) | r2);
        }

        /// <summary>
        /// 获取lower与Upper之间的随机数,包含下限，不包含上限
        /// </summary>
        /// <param name="lower"></param>
        /// <param name="upper"></param>
        /// <returns></returns>
        public static int RandomNumber(int lower, int upper) // in [min,max)
        {
            int value = GetRandom().Next(lower, upper);
            return value;
        }

        /// <summary>
        /// 获取lower与Upper之间的随机数,包含下限,上限
        /// </summary>
        /// <param name="lower"></param>
        /// <param name="upper"></param>
        /// <returns></returns>
        public static int RandomNumberInclude(int lower, int upper) // in [min, max]
        {
            int value = RandomNumber(lower, upper + 1);
            return value;
        }

        /// <summary>
        /// 获取lower与Upper之间的随机数,包含下限，不包含上限
        /// </summary>
        /// <param name="lower"></param>
        /// <param name="upper"></param>
        /// <returns></returns>
        public static float Random(float lower, float upper) // in [min,max)
        {
            float valude = lower + (float)GetRandom().NextDouble() * (upper - lower); // NextSingle返回0.0-1.0的随机小数;
            return valude;
        }
        /// <summary>
        /// 获取lower与Upper之间的随机数,包含下限，上限
        /// </summary>
        /// <param name="lower"></param>
        /// <param name="upper"></param>
        /// <returns></returns>
        public static float RandomInclude(float lower, float upper) // in [min, max]
        {
            // 生成一个额外的微小值，确保上限也可取到
            float adjustment = (upper - lower) * 0.0000001f;
            return lower + (float)GetRandom().NextDouble() * (upper - lower + adjustment);
        }

        /// <summary>
        /// 获取lower与Upper之间的随机数,包含下限，不包含上限
        /// </summary>
        /// <param name="lower"></param>
        /// <param name="upper"></param>
        /// <returns></returns>
        public static double Random(double lower, double upper) // in [min,max)
        {
            double valude = lower + GetRandom().NextDouble() * (upper - lower); // NextDouble返回0.0-1.0的随机小数;
            return valude;
        }
        /// <summary>
        /// 获取lower与Upper之间的随机数,包含下限，上限
        /// </summary>
        /// <param name="lower"></param>
        /// <param name="upper"></param>
        /// <returns></returns>
        public static double RandomInclude(double lower, double upper) // in [min, max]
        {
            // 生成一个额外的微小值，确保上限也可取到
            double adjustment = (upper - lower) * 0.0000001;
            return lower + GetRandom().NextDouble() * (upper - lower + adjustment);
        }

        /// <summary>
        /// 根据随机权重返回索引
        /// </summary>
        /// <param name="arr"></param>
        /// <returns></returns>
        public static int randomEx(int[] arr)
        {
            if (arr == null) return 0;
            int totalWeight = 0;
            foreach (var weight in arr)
            {
                totalWeight += weight;
            }
            int randomWeight = GetRandom().Next(0, totalWeight);
            int index = 0;

            for (int i = 0; i < arr.Length; i++)
            {
                if (randomWeight < arr[i])
                {
                    index = i;
                    break;
                }

                randomWeight -= arr[i];
            }
            return index;
        }
        /// <summary>
        /// 根据随机权重返回索引
        /// </summary>
        /// <param name="arr"></param>
        /// <returns></returns>
        public static int randomEx(List<int> arr)
        {
            if (arr == null) return 0;
            int totalWeight = 0;
            foreach (var weight in arr)
            {
                totalWeight += weight;
            }

            int randomWeight = GetRandom().Next(0, totalWeight);
            int index = 0;

            for (int i = 0; i < arr.Count; i++)
            {
                if (randomWeight < arr[i])
                {
                    index = i;
                    break;
                }

                randomWeight -= arr[i];
            }

            return index;
        }

        /// <summary>
        /// 根据随机权重返回索引
        /// </summary>
        /// <param name="arr"></param>
        /// <returns></returns>
        public static int randomEx(float[] arr)
        {
            if (arr == null) return 0;
            float totalWeight = 0;
            foreach (var weight in arr)
            {
                totalWeight += weight;
            }

            float randomWeight = (float)GetRandom().NextDouble() * totalWeight;
            int index = 0;

            for (int i = 0; i < arr.Length; i++)
            {
                if (randomWeight < arr[i])
                {
                    index = i;
                    break;
                }

                randomWeight -= arr[i];
            }

            return index;
        }

        public static bool RandomBool()
        {
            return GetRandom().Next(2) == 0;
        }

        public static T RandomArray<T>(T[] array)
        {
            if (array == null) return default(T);
            return array[RandomNumber(0, array.Length)];
        }

        public static T RandomArray<T>(List<T> array)
        {
            if (array == null) return default(T);
            return array[RandomNumber(0, array.Count)];
        }
        public static K RandomDic<K, V>(Dictionary<K, V> dic, Random r = null)
        {
            if (dic == null) return default(K);
            if (dic.Count == 0)
            {
                return default;
            }
            if (r == null)
            {
                r = GetRandom();
            }
            int index = r.Next(0, dic.Count);

            return dic.ElementAt(index).Key;

        }
        public static List<T> RandomListRange<T>(List<T> list,int countToSelect = -1, Random r = null)
        {
            var tmpList = new List<T>(list);
            if (r == null)
                r = GetRandom();
            int N = tmpList.Count;

            // 随机选择抓取的球的数量，0 到 N 之间
            if (countToSelect == -1)
                countToSelect = r.Next(N + 1); // 随机选择一个数量，可能是 0 到 N

            // 打乱数组以模拟随机抓取
            for (int i = 0; i < N; i++)
            {
                int j = r.Next(i, N);
                (tmpList[i], tmpList[j]) = (tmpList[j], tmpList[i]);
            }

            // 选择前 countToSelect 个元素
            return tmpList.GetRange(0, countToSelect);
        }


        /// <summary>
        /// 根据权重返回x个下标
        /// </summary>
        /// <param name="weights">权重list</param>
        /// <param name="count">返回数量</param>
        public static List<int> RandomListByWeight(List<int> weights, int count = 1)
        {
            if (weights == null || weights.Count < count)
            {
                Log.Error("权重数组长度不足或为空");
            }

            Random random = new Random();
            List<int> selectedIndices = new List<int>();
            // 复制权重和索引，避免修改原始数据
            List<(int Index, int Weight)> weightedList = weights
                .Select((w, i) => (i, w))
                .ToList();

            for (int n = 0; n < count; n++)
            {
                // 计算当前剩余权重总和
                int totalWeight = weightedList.Sum(item => item.Weight);
                // 生成 [0, totalWeight) 的随机数
                int randomValue = random.Next(totalWeight);
                int cumulativeWeight = 0;
                int selectedIndex = -1;

                // 遍历找到随机数落在的权重区间
                for (int i = 0; i < weightedList.Count; i++)
                {
                    cumulativeWeight += weightedList[i].Weight;
                    if (randomValue < cumulativeWeight)
                    {
                        selectedIndex = i;
                        break;
                    }
                }

                if (selectedIndex == -1) selectedIndex = weightedList.Count - 1;
                // 记录选中的原始索引
                selectedIndices.Add(weightedList[selectedIndex].Index);
                // 移除已选项，避免重复
                weightedList.RemoveAt(selectedIndex);
            }

            return selectedIndices;
        }

        /// <summary>
        /// 打乱数组
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="arr">要打乱的数组</param>
        public static void BreakRank<T>(List<T> arr)
        {
            if (arr == null || arr.Count < 2)
            {
                return;
            }

            for (int i = 0; i < arr.Count; i++)
            {
                int index = GetRandom().Next(0, arr.Count);
                (arr[index], arr[i]) = (arr[i], arr[index]);
            }
        }

        public static float RandFloat01()
        {
            int a = RandomNumber(0, 1000000);
            return a / 1000000f;
        }

    }
}