/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableItem struct {
	// ============= 变量定义 =============
	// 道具ID
	ID int32
	// 道具名称(对应language表)
	NameID int32
	// 道具描述
	Desc int32
	// 道具Icon
	Icon string
	// 道具类型
	Type int32
	// 子类型
	SubType int32
	// 道具品质
	Qulity int32
	// 优先级
	SortOrder int32
	// 道具售价
	Price []int32
	// 是否在背包展示
	ShowPacket int32
	// 等级限制
	LevelLimit int32
	// 道具操作列表
	OptList []int32
	// 批量使用上限
	BatchUseMaxCount int32
	// 获取途径
	FromFunctionList []int32
	// 道具参数A
	NumParams []int32
	// 道具参数B
	StrParams []string
	// 是否使用：用于GM后台邮件(0:未使用1:使用)
	IsUse int32
	// 最大可叠加数量
	Stacking int32
	// 来源(0：商城1：等级基金2：七日签到3：充值返利)
	Source int32
}




// TableItemData 表格
type TableItemData struct {
	file    string
	dataMap map[int32]*TableItem
	Data    []*TableItem
	md5     string
}

// load 加载
func (tb *TableItemData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableItem{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableItem, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableItem)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableItem, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableItemData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableItem{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableItem, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableItem)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableItemData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableItemData) GetById(id int32) *TableItem {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableItemData) GetCloneById(id int32) *TableItem {
	v := tb.dataMap[id]
	out := &TableItem{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableItemData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableItemData) Foreach(call func(*TableItem) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableItemData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableItemData) Clone() ITable {
	ntb := &TableItemData{
		file:    tb.file,
		dataMap: make(map[int32]*TableItem),
		Data:    make([]*TableItem, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableItem{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
