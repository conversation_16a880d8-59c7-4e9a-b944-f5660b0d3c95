﻿//*********************************************************
// Framework
// Author:  Jasen 
// Date  :  2022-10-25
//*********************************************************

using System;
using System.Collections.Generic;

namespace Aurora.Framework
{
    public delegate List<T> ObjectPoolFilterCallback<T>(List<T> toReleaseObjects, int releaseCount, DateTime expireTime) where T : SpawnObject;

    //一个用作操作接口模板类
    public interface IObjectPool<T> where T : SpawnObject
    {
        int Count
        {
            get;
        }
        Type ObjectType
        {
            get;
        }
        int CanReleaseCount
        {
            get;
        }
        bool AllowMultiSpawn
        {
            get;
        }
        int Capacity
        {
            get;
            set;
        }

        /// 获取或设置对象池对象过期秒数。
        float ExpireTime
        {
            get;
            set;
        }

        void Register(T spawnObject);
        T Spawn();
        T Spawn(string name);
        void Unspawn(T spawnObject);
        void Unspawn(object target);
        void SetLocked(T spawnObject, bool locked);
        void SetLocked(object target, bool locked);
        bool ReleaseObject(T spawnObject);
        bool ReleaseObject(object target);
        void Release();
        void Release(int toReleaseCount);
        void ReleaseUnused();
        void Update(float realElapseSeconds);
        void Shutdown();
        void SetFilterCallback(ObjectPoolFilterCallback<T> callback);
    }

    //一个用于基类，作为非模板类容器存贮
    public abstract class ObjectPoolInternal
    {
        public abstract int CanReleaseCount
        {
            get;
        }
        public abstract bool AllowMultiSpawn
        {
            get;
        }
        public abstract int Capacity
        {
            get;
            set;
        }

        //获取或设置对象池对象过期秒数。
        public abstract float ExpireTime
        {
            get;
            set;
        }
        public abstract void Update(float realElapseSeconds);
        public abstract void Release();
        public abstract void Shutdown();
        public abstract void ReleaseUnused();
    }
}

