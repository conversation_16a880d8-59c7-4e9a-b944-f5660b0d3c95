/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableDropBox struct {
	// ============= 变量定义 =============
	// 掉落包ID
	ID int32
	// 掉落规则(每个道具池改为权重模式，从这个池中随机出 指定数量的道具)，后2列同时有效，不配置默认只掉落1个。掉落多个时受后2列影响，后面的每个道具池都是按此规则处理。此列不再使用，固定为0模式。
	DropOpt int32
	// 每个道具池最大掉落数量
	DropNum int32
	// 每个道具池掉落是否可重复(0不可 1可以)
	IsCanDuplicate int32
	// 道具池1，二维数组格式，配置多个元素格式为：[[掉落类型，ID（公式类型或道具ID），掉落权重（万分比），掉落数量基础值，掉落数量参数1(-1：表示受局内等级影响)，掉落数量参数2],[…],[…]]，目前二维数组统一使用string格式，后续导表工具优化后再修改为数组类型。
	DropItem1 string
	// 掉落道具2
	DropItem2 string
	// 掉落道具3
	DropItem3 string
	// 掉落道具4
	DropItem4 string
	// 掉落道具5
	DropItem5 string
	// 掉落道具6
	DropItem6 string
	// 掉落道具7
	DropItem7 string
	// 掉落道具8
	DropItem8 string
	// 掉落道具9
	DropItem9 string
	// 掉落道具10
	DropItem10 string
}




// TableDropBoxData 表格
type TableDropBoxData struct {
	file    string
	dataMap map[int32]*TableDropBox
	Data    []*TableDropBox
	md5     string
}

// load 加载
func (tb *TableDropBoxData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableDropBox{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableDropBox, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableDropBox)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableDropBox, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableDropBoxData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableDropBox{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableDropBox, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableDropBox)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableDropBoxData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableDropBoxData) GetById(id int32) *TableDropBox {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableDropBoxData) GetCloneById(id int32) *TableDropBox {
	v := tb.dataMap[id]
	out := &TableDropBox{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableDropBoxData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableDropBoxData) Foreach(call func(*TableDropBox) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableDropBoxData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableDropBoxData) Clone() ITable {
	ntb := &TableDropBoxData{
		file:    tb.file,
		dataMap: make(map[int32]*TableDropBox),
		Data:    make([]*TableDropBox, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableDropBox{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
