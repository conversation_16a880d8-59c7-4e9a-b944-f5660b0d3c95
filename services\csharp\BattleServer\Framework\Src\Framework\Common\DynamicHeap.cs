﻿using System.Collections.Generic;

namespace Aurora.Framework
{
    public class DynamicHeap
    {
        private bool _minRoot;

        private struct Element
        {
            public long Key;
            public long Value;
        }
        private List<Element> _heap = new List<Element>();

        private Dictionary<long, int> _indexMap = new Dictionary<long, int>();

        public int Count
        {
            get
            {
                return _heap.Count;
            }
        }

        public void Init(bool minRoot)
        {
            Reset();

            _minRoot = minRoot;
        }

        public void Reset()
        {
            Clear();

            _minRoot = false;
        }

        public void Clear()
        {
            _indexMap.Clear();
            _heap.Clear();
        }

        public void BuildKeyList(ref List<long> keyList)
        {
            keyList.Clear();

            foreach (Element element in _heap)
            {
                keyList.Add(element.Key);
            }
        }

        public bool Get(long key, out long value)
        {
            value = 0;

            if (_indexMap.TryGetValue(key, out int index))
            {
                value = _heap[index].Value;

                return true;
            }

            return false;
        }

        public bool Peek(out long key, out long value)
        {
            key = 0;
            value = 0;

            if (_heap.Count > 0)
            {
                key = _heap[0].Key;
                value = _heap[0].Value;

                return true;
            }

            return false;
        }

        public bool Pop(out long key, out long value)
        {
            key = 0;
            value = 0;

            if (_heap.Count > 0)
            {
                key = _heap[0].Key;
                value = _heap[0].Value;

                Delete(0);

                return true;
            }

            return false;
        }

        public bool Add(long key, long value)
        {
            if (!_indexMap.TryGetValue(key, out int index))
            {
                Create(key, value);

                return true;
            }

            return false;
        }

        public bool Set(long key, long value)
        {
            if (_indexMap.TryGetValue(key, out int index))
            {
                Update(index, value);

                return true;
            }

            return false;
        }

        public bool Remove(long key)
        {
            if (_indexMap.TryGetValue(key, out int index))
            {
                Delete(index);

                return true;
            }

            return false;
        }

        private void Create(long key, long value)
        {
            Element element = new Element();
            {
                element.Key = key;
                element.Value = value;
            }
            _heap.Add(element);
            int lastIndex = _heap.Count - 1;
            _indexMap.Add(key, lastIndex);

            PopUp(lastIndex);
        }

        private void Update(int index, long value)
        {
            Element element = _heap[index];
            element.Value = value;
            _heap[index] = element;

            if (PopUp(index) == index)
            {
                PushDown(index);
            }
        }

        private void Delete(int index)
        {
            int lastIndex = _heap.Count - 1;

            if (index != lastIndex)
            {
                Swap(index, lastIndex);
            }

            _indexMap.Remove(_heap[lastIndex].Key);
            _heap.RemoveAt(lastIndex);

            if (index != lastIndex)
            {
                if (PopUp(index) == index)
                {
                    PushDown(index);
                }
            }
        }

        private int PopUp(int index)
        {
            int parentIndex = (index + 1) / 2 - 1;
            if (parentIndex >= 0)
            {
                if (_minRoot)
                {
                    if (_heap[index].Value < _heap[parentIndex].Value)
                    {
                        Swap(index, parentIndex);

                        return PopUp(parentIndex);
                    }
                }
                else
                {
                    if (_heap[index].Value > _heap[parentIndex].Value)
                    {
                        Swap(index, parentIndex);

                        return PopUp(parentIndex);
                    }
                }
            }

            return index;
        }

        private int PushDown(int index)
        {
            int leftChildIndex = (index + 1) * 2 - 1;
            int rightChildIndex = (index + 1) * 2;
            if (leftChildIndex < _heap.Count)
            {
                if (_minRoot)
                {
                    int minChildIndex = leftChildIndex;
                    if (rightChildIndex < _heap.Count)
                    {
                        if (_heap[rightChildIndex].Value < _heap[leftChildIndex].Value)
                        {
                            minChildIndex = rightChildIndex;
                        }
                    }

                    if (_heap[index].Value > _heap[minChildIndex].Value)
                    {
                        Swap(index, minChildIndex);

                        return PushDown(minChildIndex);
                    }
                }
                else
                {
                    int maxChildIndex = leftChildIndex;
                    if (rightChildIndex < _heap.Count)
                    {
                        if (_heap[rightChildIndex].Value > _heap[leftChildIndex].Value)
                        {
                            maxChildIndex = rightChildIndex;
                        }
                    }

                    if (_heap[index].Value < _heap[maxChildIndex].Value)
                    {
                        Swap(index, maxChildIndex);

                        return PushDown(maxChildIndex);
                    }
                }
            }

            return index;
        }

        private void Swap(int index1, int index2)
        {
            if (index1 != index2)
            {
                Element tempElement = _heap[index1];
                _heap[index1] = _heap[index2];
                _heap[index2] = tempElement;

                _indexMap[_heap[index1].Key] = index1;
                _indexMap[_heap[index2].Key] = index2;
            }
        }
    }
}
