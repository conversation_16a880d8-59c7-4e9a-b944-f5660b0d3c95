﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Game.Core
{
    public struct LevelSkill
    {
        public int Level;
        public int StartCount;
        public int SkillId;
    }
    public struct CampSynergy
    {
        public int Camp;
        public int HeroCount;
        public List<LevelSkill> LevelSkills;

        public CampSynergy()
        {
            LevelSkills = new List<LevelSkill>();
        }
    }
    public sealed partial class TableSynergy
    {
    }


    public sealed partial class TableSynergyData
    {
        private List<CampSynergy> campSynergy = new List<CampSynergy>();
        public void InitHelper()
        {
            foreach (var tableSynergy in dataList)
            {
                if (tableSynergy.Stage != 2)
                    continue;

                if (tableSynergy.SkillList == null)
                    continue;

                //string str = tableSynergy.Param;
                //string[] strs = str.Split(',');
                var numberLists = JsonConvert.DeserializeObject<List<int>>(tableSynergy.Param);

                CampSynergy synergy = new CampSynergy();
                synergy.Camp = numberLists[0];
                synergy.HeroCount = numberLists[1];
                
                for (int i = 0; i < 3; i++)
                {
                    LevelSkill levelSkill = new LevelSkill();
                    levelSkill.Level = i+1;
                    levelSkill.StartCount = numberLists[i+2];
                    levelSkill.SkillId = tableSynergy.SkillList[i];
                    synergy.LevelSkills.Add(levelSkill);
                }

                campSynergy.Add(synergy);
            }
        }

        public List<CampSynergy> GetSynergy()
        {
            return campSynergy;
        }
    }
}
