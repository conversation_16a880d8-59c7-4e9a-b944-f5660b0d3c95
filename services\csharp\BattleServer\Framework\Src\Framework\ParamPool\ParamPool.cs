﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-2-20
//*********************************************************


using System;
using System.Buffers;
using System.Collections;

namespace Aurora.Framework
{

    public class ParamPool : IReference
    {
        public delegate void HandleParamChange(int index, object oldVal, object newVal);
        public HandleParamChange ParamChangeHandler;

        public static readonly int INVALID_ID = -1;
        
        public const float EPSILON = 1.1920928955078125e-7f;
        
        public const double EPSILON_DBL = 2.22044604925031308085e-16;
        
        private PoolDefine m_PoolDefine;
        public PoolDefine PoolDefine
        {
            get { return m_PoolDefine; }
        }
        //数据Buff
        private byte[] m_Data;
        //标脏
        private BitArray  m_DirtyBits;
        private bool m_HasDirty = false;
        //属性的配置表ID，如果没有-1,
        private int m_DataID;
        public int PoolType
        {
            get { return m_PoolDefine.PoolType; }
        }
        public int DataID
        {
            get { return m_DataID; }
        }
        public void Init(PoolDefine poolDefine)
        {
            m_PoolDefine = poolDefine;
            m_DirtyBits = new BitArray(m_PoolDefine.MaxIndex+1);
            //m_Data = ArrayPool<byte>.Shared.Rent(m_PoolDefine.PoolSize);//new byte[m_PoolDefine.PoolSize];
            m_DataID = INVALID_ID;
        }

        public void CopyFrom(PoolConfigData configData)
        {
            if (configData == null)
                return;

            if (configData.DataBuff.Length != m_PoolDefine.PoolSize)
            {
                Log.Error("param pool init by buff,but buff length is not equal!");
                return;
            }
            m_DataID = configData.DataID;
            Buffer.BlockCopy(configData.DataBuff, 0, m_Data, 0, configData.DataBuff.Length);
        }


        #region 通过Param下标访问
        public void SetValue(int index, byte value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            byte old = param.GetValue(m_Data,(byte)0);
            if(old == value) return;
            param.SetValue(m_Data, value);
            if(dirty)
            {
                m_DirtyBits[index] = true;  
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public byte GetValue(int index, byte def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }
        public void SetValue(int index, sbyte value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            sbyte old = param.GetValue(m_Data, (sbyte)0);
            if(old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public sbyte GetValue(int index, sbyte def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index, short value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            short old = param.GetValue(m_Data, (short)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public short GetValue(int index, short def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index, ushort value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            ushort old = param.GetValue(m_Data, (ushort)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public ushort GetValue(int index, ushort def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index,int value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            int old = param.GetValue(m_Data, (int)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public int GetValue(int index, int def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index, uint value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            uint old = param.GetValue(m_Data, (uint)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public uint GetValue(int index, uint def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index, long value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            long old = param.GetValue(m_Data, (long)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public long GetValue(int index, long def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index, ulong value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            ulong old = param.GetValue(m_Data, (ulong)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public ulong GetValue(int index, ulong def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index, bool value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            bool old = param.GetValue(m_Data, false);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public bool GetValue(int index, bool def = false)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }


        public void SetValue(int index, float value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            float old = param.GetValue(m_Data, (float)0);
            if (Math.Abs(old - value) < EPSILON) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public float GetValue(int index, float def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }
        public void SetValue(int index, double value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            double old = param.GetValue(m_Data, (double)0);
            if (Math.Abs(old - value) < EPSILON_DBL) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public double GetValue(int index, double def = 0)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(int index, string value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            string old = param.GetValue(m_Data, "");
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, old, value);
            }
        }

        public string GetValue(int index, string def = "")
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }
        #endregion

        #region 通过Param名字访问
        public void SetValue(string name, byte value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            byte old = param.GetValue(m_Data, (byte)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public byte GetValue(string name, byte def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }
        public void SetValue(string name, sbyte value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            sbyte old = param.GetValue(m_Data, (sbyte)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public sbyte GetValue(string name, sbyte def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, short value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            short old = param.GetValue(m_Data, (short)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public short GetValue(string name, short def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, ushort value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            ushort old = param.GetValue(m_Data, (ushort)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public ushort GetValue(string name, ushort def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, int value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            int old = param.GetValue(m_Data, (int)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public int GetValue(string name, int def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, uint value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            uint old = param.GetValue(m_Data, (uint)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public uint GetValue(string name, uint def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, long value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            long old = param.GetValue(m_Data, (long)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public long GetValue(string name, long def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, ulong value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            ulong old = param.GetValue(m_Data, (ulong)0);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public ulong GetValue(string name, ulong def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, bool value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            bool old = param.GetValue(m_Data, false);
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public bool GetValue(string name, bool def = false)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }


        public void SetValue(string name, float value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            float old = param.GetValue(m_Data, (float)0);
            if (Math.Abs(old - value) < EPSILON) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public float GetValue(string name, float def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }
        public void SetValue(string name, double value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            double old = param.GetValue(m_Data, (double)0);
            if (Math.Abs(old - value) < EPSILON_DBL) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public double GetValue(string name, double def = 0)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }

        public void SetValue(string name, string value, bool dirty = true)
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return;
            string old = param.GetValue(m_Data, "");
            if (old == value) return;
            param.SetValue(m_Data, value);
            if (dirty)
            {
                m_DirtyBits[param.Index] = true;
                m_HasDirty = true;
            }
            if (ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(param.Index, old, value);
            }
        }

        public string GetValue(string name, string def = "")
        {
            AParam param = m_PoolDefine.GetParam(name);
            if (param == null)
                return def;
            return param.GetValue(m_Data, def);
        }
        #endregion

        public void Copy2Buff(int index, byte[] buff,int offset)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            param.Copy2Buff(m_Data, buff, offset);
        }

        public void Copy2Buff(AParam param, byte[] buff, int offset)
        {
            if (param == null || param.PoolType != PoolType)
                return;
            param.Copy2Buff(m_Data, buff, offset);
        }

        public void CopyFrom(int index, byte[] buff,int offset)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            param.CopyFrom(m_Data, buff, offset);
        }

        public void CopyFrom(AParam param, byte[] buff, int offset)
        {
            if (param == null || param.PoolType != PoolType)
                return;
            param.CopyFrom(m_Data, buff, offset);
        }

        public void CopyFromIfChanged(int index, byte[] buff, int offset)
        {
            AParam param = m_PoolDefine.GetParam(index);
            if (param == null)
                return;
            object oldVal = param.GetValueByObject(m_Data);
            if (oldVal == null)
                return;
            bool changed = param.CopyFromIfChanged(m_Data, buff, offset);
            object newVal = param.GetValueByObject(m_Data);
            if (newVal == null)
                return;
            if (changed && ParamChangeHandler != null)
            {
                ParamChangeHandler.Invoke(index, oldVal, newVal);
            }
        }
        public bool HasDirty()
        {
            return m_HasDirty;
        }

        public bool CheckDirty(int index)
        {
            if (m_PoolDefine == null)
                return false;
            if(index > PoolDefine.MaxIndex) 
                return false;
            return m_DirtyBits[index];
        }

        public void ClearDirty()
        {
            if (m_PoolDefine == null)
                return;
            m_DirtyBits.SetAll(false);
            m_HasDirty = false;
        }
        public void Clear()
        {
            ParamChangeHandler = null;
            m_PoolDefine = null;

            //这里可以用ArrayPool池优化
            //ArrayPool<byte>.Shared.Return(m_Data, true);
            m_Data = null;
            m_DirtyBits = null;
            m_HasDirty = false;
            m_DataID = INVALID_ID;
        }

        public static ParamPool CreatePool(int paramType, int dataID)
        {
            //TODO:后续优化用工厂或池子实现 
            PoolDefine define = PoolDefineSystem.Instance.GetPoolDefine(paramType);
            if(define== null)
                return null;

            //区分是否有DataID，如果有，走配置初始化
            ParamPool paramPool = ReferencePool.Acquire<ParamPool>();
            paramPool.Init(define);
            PoolConfigData configData = define.GetConfigData(dataID);
            if(configData != null)
            {
                paramPool.CopyFrom(configData);
            }
            return paramPool;
        }
    }
}