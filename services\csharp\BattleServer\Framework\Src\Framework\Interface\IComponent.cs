﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-4
//*********************************************************

using System;
using System.Reflection;

namespace Aurora.Framework
{
    //Component具有Update能力的基类
    public interface IAwake
    {
    }

    public interface IFrequentlyUpdate
    {
    }

    public interface IUpdate
    {
    }

    public interface ILateUpdate
    {
    }

    public interface ISlowlyUpdate
    {
    }

    public interface IDestroy
    {
    }

    //Component各个函数回调包装的基类
    public interface IComponentSystemCallback
    {
        void Invoke(Entity comp);
    }

    public interface IAwakeCallbackArg1
    {
        void Invoke(Entity comp, object arg);
    }

    public interface IAwakeCallbackArg2
    {
        void Invoke(Entity comp, object arg1, object arg2);
    }

    //回调的包装模板类，为了实现返回参数特定化
    public sealed class SystemCallback<T> : IComponentSystemCallback where T: Entity
    {
        private Action<T> Callback = null;

        public SystemCallback(Action<T> action) 
        {
            Callback = action;
        }
        public void Invoke(Entity comp)
        {
            if(Callback != null)
            {
                Callback.Invoke(comp as T);
            }
        }
    }
    public sealed class AwakeCallbackArg1<T,Arg1> : IAwakeCallbackArg1 where T : Entity
    {
        private Action<T,Arg1> Callback;

        public AwakeCallbackArg1(Action<T, Arg1> action)
        {
            Callback = action;
        }
        public void Invoke(Entity comp,object arg)
        {
            if (Callback != null)
            {
                Callback.Invoke(comp as T, (Arg1)arg);
            }
        }
    }

    public sealed class AwakeCallbackArg2<T, Arg1,Arg2> : IAwakeCallbackArg2 where T : Entity
    {
        private Action<T,Arg1, Arg2> Callback = null;

        public AwakeCallbackArg2(Action<T, Arg1, Arg2> action)
        {
            Callback = action;
        }
        public void Invoke(Entity comp, object arg1, object arg2)
        {
            if (Callback != null)
            {
                Callback.Invoke(comp as T, (Arg1)arg1,(Arg2)arg2);
            }
        }
    }
}
