using System;

namespace Aurora.Framework
{
    
    public class MathHelper
    {
        public const float EPSILON = 1.1920928955078125e-7f;
        
        public const double EPSILON_DBL = 2.22044604925031308085e-16;

        public static bool Equal(float a, float b)
        {
            if (Math.Abs(a - b ) > EPSILON)
            {
                return false;
            }

            return true;
        }

        public static bool Equal(double a, double b)
        {
            if (Math.Abs(a - b ) > EPSILON_DBL)
            {
                return false;
            }

            return true;
        }
    }
}
