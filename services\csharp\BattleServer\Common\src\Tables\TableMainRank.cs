#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableMainRank
	{

		public static readonly string TName="MainRank.json";

		#region 属性定义
		/// <summary> 
		/// 段位id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 分数要求 
		/// </summary> 
		public int ScoreRank {get; set;}
		/// <summary> 
		/// 分数重置 
		/// </summary> 
		public int ScoreReset {get; set;}
		/// <summary> 
		/// 分数类型 
		/// </summary> 
		public int ScoreType {get; set;}
		/// <summary> 
		/// 匹配类型 
		/// </summary> 
		public int MatchType {get; set;}
		/// <summary> 
		/// 匹配分范围 
		/// </summary> 
		public int[][] Area {get; set;}
		/// <summary> 
		/// 战斗得分 
		/// </summary> 
		public int[] ScoreBattle {get; set;}
		/// <summary> 
		/// 段位名 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 段位资源 
		/// </summary> 
		public int Icon {get; set;}
		/// <summary> 
		/// 段位奖励 
		/// </summary> 
		public int[][] Reward {get; set;}
		/// <summary> 
		/// 战斗奖励 
		/// </summary> 
		public int[][] RewardBattle {get; set;}
		/// <summary> 
		/// 每日奖励 
		/// </summary> 
		public int[][] RewardDaily {get; set;}
		/// <summary> 
		/// 结算奖励 
		/// </summary> 
		public int[][] RewardSeason {get; set;}
		#endregion

		public static TableMainRank GetData(int ID)
		{
			return TableManager.MainRankData.Get(ID);
		}

		public static List<TableMainRank> GetAllData()
		{
			return TableManager.MainRankData.GetAll();
		}

	}
	public sealed partial class TableMainRankData
	{
		private Dictionary<int, TableMainRank> dict = new Dictionary<int, TableMainRank>();
		private List<TableMainRank> dataList = new List<TableMainRank>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableMainRank.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableMainRank>>(jsonContent);
			foreach (TableMainRank config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableMainRank Get(int id)
		{
			if (dict.TryGetValue(id, out TableMainRank item))
				return item;
			return null;
		}

		public List<TableMainRank> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
