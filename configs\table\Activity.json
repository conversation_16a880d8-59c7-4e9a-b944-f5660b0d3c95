[{"ID": 1001, "Name": 1001, "Type": 1, "TypeValue": [[102], [201, 202, 203]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 1, "OpenCondition2Value": 2, "Duration": 0, "Interval": 0}, {"ID": 1002, "Name": 1002, "Type": 2, "TypeValue": [[10]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 0, "Interval": 0}, {"ID": 1003, "Name": 1003, "Type": 3, "TypeValue": [[101]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 48, "Interval": 48}, {"ID": 1004, "Name": 1004, "Type": 4, "TypeValue": [[101, 101]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 0, "Interval": 0}, {"ID": 1005, "Name": 1005, "Type": 4, "TypeValue": [[101, 102]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 0, "Interval": 0}, {"ID": 1006, "Name": 1006, "Type": 4, "TypeValue": [[101, 103]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 0, "Interval": 0}, {"ID": 1007, "Name": 1007, "Type": 5, "TypeValue": [[201]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 7, "Interval": 0}, {"ID": 1008, "Name": 1008, "Type": 5, "TypeValue": [[202]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 2600, "Duration": 7, "Interval": 0}, {"ID": 1009, "Name": 1009, "Type": 5, "TypeValue": [[203]], "OpenCondition1": 1, "OpenCondition1Value": 29, "OpenCondition2": 2, "OpenCondition2Value": 6600, "Duration": 7, "Interval": 0}, {"ID": 1010, "Name": 1010, "Type": 6, "TypeValue": [[101, 1, 68]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 0, "Interval": 0}, {"ID": 1011, "Name": 1011, "Type": 6, "TypeValue": [[201, 2, 68]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 2500, "Duration": 0, "Interval": 0}, {"ID": 1012, "Name": 1012, "Type": 6, "TypeValue": [[301, 3, 128]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 4100, "Duration": 0, "Interval": 0}, {"ID": 1013, "Name": 1013, "Type": 6, "TypeValue": [[401, 4, 128]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 700, "Duration": 0, "Interval": 0}, {"ID": 1014, "Name": 1014, "Type": 6, "TypeValue": [[501, 5, 128]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 4100, "Duration": 0, "Interval": 0}, {"ID": 1015, "Name": 1015, "Type": 7, "TypeValue": [[1001]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 0, "Interval": 0}, {"ID": 1016, "Name": 1016, "Type": 7, "TypeValue": [[1002]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 7, "Interval": 7}, {"ID": 1017, "Name": 1017, "Type": 7, "TypeValue": [[1003]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 30, "Interval": 30}, {"ID": 1018, "Name": 1018, "Type": 8, "TypeValue": [[601, 6]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 7, "Interval": 7}, {"ID": 1019, "Name": 1019, "Type": 9, "TypeValue": [[101]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 2000, "Duration": 14, "Interval": 0}, {"ID": 1020, "Name": 1020, "Type": 9, "TypeValue": [[102]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 2000, "Duration": 14, "Interval": 0}, {"ID": 1021, "Name": 1021, "Type": 10, "TypeValue": [[701, 7, 12, 30]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 1000, "Duration": 7, "Interval": 0}, {"ID": 1022, "Name": 1022, "Type": 10, "TypeValue": [[801, 8, 12, 30]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 4100, "Duration": 7, "Interval": 0}, {"ID": 1023, "Name": 1023, "Type": 10, "TypeValue": [[901, 9, 12, 30]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 2500, "Duration": 7, "Interval": 0}, {"ID": 1024, "Name": 1024, "Type": 11, "TypeValue": [[18, 1002]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 1500, "Duration": 7, "Interval": 0}, {"ID": 1025, "Name": 1025, "Type": 12, "TypeValue": [[68], [1001], [10], [1, 2, 3, 4, 5, 6, 7], [1, 1, 0, 0, 0, 0, 0], [101, 99]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 0, "OpenCondition2Value": 0, "Duration": 30, "Interval": 0}, {"ID": 1026, "Name": 1026, "Type": 13, "TypeValue": [[1, 2, 3, 4, 5, 6, 7, 8, 9], [1001], [1], [101]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 3000, "Duration": 5, "Interval": 5}, {"ID": 1027, "Name": 1027, "Type": 14, "TypeValue": [[1, 1101, 11, 68, 1003, 103]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 3000, "Duration": 5, "Interval": 5}, {"ID": 1028, "Name": 1028, "Type": 15, "TypeValue": [[1004, 5, 750, 104, 102]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 6600, "Duration": 7, "Interval": 7}, {"ID": 1029, "Name": 1029, "Type": 16, "TypeValue": [[1201, 68, 105, 103, 1001, 1, 1]], "OpenCondition1": 1, "OpenCondition1Value": 2, "OpenCondition2": 2, "OpenCondition2Value": 4000, "Duration": 7, "Interval": 7}, {"ID": 1030, "Name": 1030, "Type": 17, "TypeValue": [[1, 1203, 98, 12, 1005, 106, 104]], "OpenCondition1": 1, "OpenCondition1Value": 0, "OpenCondition2": 2, "OpenCondition2Value": 3000, "Duration": 7, "Interval": 7}]