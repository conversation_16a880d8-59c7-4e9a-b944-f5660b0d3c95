﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-27
//*********************************************************

using System;
using System.Collections.Generic;
using System.Reflection;

namespace Aurora.Framework
{/*
    public class ActorMsgHandlerComponent : BaseComponent, IAwake
    {
        public static ActorMsgHandlerComponent Instance { get; set; }

        //当前进程Actor消息处理
        public Dictionary<ushort, List<IActorMessageHandler>> Handlers = new Dictionary<ushort, List<IActorMessageHandler>>();
    }

    [ComponentSystem(typeof(ActorMsgHandlerComponent))]
    public static class ActorMsgHandlerComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(ActorMsgHandlerComponent self)
        {
            ActorMsgHandlerComponent.Instance = self;

            List<Type> allTypes = AssemblyManager.Instance.GetAllTypes();
            if (allTypes.Count <= 0)
            {
                throw new FrameworkException("Assembly Init is Empty!");
            }
            foreach (Type type in allTypes)
            {
                var methods = type.GetMethods();
                foreach (var method in methods)
                {
                    var msgMethodAttr = method.GetCustomAttribute<ActorMsgAttribute>();
                    if (msgMethodAttr == null)
                    {
                        continue;
                    }
                    IActorMessageHandler handler = ActorMsgHandlerBinder.Bind(msgMethodAttr.MsgType,method);
                    if (handler == null)
                    {
                        Log.Error($"Actor消息绑定回调错误：Type={msgMethodAttr.MsgType.Name},Method={method.Name}");
                        continue;
                    }
                    ushort msgID = MessageIDComponent.Instance.GetMsgID(msgMethodAttr.MsgType);
                    if (!self.Handlers.ContainsKey(msgID))
                    {
                        self.Handlers[msgID] = new List<IActorMessageHandler>();
                    }
                    self.Handlers[msgID].Add(handler);
                }
            }
        }

        public static async ATask HandleAsync(this ActorMsgHandlerComponent self, Entity entity, Session session, Packet pkt)
        {
            ushort msgID = pkt.MsgID;
            List<IActorMessageHandler> handles = null;
            if (!self.Handlers.TryGetValue(msgID, out handles))
            {
                string msgName = msgID.ToString();
                Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                if (msgType != null)
                {
                    msgName = msgType.Name;
                }
                Log.Error($"未找到Actor的消息处理函数，Type={msgName}");
                ReferencePool.Release(pkt);
                return;
            }
            foreach (var h in handles)
            {
                try
                {
                    await h.Handle(entity, session, pkt);
                }
                catch (Exception e)
                {
                    string msgName = msgID.ToString();
                    Type msgType = MessageIDComponent.Instance.GetMsgType(msgID);
                    if (msgType != null)
                    {
                        msgName = msgType.Name;
                    }
                    Log.Exception($"Actor消息处理异常,Type={msgName}, 错误:{e.Message}");
                }
            }
            ReferencePool.Release(pkt);
        }

        public static bool HasHandler(this ActorMsgHandlerComponent self, Packet pkt)
        {
            return self.HasHandler(pkt.MsgID);
        }
        public static bool HasHandler(this ActorMsgHandlerComponent self, IMessage message)
        {
            ushort msgID = MessageIDComponent.Instance.GetMsgID(message.GetType());
            return self.HasHandler(msgID);
        }
        public static bool HasHandler(this ActorMsgHandlerComponent self, ushort msgID)
        {
            return self.Handlers.ContainsKey(msgID);
        }

        private static void FailResponse(Session session, IRequest request, int error)
        {
            if (session == null || session.IsReleased)
            {
                Log.Error("Actor msg handler fail response in null session!");
                return;
            }
            IResponse response = MessageIDComponent.Instance.CreateResponse(request, error);
            session.Reply(response);
        }

        public static void HandleActorRequest(this ActorMsgHandlerComponent self, Packet pkt)
        {
            self._HandleActorRequest(pkt).Coroutine();
        }
        private static async ATask _HandleActorRequest(this ActorMsgHandlerComponent self, Packet pkt)
        {
            InstanceIdStruct instanceIdStruct = new InstanceIdStruct(pkt.ActorID);
            int fromProcess = instanceIdStruct.Process;
            instanceIdStruct.Process = ServerOptions.Instance.Process;
            long realActorId = instanceIdStruct.ToLong();//本进程里的ActorID
            Session replySession = NetInnerComponent.Instance.GetSession(fromProcess);
            Entity entity = EntitySystem.Instance.GetEntity(realActorId);
            if (entity == null)
            {
                FailResponse(replySession, pkt.GetMessage() as IRequest, FError.ERR_ActorNotFound);
                ReferencePool.Release(pkt);
                return;
            }
            MailboxComponent mailbox = entity.GetComponent<MailboxComponent>();
            if (mailbox != null)
            {
                //Actor消息需要加入队列处理
                long instanceID = entity.InstanceID;
                using (await CoroutineLockComponent.Instance.Wait(CoroutineLockType.Mailbox, realActorId))
                {
                    //因为这里是异步等待，有可能这个Entity会不纯在
                    if (entity.InstanceID != instanceID)
                    {
                        FailResponse(replySession, pkt.GetMessage() as IRequest, FError.ERR_ActorNotFound);
                        ReferencePool.Release(pkt);
                        return;
                    }
                    await self.HandleAsync(entity, replySession, pkt);
                }
            }
            else
            {
                await self.HandleAsync(entity, replySession, pkt);
            }

        }
    }
    */
}
