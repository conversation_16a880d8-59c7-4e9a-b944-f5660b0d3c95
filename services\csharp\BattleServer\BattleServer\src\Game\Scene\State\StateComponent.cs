﻿using Aurora.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BattleServer.Game
{
    public class StateComponent
    {
        private Dictionary<StateType, State> mStateDic = new Dictionary<StateType, State>();
        private State mCurrentState = null;
        private StateType mCurrentStateType = StateType.None;

        private Scene mScene;


        public StateComponent(Scene scene)
        {
            mScene = scene;
            AddStates();
        }

        private void AddStates()
        {

            AddState(StateType.Wait, new StateWait(this));
            AddState(StateType.Match, new StateMatch(this));
            AddState(StateType.Buffer, new StateBuffer(this));
            AddState(StateType.UpdateHero, new StateUpdateHero(this));
            AddState(StateType.Preparation, new StatePreparation(this));
            AddState(StateType.Fight, new StateFight(this));
            AddState(StateType.End, new StateEnd(this));
            AddState(StateType.Settlement, new StateSettlement(this));

        }
        private void AddState(StateType stateType, State state)
        {
            if (mStateDic.ContainsKey(stateType))
            {
                Log.Error($"StateComponent.AddState state is already exist  {stateType.ToString()}");
                return;
            }
            mStateDic.Add(stateType, state);

            state.OnInit();
        }

        public void ChangeState(StateType stateType)
        {
            StateType oldStateType = mCurrentStateType;
            if (!mStateDic.ContainsKey(stateType))
            {
                Log.Error("StateComponent.ChangeState state is not exist");
                return;
            }


            if (mCurrentState != null)
            {
                mCurrentState.OnExit();
            }

            mCurrentState = mStateDic[stateType];
            if (mCurrentState == null)
            {
                Log.Error("StateComponent.ChangeState mCurrentState is null");
                return;
            }
            mCurrentStateType = stateType;
            mCurrentState.OnEnter();

            //Log.Error("ChangeState: oldStateType " + oldStateType + "  newStateType " + stateType + " ID " + GetEntity().GetID());
        }

        public void OnUpdate(float deltaTime)
        {

            if (mCurrentState != null)
                mCurrentState.OnUpdate(deltaTime);

        }
        //public void OnLateUpdate(float deltaTime)
        //{
        //    if (mCurrentState != null)
        //        mCurrentState.OnLateUpdate(deltaTime);

        //    foreach (var state in mStateDic)
        //    {
        //        if (state.Value.TryEnter(mCurrentStateType))
        //        {
        //            ChangeState(state.Key);
        //            break;
        //        }
        //    }
        //}

        public Scene GetScene() {  return mScene;  }
    }
}
