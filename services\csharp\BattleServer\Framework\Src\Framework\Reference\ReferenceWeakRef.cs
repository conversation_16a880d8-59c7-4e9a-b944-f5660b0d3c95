﻿namespace Aurora.Framework
{
    /// <summary>
    /// 线程非安全！！！
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ReferenceWeakRef<T> where T : class,IReference,IReferenceWeak
    {
        private long _instanceId;

        private T _reference;
        public T Reference {
            get
            {
                if (_reference != null && _reference.InstanceId == _instanceId)
                {
                    return _reference;
                }
            
                return null;
            }
            set
            {
                if (value == null)
                {
                    _instanceId = 0;
                    _reference = null;
                    return;
                }
                _reference = value;
                _instanceId = value.InstanceId;
            }
        }
    
        public ReferenceWeakRef()
        {
       
        }
    }
}
