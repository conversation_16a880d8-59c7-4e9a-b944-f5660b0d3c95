﻿using System.Collections.Generic;

namespace Aurora.Framework
{
    public class DynamicSelector
    {
        public delegate void EnterSelection(long key);
        public delegate void LeaveSelection(long key);

        private bool _needReselect;
        private bool _minFirst;
        private uint _selectionMaxCount;
        private EnterSelection _enterSelection;
        private LeaveSelection _leaveSelection;

        private DynamicHeap _selectedList = new DynamicHeap();
        private DynamicHeap _unselectedList = new DynamicHeap();

        public int Count
        {
            get
            {
                return (_selectedList.Count + _unselectedList.Count);
            }
        }

        public void Init(bool minFirst, uint selectionMaxCount, EnterSelection enterSelection, LeaveSelection leaveSelection)
        {
            Reset();

            _minFirst = minFirst;
            _selectionMaxCount = selectionMaxCount;
            _enterSelection = enterSelection;
            _leaveSelection = leaveSelection;

            if (minFirst)
            {
                _selectedList.Init(false);
                _unselectedList.Init(true);
            }
            else
            {
                _selectedList.Init(true);
                _unselectedList.Init(false);
            }
        }

        public void Reset()
        {
            Clear();

            _needReselect = false;
            _minFirst = false;
            _selectionMaxCount = 0;
            _enterSelection = null;
            _leaveSelection = null;
        }

        public void Clear()
        {
            _selectedList.Clear();
            _unselectedList.Clear();
        }

        public void BuildSelectedKeyList(ref List<long> selectedKeyList)
        {
            _selectedList.BuildKeyList(ref selectedKeyList);
        }

        public void SetSelectedMaxCount(uint selectionMaxCount)
        {
            if (_selectionMaxCount != selectionMaxCount)
            {
                _selectionMaxCount = selectionMaxCount;
                _needReselect = true;
            }
        }

        public bool Add(long key, long value)
        {
            if (!_selectedList.Get(key, out _))
            {
                if (_unselectedList.Add(key, value))
                {
                    _needReselect = true;
                    return true;
                }
            }

            return false;
        }

        public bool Remove(long key)
        {
            if (_selectedList.Remove(key))
            {
                _leaveSelection?.Invoke(key);
                _needReselect = true;
                return true;
            }
            else if (_unselectedList.Remove(key))
            {
                return true;
            }

            return false;
        }

        public bool Set(long key, long value)
        {
            if (_selectedList.Set(key, value))
            {
                _needReselect = true;
                return true;
            }
            else if (_unselectedList.Set(key, value))
            {
                _needReselect = true;
                return true;
            }

            return false;
        }

        public bool Get(long key, out long value)
        {
            if (_selectedList.Get(key, out value))
            {
                return true;
            }
            else if (_unselectedList.Get(key, out value))
            {
                return true;
            }

            return false;
        }

        public bool SelectOne()
        {
            bool found = false;

            if (_needReselect)
            {
                while (_selectedList.Count > _selectionMaxCount)
                {
                    if (_selectedList.Pop(out long key, out long value))
                    {
                        if (_unselectedList.Add(key, value))
                        {
                            _leaveSelection?.Invoke(key);

                            continue;
                        }
                        else
                        {
                            Log.Error($"Dynamic Selector Select One Error! Unselected List Add Fail.");
                        }
                    }
                    else
                    {
                        Log.Error($"Dynamic Selector Select One Error! Selected List Pop Fail.");
                    }

                    break;
                }

                if (_selectedList.Count < _selectionMaxCount)
                {
                    if (_unselectedList.Pop(out long key, out long value))
                    {
                        if (_selectedList.Add(key, value))
                        {
                            _enterSelection?.Invoke(key);

                            found = true;
                        }
                        else
                        {
                            Log.Error($"Dynamic Selector Select One Error! Selected List Add Fail.");
                        }
                    }
                }
                else if (_selectedList.Count == _selectionMaxCount)
                {
                    if (_unselectedList.Peek(out long outKey, out long outValue))
                    {
                        if (_selectedList.Peek(out long inKey, out long inValue))
                        {
                            if ((_minFirst && (outValue < inValue)) || (!_minFirst && (outValue > inValue)))
                            {
                                if (_selectedList.Pop(out inKey, out inValue))
                                {
                                    if (_unselectedList.Add(inKey, inValue))
                                    {
                                        _leaveSelection?.Invoke(inKey);

                                        if (_unselectedList.Pop(out outKey, out outValue))
                                        {
                                            if (_selectedList.Add(outKey, outValue))
                                            {
                                                _enterSelection?.Invoke(outKey);

                                                found = true;
                                            }
                                            else
                                            {
                                                Log.Error($"Dynamic Selector Select One Error! Selected List Add Fail.");
                                            }
                                        }
                                        else
                                        {
                                            Log.Error($"Dynamic Selector Select One Error! Unselected List Pop Fail.");
                                        }
                                    }
                                    else
                                    {
                                        Log.Error($"Dynamic Selector Select One Error! Unselected List Add Fail.");
                                    }
                                }
                                else
                                {
                                    Log.Error($"Dynamic Selector Select One Error! Selected List Pop Fail.");
                                }
                            }
                        }
                    }
                }

                if (!found)
                {
                    _needReselect = false;
                }
            }

            return found;
        }

        public void SelectAll()
        {
            if (_needReselect)
            {
                _needReselect = false;

                while (_selectedList.Count > _selectionMaxCount)
                {
                    if (_selectedList.Pop(out long key, out long value))
                    {
                        if (_unselectedList.Add(key, value))
                        {
                            _leaveSelection?.Invoke(key);

                            continue;
                        }
                        else
                        {
                            Log.Error($"Dynamic Selector Select All Error! Unselected List Add Fail.");
                        }
                    }
                    else
                    {
                        Log.Error($"Dynamic Selector Select All Error! Selected List Pop Fail.");
                    }

                    break;
                }

                while (_selectedList.Count < _selectionMaxCount)
                {
                    if (_unselectedList.Pop(out long key, out long value))
                    {
                        if (_selectedList.Add(key, value))
                        {
                            _enterSelection?.Invoke(key);

                            continue;
                        }
                        else
                        {
                            Log.Error($"Dynamic Selector Select All Error! Selected List Add Fail.");
                        }
                    }

                    break;
                }

                if (_selectedList.Count == _selectionMaxCount)
                {
                    while (true)
                    {
                        if (_unselectedList.Peek(out long outKey, out long outValue))
                        {
                            if (_selectedList.Peek(out long inKey, out long inValue))
                            {
                                if ((_minFirst && (outValue < inValue)) || (!_minFirst && (outValue > inValue)))
                                {
                                    if (_selectedList.Pop(out inKey, out inValue))
                                    {
                                        if (_unselectedList.Add(inKey, inValue))
                                        {
                                            _leaveSelection?.Invoke(inKey);

                                            if (_unselectedList.Pop(out outKey, out outValue))
                                            {
                                                if (_selectedList.Add(outKey, outValue))
                                                {
                                                    _enterSelection?.Invoke(outKey);

                                                    continue;
                                                }
                                                else
                                                {
                                                    Log.Error($"Dynamic Selector Select All Error! Selected List Add Fail.");
                                                }
                                            }
                                            else
                                            {
                                                Log.Error($"Dynamic Selector Select All Error! Unselected List Pop Fail.");
                                            }
                                        }
                                        else
                                        {
                                            Log.Error($"Dynamic Selector Select All Error! Unselected List Add Fail.");
                                        }
                                    }
                                    else
                                    {
                                        Log.Error($"Dynamic Selector Select All Error! Selected List Pop Fail.");
                                    }
                                }
                            }
                        }

                        break;
                    }
                }
            }
        }
    }
}
