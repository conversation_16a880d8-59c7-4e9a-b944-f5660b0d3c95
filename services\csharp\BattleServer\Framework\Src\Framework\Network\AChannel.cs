﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-15
//*********************************************************

using System.IO;
using System.Net;

namespace Aurora.Framework
{
    public enum ChannelType
    {
        Connect,
        Accept,
    }


    public abstract class AChannel : IReference
    {
        public ChannelType ChannelType { get; protected set; }
        public int Error { get; set; }
        public IPEndPoint RemoteAddress { get; set; }

        public abstract void Clear();
    }
}
