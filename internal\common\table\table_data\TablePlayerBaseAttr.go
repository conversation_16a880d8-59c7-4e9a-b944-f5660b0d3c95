/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TablePlayerBaseAttr struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 晴天的模型
	SunnyModelIds []int32
	// 雨天的模型
	RainModelIds []int32
	// 拥有的技能id
	OwnSkillConfigId int32
	// 武器挂点名字
	WeaponPoint string
	// 默认的武器模型id
	DefaultWeaponModelId int32
	// 角色死亡后尸体存在的时间
	PlayerDeathTime float32
	// 攻击
	Attack int32
	// 生命
	Hp int32
	// 防御
	Def int32
	// 命中
	HitPre int32
	// 闪避
	DodgePre int32
	// 基础抗性
	BaseOccupationDef int32
	// 抗性类型数组
	OccupationDefArr []int32
	// 抗性的值
	OccupationDefValue int32
	// 穿透值
	Penetrate int32
	// 穿透率
	PenetratePre int32
	// 暴击抵抗
	CriticalResistPre int32
	// 暴击率
	CriticalPre int32
	// 暴击倍数
	CriticalMultiplePre int32
}




// TablePlayerBaseAttrData 表格
type TablePlayerBaseAttrData struct {
	file    string
	dataMap map[int32]*TablePlayerBaseAttr
	Data    []*TablePlayerBaseAttr
	md5     string
}

// load 加载
func (tb *TablePlayerBaseAttrData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TablePlayerBaseAttr{}))
	if err != nil {
		return err
	}

	typeData := make([]*TablePlayerBaseAttr, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TablePlayerBaseAttr)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TablePlayerBaseAttr, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TablePlayerBaseAttrData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TablePlayerBaseAttr{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TablePlayerBaseAttr, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TablePlayerBaseAttr)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TablePlayerBaseAttrData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TablePlayerBaseAttrData) GetById(id int32) *TablePlayerBaseAttr {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TablePlayerBaseAttrData) GetCloneById(id int32) *TablePlayerBaseAttr {
	v := tb.dataMap[id]
	out := &TablePlayerBaseAttr{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TablePlayerBaseAttrData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TablePlayerBaseAttrData) Foreach(call func(*TablePlayerBaseAttr) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TablePlayerBaseAttrData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TablePlayerBaseAttrData) Clone() ITable {
	ntb := &TablePlayerBaseAttrData{
		file:    tb.file,
		dataMap: make(map[int32]*TablePlayerBaseAttr),
		Data:    make([]*TablePlayerBaseAttr, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TablePlayerBaseAttr{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
