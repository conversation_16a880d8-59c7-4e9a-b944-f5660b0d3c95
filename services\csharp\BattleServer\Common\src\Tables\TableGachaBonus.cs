#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGachaBonus
	{

		public static readonly string TName="GachaBonus.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 解锁条件 
		/// </summary> 
		public int ClearStage {get; set;}
		/// <summary> 
		/// 奖励 
		/// </summary> 
		public int DropGroupId {get; set;}
		#endregion

		public static TableGachaBonus GetData(int ID)
		{
			return TableManager.GachaBonusData.Get(ID);
		}

		public static List<TableGachaBonus> GetAllData()
		{
			return TableManager.GachaBonusData.GetAll();
		}

	}
	public sealed partial class TableGachaBonusData
	{
		private Dictionary<int, TableGachaBonus> dict = new Dictionary<int, TableGachaBonus>();
		private List<TableGachaBonus> dataList = new List<TableGachaBonus>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGachaBonus.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGachaBonus>>(jsonContent);
			foreach (TableGachaBonus config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGachaBonus Get(int id)
		{
			if (dict.TryGetValue(id, out TableGachaBonus item))
				return item;
			return null;
		}

		public List<TableGachaBonus> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
