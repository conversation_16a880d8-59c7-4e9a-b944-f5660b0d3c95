#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableTopupRebate
	{

		public static readonly string TName="TopupRebate.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 任务类型 
		/// </summary> 
		public int TaskType {get; set;}
		/// <summary> 
		/// 返利参数1 
		/// </summary> 
		public int Param1 {get; set;}
		/// <summary> 
		/// 返利参数2 
		/// </summary> 
		public int Param2 {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		/// <summary> 
		/// 展示道具id 
		/// </summary> 
		public int ItemId {get; set;}
		/// <summary> 
		/// 展示道具num 
		/// </summary> 
		public int ItemNum {get; set;}
		#endregion

		public static TableTopupRebate GetData(int ID)
		{
			return TableManager.TopupRebateData.Get(ID);
		}

		public static List<TableTopupRebate> GetAllData()
		{
			return TableManager.TopupRebateData.GetAll();
		}

	}
	public sealed partial class TableTopupRebateData
	{
		private Dictionary<int, TableTopupRebate> dict = new Dictionary<int, TableTopupRebate>();
		private List<TableTopupRebate> dataList = new List<TableTopupRebate>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableTopupRebate.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableTopupRebate>>(jsonContent);
			foreach (TableTopupRebate config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableTopupRebate Get(int id)
		{
			if (dict.TryGetValue(id, out TableTopupRebate item))
				return item;
			return null;
		}

		public List<TableTopupRebate> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
