using Aurora.Framework;
using BattleServer.Game.Core;
using BattleServer.Server;
using Game.Core;
using System;
using System.Collections.Generic;

namespace BattleServer.Game
{
    //public class Entity : IReference
    //{
    //    // 实体唯一ID
    //    public int EntityId { get; set; }
        
    //    // 实体类型ID (对应配置表中的英雄ID)
    //    public int ConfigId { get; set; }
        
    //    // 星级 (1-3)
    //    public int StarLevel { get; set; } = 1;
        
    //    // 所属玩家ID
    //    public long OwnerId { get; set; }
        
    //    // 在棋盘上的位置
    //    public int GridX { get; set; }
    //    public int GridY { get; set; }
        
    //    // 初始化方法
    //    public virtual void Init(int entityId, int configId, long ownerId)
    //    {
    //        EntityId = entityId;
    //        ConfigId = configId;
    //        OwnerId = ownerId;
    //        StarLevel = 1;
    //    }
        
    //    // 清理方法 (实现IReference接口)
    //    public virtual void Clear()
    //    {
    //        EntityId = 0;
    //        ConfigId = 0;
    //        OwnerId = 0;
    //        StarLevel = 1;
    //        GridX = 0;
    //        GridY = 0;
    //    }
        
    //    // 升星操作
    //    public bool UpgradeStar()
    //    {
    //        if (StarLevel < BattleConfig.Entity.GetMaxStarLevel())
    //        {
    //            StarLevel++;
    //            return true;
    //        }
    //        return false;
    //    }
    //}

    public class Hero
    {
        public int ResID { get; set; }
        public int Position { get; set; }
        public int StarLevel { get; set; }

        public BattlePlayer Player { get; set; }
        public TableHero Res {  get; set; }

        //public Hero(int redId, int starLevel, BattlePlayer player)
        //{
        //    ResID = redId;
        //    Player = player;
        //    Position = 0;
        //    StarLevel = starLevel;
        //}
        public int GetResID()
        {
            return ResID;
        }

        public void LevelUp()
        {
            if (StarLevel >= 5)
                return;

            StarLevel = StarLevel + 1;

            TableServerSkill skill = Player.GetLevelUpSkill();
            if (skill == null)
                return;

            if (!TableServerSkill.RandomWeight(skill.ID))
            {
                Log.Info($"[Hero] {ResID} Position {Position} StarLevel {StarLevel} have skill but randNum less");
                return;
            }

            Log.Info($"[Hero] {ResID} Position {Position} StarLevel {StarLevel} have level up skill ");
            StarLevelUp levelUp = TableServerSkill.GetLevelUpSkill(skill.ID);
            if (StarLevel >= levelUp.MaxStarLevel)
            {
                Log.Info($"[Hero] {ResID} Position {Position} StarLevel {StarLevel} >= MaxStarLevel {levelUp.MaxStarLevel}");
                return;
            }

            StarLevel += levelUp.StarLevel;
            if (StarLevel >= levelUp.MaxStarLevel)
            {
                StarLevel = levelUp.MaxStarLevel;
            }
        }

        public PBBattleHeroInfo ToPB()
        {
            var pbHero = new PBBattleHeroInfo();
            pbHero.Id = ResID;
            pbHero.Level = StarLevel; // 使用阵容中的真实等级
            pbHero.StarLevel = StarLevel;
            pbHero.AwakeLevel = 0;

            return pbHero;
        }
    }

    public static class HeroFactory
    {
        public static Hero CreateHero(int nConfigID, int starLevel, BattlePlayer player)
        {
            // this will be modified to use a factory method or DI container in the future
            Hero hero = new Hero();
            hero.ResID = nConfigID;
            hero.Res = TableHero.GetData(nConfigID);
            hero.StarLevel = starLevel;
            hero.Player = player;

            return hero;
        }
    }

    public static class BestPosition
    {
        public static int[] Front = new int[30] { 27, 28, 26, 29, 25, 30, 21, 22, 20, 23, 19, 24, 15, 16, 14, 17, 13, 18, 9, 10, 8, 11, 7, 12, 3, 4, 2, 5, 1, 6};
        public static int[] Center = new int[30] { 21, 22, 20, 23, 19, 24, 15, 16, 14, 17, 13, 18, 9, 10, 8, 11, 7, 12, 3, 4, 2, 5, 1, 6, 27, 28, 26, 29, 25, 30 };
        public static int[] Back = new int[30] { 3, 4, 2, 5, 1, 6, 9, 10, 8, 11, 7, 12, 15, 16, 14, 17, 13, 18, 21, 22, 20, 23, 19, 24, 27, 28, 26, 29, 25, 30 };

        public static int[] GetBestPosition(int position)
        {
            if (position == 1)
            {
                return Front;
            }
            else if (position == 2)
            {
                return Center;
            }
            else if (position == 3)
            {
                return Back;
            }

            return Center;
        }
    }
}
