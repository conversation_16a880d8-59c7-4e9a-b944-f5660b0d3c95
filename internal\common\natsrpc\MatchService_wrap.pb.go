// Code generated by protoc-gen-rpc-wrap. DO NOT EDIT.
// versions:
// - protoc-gen-rpc-wrap v1.0.0
// - protoc             v4.23.2
// source: MatchService.proto

package natsrpc

type matchserviceClientWrap struct {
	MatchServiceClient
}

func NewMatchserviceClientWrap(matchserviceclient MatchServiceClient) *matchserviceClientWrap {
	return &matchserviceClientWrap{
		MatchServiceClient: matchserviceclient,
	}
}
