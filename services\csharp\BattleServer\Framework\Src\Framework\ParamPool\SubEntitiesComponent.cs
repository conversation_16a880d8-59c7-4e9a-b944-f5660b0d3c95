﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-2-20
//*********************************************************

using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
namespace Aurora.Framework
{
    public struct SubEntityKey : IEquatable<SubEntityKey>
    {
        public int m_ParentIndex;
        public int m_ParentDataType;
        public long m_ParentElemIndex;
        public SubEntityKey(int parentIndex, EParamIncrementType parentDataType, long parentElemIndex)
        {
            m_ParentIndex = parentIndex;
            m_ParentDataType = (int)parentDataType;
            m_ParentElemIndex = parentElemIndex;
        }
        public bool Equals(SubEntityKey other)
        {
                return m_ParentIndex == other.m_ParentIndex &&
                    m_ParentDataType == other.m_ParentDataType &&
                    m_ParentElemIndex == other.m_ParentElemIndex;
        }

        public override bool Equals(object obj)
        {
            return obj is SubEntityKey other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(m_ParentIndex, m_ParentDataType, m_ParentElemIndex);
        }
    }
    public class SubEntitiesComponent : BaseComponent, IAwake, IDestroy
    {
        public Dictionary<SubEntityKey, PoolEntity> SubEntities;
        public bool m_HasDirty = true;
    }

    [ComponentSystem(typeof(SubEntitiesComponent))]
    public static class SubEntitiesComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(this SubEntitiesComponent self)
        {
            if (self == null) return;
            self.SubEntities = new Dictionary<SubEntityKey, PoolEntity>();
            self.m_HasDirty = true;
        }

        [MethodDestroy]
        public static void OnDestroy(this SubEntitiesComponent self)
        {
            if (self == null) return;
            self.SubEntities.Clear();
            self.m_HasDirty = true;
        }
        public static void AddSubEntity(this SubEntitiesComponent self, SubEntityKey subEntityKey, PoolEntity subComp)
        {
            if (self == null) return;
            if (self.SubEntities.ContainsKey(subEntityKey))
            {
                return;
            }
            self.SubEntities.Add(subEntityKey, subComp);
            self.AddChild(subComp);
        }

        public static PoolEntity GetSubEntity(this SubEntitiesComponent self, SubEntityKey subEntityKey)
        {
            PoolEntity subComp = null;
            if (self == null) return subComp;
            self.SubEntities.TryGetValue(subEntityKey, out subComp);
            return subComp;
        }

        public static void RemoveSubEntity(this SubEntitiesComponent self, SubEntityKey subEntityKey)
        {
            if (self == null) return;
            PoolEntity subComp = null;
            if (self.SubEntities.TryGetValue(subEntityKey, out subComp))
            {
                self.SubEntities.Remove(subEntityKey);
                self.RemoveChild(subComp);
            }
        }

        public static int GetSubUnitCount(this SubEntitiesComponent self)
        {
            return 0;
        }
        public static bool HasDirty(this SubEntitiesComponent self)
        {
            return self.m_HasDirty;
        }
        public static void MarkDirty(this SubEntitiesComponent self)
        {
            self.m_HasDirty = true;
            ParamComponent parentComp = self.Parent as ParamComponent;
            if(parentComp != null)
            {
                parentComp.m_HasDirty = true;
                PoolEntity poolEntity = parentComp.Parent as PoolEntity;
                if (poolEntity != null)
                {
                    poolEntity.MarkDirty();
                }
            }
        }
        public static void ClearDirty(this SubEntitiesComponent self)
        {
            self.m_HasDirty = false;
            foreach (PoolEntity entity in self.SubEntities.Values)
            {
                if (null != entity)
                {
                    if (entity.HasDirty())
                    {
                        entity.ClearDirty();
                    }
                }
            }
        }
    }

}