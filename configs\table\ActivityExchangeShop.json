[{"ID": 101, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 1, "Price": 100, "Order": 1}, {"ID": 102, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 1, "Price": 50, "Order": 2}, {"ID": 103, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 1, "Price": 5, "Order": 3}, {"ID": 104, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 5, "CurrencyType": 1, "Price": 400, "Order": 4}, {"ID": 105, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 50, "CurrencyType": 1, "Price": 80, "Order": 5}, {"ID": 106, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 10, "CurrencyType": 1, "Price": 80, "Order": 6}, {"ID": 107, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 1, "Price": 80, "Order": 7}, {"ID": 108, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 1, "Price": 30, "Order": 8}, {"ID": 109, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 1, "Price": 30, "Order": 9}, {"ID": 110, "GroupId": 101, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 1, "Price": 10, "Order": 10}, {"ID": 111, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 2, "Price": 1200, "Order": 1}, {"ID": 112, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 2, "Price": 4380, "Order": 2}, {"ID": 113, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 2, "Price": 4380, "Order": 3}, {"ID": 114, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 2, "Price": 850, "Order": 4}, {"ID": 115, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 30, "CurrencyType": 2, "Price": 20, "Order": 5}, {"ID": 116, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 200, "CurrencyType": 2, "Price": 5, "Order": 6}, {"ID": 117, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 200, "CurrencyType": 2, "Price": 8, "Order": 7}, {"ID": 118, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 150, "CurrencyType": 2, "Price": 15, "Order": 8}, {"ID": 119, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 2, "Price": 850, "Order": 9}, {"ID": 120, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 20, "CurrencyType": 2, "Price": 70, "Order": 10}, {"ID": 121, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 20, "CurrencyType": 2, "Price": 30, "Order": 11}, {"ID": 122, "GroupId": 102, "Drop": [[101, 99]], "ExchangeNumber": 10, "CurrencyType": 2, "Price": 30, "Order": 12}, {"ID": 123, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 3, "CurrencyType": 3, "Price": 2000, "Order": 1}, {"ID": 124, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 30, "CurrencyType": 3, "Price": 60, "Order": 2}, {"ID": 125, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 3, "Price": 2000, "Order": 3}, {"ID": 126, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 3, "Price": 2000, "Order": 4}, {"ID": 127, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 3, "Price": 2000, "Order": 5}, {"ID": 128, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 10, "CurrencyType": 3, "Price": 120, "Order": 6}, {"ID": 129, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 10, "CurrencyType": 3, "Price": 1200, "Order": 7}, {"ID": 130, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 5, "CurrencyType": 3, "Price": 120, "Order": 8}, {"ID": 131, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 3, "CurrencyType": 3, "Price": 600, "Order": 9}, {"ID": 132, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 5, "CurrencyType": 3, "Price": 30, "Order": 10}, {"ID": 133, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 5, "CurrencyType": 3, "Price": 210, "Order": 11}, {"ID": 134, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 5, "CurrencyType": 3, "Price": 3000, "Order": 12}, {"ID": 135, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 80, "CurrencyType": 3, "Price": 25, "Order": 13}, {"ID": 136, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 100, "CurrencyType": 3, "Price": 15, "Order": 14}, {"ID": 137, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 150, "CurrencyType": 3, "Price": 8, "Order": 15}, {"ID": 138, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 70, "CurrencyType": 3, "Price": 120, "Order": 16}, {"ID": 139, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 30, "CurrencyType": 3, "Price": 400, "Order": 17}, {"ID": 140, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 20, "CurrencyType": 3, "Price": 250, "Order": 18}, {"ID": 141, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 3, "Price": 3000, "Order": 19}, {"ID": 142, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 3, "Price": 50, "Order": 20}, {"ID": 143, "GroupId": 103, "Drop": [[101, 99]], "ExchangeNumber": 0, "CurrencyType": 3, "Price": 10, "Order": 21}, {"ID": 144, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 100, "CurrencyType": 4, "Price": 600, "Order": 1}, {"ID": 145, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 100, "CurrencyType": 4, "Price": 400, "Order": 2}, {"ID": 146, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 25, "CurrencyType": 4, "Price": 2800, "Order": 3}, {"ID": 147, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 1, "CurrencyType": 4, "Price": 6800, "Order": 4}, {"ID": 148, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 999, "CurrencyType": 4, "Price": 10, "Order": 5}, {"ID": 149, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 50, "CurrencyType": 4, "Price": 250, "Order": 6}, {"ID": 150, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 999, "CurrencyType": 4, "Price": 200, "Order": 7}, {"ID": 151, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 999, "CurrencyType": 4, "Price": 200, "Order": 8}, {"ID": 152, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 999, "CurrencyType": 4, "Price": 10, "Order": 9}, {"ID": 153, "GroupId": 104, "Drop": [[101, 99]], "ExchangeNumber": 999, "CurrencyType": 4, "Price": 10, "Order": 10}]