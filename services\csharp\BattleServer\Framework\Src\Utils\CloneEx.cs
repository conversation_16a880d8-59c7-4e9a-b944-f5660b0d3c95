﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2023-3-20
//*********************************************************


using System;
using System.Buffers.Binary;
using System.Collections.Generic;
//由于客户端使用.net较低版本，这里重新实现做兼容

//namespace Aurora.Framework
//{
//    public static class CloneEx
//    {
//        public static List<T> Clone<T>(this List<T> source)
//        {
//            var neww = new List<T>(source);
//            return neww;
//        }
//        public static Dictionary<K, V> Clone<K, V>(this Dictionary<K, V> source)
//        {
//            var dic = new Dictionary<K, V>(source);
//            return dic;
//        }
//#if SERVER_FRAMEWORK
//        public static Unity.Mathematics.float3 Normalize(this Unity.Mathematics.float3 v3)
//        {
//            System.Numerics.Vector3 dir = new System.Numerics.Vector3(v3.x, v3.y, v3.z);
//            dir = System.Numerics.Vector3.Normalize(dir);
//            return new Unity.Mathematics.float3(dir.X, dir.Y, dir.Z);
//        }
//        public static System.Numerics.Vector3 Construct(this System.Numerics.Vector3 v3, double[] values)
//        {
//            v3.X = (float)values[0];
//            v3.Y = (float)values[1];
//            v3.Z = (float)values[2];
//            return v3;
//        }
//        public static System.Numerics.Vector3 Construct(this System.Numerics.Vector3 v3, Unity.Mathematics.float3 float3)
//        {
//            v3.X = float3[0];
//            v3.Y = float3[1];
//            v3.Z = float3[2];
//            return v3;
//        }
//        public static System.Numerics.Vector3 ToVector3(this Unity.Mathematics.float3 v3)
//        {
//            return new System.Numerics.Vector3(v3.x,v3.y,v3.z);
//        }
//        public static Unity.Mathematics.float3 ToFloat3(this System.Numerics.Vector3 v3)
//        {
//            return new Unity.Mathematics.float3(v3.X, v3.Y, v3.Z);
//        }
//#endif
//        public static void SafeAdd(this ref sbyte target, sbyte value)
//        {
//            if (value > (sbyte.MaxValue - target))
//            {
//                target = sbyte.MaxValue;
//            }
//            else
//            {
//                target += value;
//            }
//        }
//        public static void SafeAdd(this ref byte target, byte value)
//        {
//            if (value > (byte.MaxValue - target))
//            {
//                target = byte.MaxValue;
//            }
//            else
//            {
//                target += value;
//            }
//        }
//        public static void SafeAdd(this ref short target, short value)
//        {
//            if (value > (short.MaxValue - target))
//            {
//                target = short.MaxValue;
//            }
//            else
//            {
//                target += value;
//            }
//        }
//        public static void SafeAdd(this ref int target,int value)
//        {
//            if(value > (int.MaxValue - target))
//            {
//                target = int.MaxValue;
//            }
//            else
//            {
//                target += value;
//            }
//        }
//        public static void SafeAdd(this ref long target, long value)
//        {
//            if (value > (long.MaxValue - target))
//            {
//                target = long.MaxValue;
//            }
//            else
//            {
//                target += value;
//            }
//        }
//    }
//}
