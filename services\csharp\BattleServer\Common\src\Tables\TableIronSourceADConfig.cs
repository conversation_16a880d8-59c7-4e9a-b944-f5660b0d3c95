#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableIronSourceADConfig
	{

		public static readonly string TName="IronSourceADConfig.json";

		#region 属性定义
		/// <summary> 
		/// 广告类型 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// android广告位 
		/// </summary> 
		public string AndroidAdName {get; set;}
		/// <summary> 
		/// ios广告位 
		/// </summary> 
		public string IOSAdName {get; set;}
		/// <summary> 
		/// 道具的奖励id 
		/// </summary> 
		public string AwardItemId {get; set;}
		#endregion

		public static TableIronSourceADConfig GetData(int ID)
		{
			return TableManager.IronSourceADConfigData.Get(ID);
		}

		public static List<TableIronSourceADConfig> GetAllData()
		{
			return TableManager.IronSourceADConfigData.GetAll();
		}

	}
	public sealed partial class TableIronSourceADConfigData
	{
		private Dictionary<int, TableIronSourceADConfig> dict = new Dictionary<int, TableIronSourceADConfig>();
		private List<TableIronSourceADConfig> dataList = new List<TableIronSourceADConfig>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableIronSourceADConfig.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableIronSourceADConfig>>(jsonContent);
			foreach (TableIronSourceADConfig config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableIronSourceADConfig Get(int id)
		{
			if (dict.TryGetValue(id, out TableIronSourceADConfig item))
				return item;
			return null;
		}

		public List<TableIronSourceADConfig> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
