#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableHook
	{

		public static readonly string TName="Hook.json";

		#region 属性定义
		/// <summary> 
		/// Id 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 最小等级 
		/// </summary> 
		public int MinLevel {get; set;}
		/// <summary> 
		/// 最大等级 
		/// </summary> 
		public int MaxLevel {get; set;}
		/// <summary> 
		/// 掉落表id 
		/// </summary> 
		public int DropGroupId {get; set;}
		/// <summary> 
		/// 额外奖励次数 
		/// </summary> 
		public int ExtraTime {get; set;}
		/// <summary> 
		/// 额外奖励掉落表id 
		/// </summary> 
		public int ExtraDropGroupId {get; set;}
		/// <summary> 
		/// 收集时间上限(小时） 
		/// </summary> 
		public int TimeLimit {get; set;}
		#endregion

		public static TableHook GetData(int ID)
		{
			return TableManager.HookData.Get(ID);
		}

		public static List<TableHook> GetAllData()
		{
			return TableManager.HookData.GetAll();
		}

	}
	public sealed partial class TableHookData
	{
		private Dictionary<int, TableHook> dict = new Dictionary<int, TableHook>();
		private List<TableHook> dataList = new List<TableHook>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableHook.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableHook>>(jsonContent);
			foreach (TableHook config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableHook Get(int id)
		{
			if (dict.TryGetValue(id, out TableHook item))
				return item;
			return null;
		}

		public List<TableHook> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
