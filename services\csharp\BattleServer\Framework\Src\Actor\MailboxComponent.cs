﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-27
//*********************************************************
/*
namespace Aurora.Framework
{
    public class MailboxComponent : BaseComponent, IAwake
    {

    }

    [ComponentSystem(typeof(MailboxComponent))]
    public static class MailboxComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(MailboxComponent self)
        {

        }
    }
}
*/