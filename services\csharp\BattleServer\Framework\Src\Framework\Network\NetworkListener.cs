﻿using System;
using System.ComponentModel;
using System.Net;
using System.Net.Sockets;

namespace Aurora.Framework
{
	public class NetworkListenerComponent : BaseComponent, <PERSON><PERSON><PERSON>ke, IDestroy, IUpdate
	{
		public Socket m_Acceptor;
		public readonly SocketAsyncEventArgs m_AcceptArgs = new SocketAsyncEventArgs();

		public ThreadSyncContext m_SyncContext;

		public Action<TChannel> m_AcceptCallback;

		public void OnComplete(object sender, SocketAsyncEventArgs e)
		{
			if (e == null) return;
			switch (e.LastOperation)
			{
				case SocketAsyncOperation.Accept:
					SocketError socketError = e.SocketError;
					Socket acceptSocket = e.AcceptSocket;
					m_SyncContext.Post(() => { OnAcceptComplete(socketError, acceptSocket); });
					break;
				default:
					throw new Exception($"socket error: {e.LastOperation}");
			}
		}

		private void OnAccept(TChannel channel)
		{
			m_AcceptCallback.Invoke(channel);
		}

		private void OnAcceptComplete(SocketError socketError, Socket acceptSocket)
		{
			if (m_Acceptor == null)
				return;

			if (socketError != SocketError.Success)
			{
				Log.Error($"accept error: {socketError.ToString()}");
				return;
			}

			try
			{
				TChannel channel = new TChannel(acceptSocket);
				Log.Debug($"Accept Adress:{channel.RemoteAddress.ToString()} successful!");
				OnAccept(channel);
			}
			catch (Exception exception)
			{
				Log.Error($"Exception:{exception.ToString()} \nStackTrace:\n{exception.StackTrace}\n");
			}

			// 开始新的accept
			AcceptAsync();
		}

		public void AcceptAsync()
		{
			m_AcceptArgs.AcceptSocket = null;
			if (m_Acceptor.AcceptAsync(m_AcceptArgs))
			{
				return;
			}
			//同步完成
			OnAcceptComplete(m_AcceptArgs.SocketError, m_AcceptArgs.AcceptSocket);
		}
	}

	[ComponentSystem(typeof(NetworkListenerComponent))]
	public static class NetworkListenerSystem
    {
		[MethodAwake1]
		public static void OnAwake(NetworkListenerComponent self, IPEndPoint endPoint)
		{
            if (self == null) return;
			self.m_Acceptor = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
#if CLIENT_FRAMEWORK
			self.m_Acceptor.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
#endif
			self.m_AcceptArgs.Completed += self.OnComplete;
			try
			{
				self.m_Acceptor.Bind(endPoint);
				self.m_Acceptor.Listen(1000);
			}
			catch (Exception e)
			{
				Log.Error($"端口号被占用: Ip:{ endPoint.Address} Port:{ endPoint.Port }");
				throw new Exception($"NetworkListenerSystem, bInPortInUsed, InPort:{endPoint.Port}, Message:{e.Message}!");
			}

			Log.Info($"NetworkListenerComponent, Address:{endPoint.Address} Port:{endPoint.Port}");
			
			self.m_SyncContext = new ThreadSyncContext();
			self.m_SyncContext.Post(self.AcceptAsync);
		}

		[MethodDestroy]
		public static void OnDestroy(NetworkListenerComponent self)
		{
            if (self == null) return;
			self.m_Acceptor = null;
			self.m_SyncContext = null;
		}

		[MethodUpdate]
		public static void Update(NetworkListenerComponent self)
		{
			if (self == null) return;
			if (self.m_SyncContext == null)
			{
				Log.Error("NetworkListenerComponent's SyncContext is null!");
				return;
			}
			self.m_SyncContext.Update();
		}
	}
}
