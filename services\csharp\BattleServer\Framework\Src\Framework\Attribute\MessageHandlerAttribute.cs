﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-16
//*********************************************************

using System;

namespace Aurora.Framework
{
    //MsgHandler的函数标签
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class MsgAttribute : AuroraMethodAttribute
    {
        public Type MsgType { get; }

        public MsgAttribute(Type msgType)
        {
            MsgType = msgType;
        }
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public sealed class ActorMsgAttribute : AuroraMethodAttribute
    {
        public Type MsgType { get; }

        public ActorMsgAttribute(Type msgType)
        {
            MsgType = msgType;
        }
    }

}
