﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="src\protos\" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Google.Protobuf" Version="3.29.1" />
      <PackageReference Include="MemoryPack" Version="1.21.4" />
      <PackageReference Include="MemoryPack.Core" Version="1.21.4" />
      <PackageReference Include="MemoryPack.Generator" Version="1.21.4">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Framework\Framework.csproj" />
    </ItemGroup>

</Project>
