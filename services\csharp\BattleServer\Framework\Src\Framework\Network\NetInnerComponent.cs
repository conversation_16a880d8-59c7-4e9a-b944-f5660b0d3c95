﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-17
//*********************************************************

using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Runtime.CompilerServices;

namespace Aurora.Framework
{
    //内网组件，用于服务器之间内网通信
    public class NetInnerComponent : BaseComponent, IAwake, IDestroy
    {
        public static NetInnerComponent Instance;

        public Dictionary<int, Session> m_SessionList = new Dictionary<int, Session>();

        public NetInnerHelper NetInnerHelper;

        public static NetInnerComponent Create(IPEndPoint listen = null)
        {
            //TODO:以后可根据具体网络配置，如TCP、KCP、WS等
            NetInnerComponent comp = ReferencePool.Acquire<NetInnerComponent>();
            if (listen != null)
            {
                NetworkListenerComponent listener = comp.AddComponent<NetworkListenerComponent>(listen);
                listener.m_AcceptCallback += comp.OnAccept;
            }

            return comp;
        }
    }

    [ComponentSystem(typeof(NetInnerComponent))]
    public static class NetInnerComponentSystem
    {
        [MethodAwake]
        public static void OnAwake(NetInnerComponent self)
        {
            NetInnerComponent.Instance = self;

            //self.NetInnerHelper = innerHelper;
        }

        [MethodDestroy]
        public static void OnDestroy(NetInnerComponent self)
        {
            if (self == null) return;
            self.m_SessionList.Clear();
        }

        public static void OnAccept(this NetInnerComponent self, TChannel channel)
        {
            if (self == null) return;
            self.CreateSession(channel);
        }

        public static Session GetSession(this NetInnerComponent self, int nZwid, ushort nServerID)
        {
            if (self == null) return null;
            int key = nZwid * 1000 + nServerID;
            Session session;
            self.m_SessionList.TryGetValue(key, out session);
            //if (session == null)
            //{
            //    ServerConfig config = ServerOptions.Instance.GetServerConfig(nServerID);
            //    if(config == null)
            //    {
            //        throw new FrameworkException($"Create session Error,Process id {nServerID} is not in config");
            //    }
            //    //通过服务器配置创建内网Session
            //    IPAddress iPAddress = IPAddress.Parse(config.IP);
            //    IPEndPoint address = new IPEndPoint(iPAddress, config.InPort);
            //    session = self.CreateSession(nServerID, address);
            //}
            return session;
        }

        private static Session CreateSession(this NetInnerComponent self, int nZwid, ushort nServerID, IPEndPoint ipAdress)
        {
            TChannel channel = new TChannel(ipAdress);
            Session session = self.CreateSession(channel);

            int key = nZwid * 1000 + nServerID;
            self.m_SessionList.Add(key, session);

            return session;
        }

        public static Session CreateSession(this NetInnerComponent self, int nZwid, ushort nServerID, string ip, int port)
        {
            if (self == null) return null;
            IPAddress iPAddress = IPAddress.Parse(ip);
            IPEndPoint address = new IPEndPoint(iPAddress, port);

            return self.CreateSession(nZwid, nServerID, address);
        }

        private static Session CreateSession(this NetInnerComponent self, TChannel channel)
        {
            Session session = self.AddChild<Session>();
            EntitySystem.Awake(session, channel);
            session.m_ReadErrorCallback += self.OnSessionError;

            return session;
        }

        private static void DestroySession(this NetInnerComponent self, Session session)
        {
            foreach (var item in self.m_SessionList)
            {
                if (item.Value == session)
                {
                    self.m_SessionList.Remove(item.Key);
                    break;
                }
            }

            session.Release();
        }

        private static void OnSessionError(this NetInnerComponent self, Session session)
        {
            self.DestroySession(session);
        }

        public static bool GetInnerIpAndPort(this NetInnerComponent self, int nZwid, ushort serverID, out string ip, out int port)
        {
            ip = "";
            port = 0;
            if (self == null) return false;
            return self.NetInnerHelper.GetInnerIpAndPort(nZwid, serverID, out ip, out port);
        }

        public static void SetInnerHelper(this NetInnerComponent self, NetInnerHelper netInnerHelper)
        {
            if (self == null) return;
            self.NetInnerHelper = netInnerHelper;
        }
    }
}
