﻿using BattleServer.Framework;
using BattleServer.Game;
using BattleServer.Service;
using Game.Core;
using Aurora.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LiteFrame.Game
{
    public static partial class SceneHandler
    {
        [Msg(typeof(CreateSceneReq))]
        public static void CreateSceneReqHandler(Session session, CreateSceneReq request, CreateSceneResp response, Action reply)
        {
            ushort sceneID = SceneManager.Instance.CreateScene(request.SceneID, request.players, request.teams);
            if (sceneID == 0)
            {
                Log.Error($"CreateSceneReqHandler, Error, sceneID == {request.SceneID.ToString()}");

                reply();
                return;
            }

            response.SceneID = sceneID;
            Log.Debug($"CreateSceneReqHandler {Thread.CurrentThread.ManagedThreadId.ToString()}");

            reply();
        }


        [Msg(typeof(PlayerEnterSceneReq))]
        public static void PlayerEnterSceneReqHandler(Session session, PlayerEnterSceneReq request, PlayerEnterSceneResp response, Action reply)
        {
            ushort sceneID = SceneManager.Instance.GetPlayerSceneID(request.Uid);
            if (sceneID == 0)
            {
                Log.Error($"PlayerEnterSceneReqHandler, Error, sceneID == {request.Uid.ToString()}");

                reply();
                return;
            }

            Scene scene = SceneManager.Instance.GetScene(sceneID);
            scene.OnPlayerEnter(request.Uid);

            Log.Debug($"PlayerEnterSceneReqHandler {Thread.CurrentThread.ManagedThreadId.ToString()}");

            reply();
        }

        [Msg(typeof(PlayerSelectBufferReq))]
        public static void PlayerSelectBufferReqHandler(Session session, PlayerSelectBufferReq request, PlayerSelectBufferResp response, Action reply)
        {
            ushort sceneID = SceneManager.Instance.GetPlayerSceneID(request.Uid);
            if (sceneID == 0)
            {
                Log.Error($"PlayerSelectBufferReqHandler, Error, sceneID == {request.Uid.ToString()}");

                reply();
                return;
            }

            Scene scene = SceneManager.Instance.GetScene(sceneID);
            scene.OnPlayerSelectBuffer(request.Uid, request.BufferID);

            response.Code = 0;
            response.NewBoards = scene.GetPlayer(request.Uid).GetNewHeroBoard();

            Log.Debug($"PlayerSelectBufferReqHandler {Thread.CurrentThread.ManagedThreadId.ToString()}");

            reply();
        }

        [Msg(typeof(PlayerMergeHeroReq))]
        public static void PlayerMergeHeroReqHandler(Session session, PlayerMergeHeroReq request, PlayerMergeHeroResp response, Action reply)
        {
            ushort sceneID = SceneManager.Instance.GetPlayerSceneID(request.pbMsg.Uid);
            if (sceneID == 0)
            {
                Log.Error($"PlayerMergeHeroReqHandler, Error, sceneID == {request.pbMsg.Uid.ToString()}");

                reply();
                return;
            }


            Scene scene = SceneManager.Instance.GetScene(sceneID);
            scene.OnPlayerMoveHero(request.pbMsg.Uid, request.pbMsg.Moves.ToList());
            var newHero = scene.OnPlayerMergeHero(request.pbMsg.Uid, request.pbMsg.From, request.pbMsg.To);
            response.pbMsg = new MergeHeroResp();
            response.pbMsg.Code = 0;
            response.pbMsg.From = request.pbMsg.From;
            response.pbMsg.To = request.pbMsg.To;

            if (newHero == null)
            {
                Log.Error($"PlayerMergeHeroReqHandler new hero list is null");
            }
            else
            {
                foreach (var item in newHero)
                {
                    var board = new PBCheckerBoard();
                    board.GridID = item.Position;
                    board.Hero = item.ToPB();
                    response.pbMsg.NewHeros.Add(board);
                }
            }
                

            Log.Debug($"PlayerMergeHeroReqHandler {Thread.CurrentThread.ManagedThreadId.ToString()}");

            reply();
        }

        [Msg(typeof(PlayerReadyReq))]
        public static void PlayerReadyReqHandler(Session session, PlayerReadyReq request, PlayerReadyResp response, Action reply)
        {
            ushort sceneID = SceneManager.Instance.GetPlayerSceneID(request.Uid);
            if (sceneID == 0)
            {
                Log.Error($"PlayerReadyReqHandler, Error, sceneID == {request.Uid.ToString()}");

                reply();
                return;
            }


            Scene scene = SceneManager.Instance.GetScene(sceneID);
            scene.OnPlayerMoveHero(request.Uid, request.moveOperation);
            scene.OnPlayerReady(request.Uid);
            Log.Debug($"PlayerReadyReqHandler {Thread.CurrentThread.ManagedThreadId.ToString()}");

            reply();
        }

        [Msg(typeof(PlayerEndBattleReq))]
        public static void PlayerEndBattleReqHandler(Session session, PlayerEndBattleReq request, PlayerEndBattleResp response, Action reply)
        {
            ushort sceneID = SceneManager.Instance.GetPlayerSceneID(request.Uid);
            if (sceneID == 0)
            {
                Log.Error($"PlayerEndBattleReqHandler, Error, sceneID == {request.Uid.ToString()}");

                reply();
                return;
            }


            Scene scene = SceneManager.Instance.GetScene(sceneID);
            scene.OnPlayerBattleEnd(request.Uid, request.Win);
            Log.Debug($"PlayerEndBattleReqHandler {Thread.CurrentThread.ManagedThreadId.ToString()}");

            reply();
        }
    }
}
