﻿//*********************************************************
// Framework
// Author:  Jasen 
// Date  :  2022-10-24
//*********************************************************

using System;
using System.Globalization;
using System.Text;
using System.Threading;

namespace Aurora.Framework
{
    //文本辅助器
    public class TextHelper
    {
        //使用StringBuilder，避免中间字符串产生GC
        private const int DefaultBuilderSize = 512;
        //默认每个线程一个
        //[ThreadStatic]
        //private static StringBuilder g_StringBuilder = new StringBuilder(DefaultBuilderSize);
        private static ThreadLocal<StringBuilder> s_StringBuild = new ThreadLocal<StringBuilder>(() => { return new StringBuilder(DefaultBuilderSize); }, false);
        private static StringBuilder g_StringBuilder
        {
            get
            {
                return s_StringBuild.Value;
            }
            set
            {
                s_StringBuild.Value = value;
            }
        }

        public static string Format<T>(string format, T arg)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg);
            return g_StringBuilder.ToString();
        }

        public static string Format<T1, T2>(string format, T1 arg1, T2 arg2)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg1, arg2);
            return g_StringBuilder.ToString();
        }

        public static string Format<T1, T2, T3>(string format, T1 arg1, T2 arg2, T3 arg3)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg1, arg2, arg3);
            return g_StringBuilder.ToString();
        }

        public static string Format<T1, T2, T3, T4>(string format, T1 arg1, T2 arg2, T3 arg3, T4 arg4)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg1, arg2, arg3, arg4);
            return g_StringBuilder.ToString();
        }

        public static string Format<T1, T2, T3, T4, T5>(string format, T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg1, arg2, arg3, arg4, arg5);
            return g_StringBuilder.ToString();
        }

        public static string Format<T1, T2, T3, T4, T5, T6>(string format, T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5, T6 arg6)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg1, arg2, arg3, arg4, arg5, arg6);
            return g_StringBuilder.ToString();
        }

        public static string Format<T1, T2, T3, T4, T5, T6, T7>(string format, T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5, T6 arg6, T7 arg7)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
            return g_StringBuilder.ToString();
        }

        public static string Format<T1, T2, T3, T4, T5, T6, T7, T8>(string format, T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5, T6 arg6, T7 arg7, T8 arg8)
        {
            if (format == null)
            {
                throw new FrameworkException("TextHelper:Format is invalid.");
            }
            g_StringBuilder.Length = 0;
            g_StringBuilder.AppendFormat(CultureInfo.InvariantCulture, format, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
            return g_StringBuilder.ToString();
        }
        //按需添加
    }
}

