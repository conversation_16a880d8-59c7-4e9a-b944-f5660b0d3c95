﻿/*
 * @description: 留空,用于Assembly加载程序集
 * @Auther: Kimsee 
 * @Date: 2022-11-04 22:01
 * 
*/
//using Apache.Ignite.Core.Events;
using Aurora.Framework;
using System.Reflection;
using System.Collections.Concurrent;

namespace Aurora.Framework
{
    public class ModuleManager : Singleton<ModuleManager>
    {
        public ConcurrentDictionary<string, IModuleRunnable> m_RunnableModuleList = new ConcurrentDictionary<string, IModuleRunnable>();
        // 全局访问各个模块的入口,省得每次用的时候去取
        // 通用的ConfigManager和Log这种就不在这里，放到全局的common里访问会方便些
        //public ITaskManager m_ITaskMgr;
        //public IEventBus m_IEventBus;


        // 如果不需要在主线程驱动的,loopInMainThread传false
        /* public bool AddModule<T>(T obj, bool loopInMainThread = true)
         {
             FieldInfo[] fields = typeof(ModuleHelper).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.Instance);
             FieldInfo foundFieldInfo = null;
             foreach (FieldInfo field in fields)
             {
                 if (field.FieldType == typeof(T))
                 {
                     foundFieldInfo = field;
                 }
             }
             if (null == foundFieldInfo)
             {
                 throw new Exception($" ModuleHelper.AddModule failed. Type[{typeof(T).Name}] not found in ModuleHelper !!");
             }
             foundFieldInfo.SetValue(ModuleHelper.Inst, obj);

             // 添加到主线程驱动
             if (loopInMainThread)
             {
                 if (typeof(IModuleRunnable).IsAssignableFrom(obj.GetType()))
                 {
                     IModuleRunnable runnable = obj as IModuleRunnable;
                     if (m_RunnableModuleList.Contains(runnable))
                     {
                         throw new Exception($" ModuleHelper.AddModule failed. Type[{typeof(T).Name}] duplicated in m_RunnableModuleList !!");
                     }
                     m_RunnableModuleList.Add(runnable);
                 }
             }
             return true;
         }

         // 整理下需要tick的Module
         public bool Init()
         {
             //AddModule<ITaskManager>(SingletonManager.m_AllSingletonList["TaskManager"] as ITaskManager);
             //AddModule<IEventBus>(SingletonManager.m_AllSingletonList["EventBus"] as IEventBus);
             //AddModule<IDBManager>(SingletonManager.m_AllSingletonList["DBManager"] as IDBManager);



             return true;
         }*/

    }
}