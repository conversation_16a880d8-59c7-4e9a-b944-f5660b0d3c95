﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using Aurora.Framework;

//namespace BattleServer.Game
//{
//    public class ChessCell
//    {
//        public int Row { get; set; }
//        public int Col { get; set; }
//        //public ChessPiece? Piece { get; set; }
//        public bool IsWalkable { get; set; } = true;

//        public ChessCell(int row, int col)
//        {
//            Row = row;
//            Col = col;
//        }

//        //public bool IsEmpty => Piece == null;

//        public Vector2Int Position => new Vector2Int(Row, Col);
//    }

//    /// <summary>
//    /// 坐标结构体
//    /// </summary>
//    public struct Vector2Int
//    {
//        public int X { get; set; }
//        public int Y { get; set; }

//        public Vector2Int(int x, int y)
//        {
//            X = x;
//            Y = y;
//        }

//        public float Distance(Vector2Int other)
//        {
//            return (float)Math.Sqrt(Math.Pow(X - other.X, 2) + Math.Pow(Y - other.Y, 2));
//        }
//    }


//    // 棋盘定义
//    public class ChessBoard
//    {
//        private int mID;
//        private int mRows;
//        private int mCols;
//        private ChessCell[,] mCells;

//        // 对站玩家
//        private BattlePlayer[] mPlayers = new BattlePlayer[2];

//        private Dictionary<ulong, bool> mBattleResult = new Dictionary<ulong, bool>();


//        public ChessBoard(int id, int rows = 10, int cols = 6)
//        {
//            mID = id;
//            mRows = rows;
//            mCols = cols;
//            mCells = new ChessCell[rows, cols];

//            InitializeBoard();
//        }

//        private void InitializeBoard()
//        {
//            for (int row = 0; row < mRows; row++)
//            {
//                for (int col = 0; col < mCols; col++)
//                {
//                    mCells[row, col] = new ChessCell(row, col);
//                }
//            }
//        }

//        public bool IsValidPosition(int row, int col)
//        {
//            return row >= 0 && row < mRows && col >= 0 && col < mCols;
//        }

//        public bool IsValidPosition(Vector2Int pos)
//        {
//            return IsValidPosition(pos.X, pos.Y);
//        }

//        // 设置战斗玩家
//        public void SetPlayers(BattlePlayer[] players)
//        {
//            if (players == null || players.Length < 2)
//            {
//                Log.Error($"ChessBoard set players error: ");
//                return;
//            }

//            mPlayers[0] = players[0];
//            mPlayers[1] = players[1];

//            Log.Debug($"chessboard {mID} set player[0] {players[0].Info.Uid} player[1] {players[1].Info.Uid}");
//        }

//        public void OnBattleEnd(ulong uid, bool win)
//        {
//            if (mBattleResult.ContainsKey(uid))
//            {
//                Log.Error($"on battle end uid {uid} has updated result");
//                return;
//            }

//            mBattleResult[uid] = win;
//        }


//        public bool IsBattleEnd()
//        {
//            return mBattleResult.Count > 2;
//        }

//        public void OnUpdate()
//        {

//        }
//    }
//}
