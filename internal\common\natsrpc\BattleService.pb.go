// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.2
// source: BattleService.proto

package natsrpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	public "liteframe/internal/common/protos/public"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 创建战斗信息
type PBCreateBattleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Players []*public.PBBattlePlayerInfo `protobuf:"bytes,1,rep,name=players,proto3" json:"players,omitempty"` // 玩家信息
	Teams   []*public.PBBattleTeamInfo   `protobuf:"bytes,2,rep,name=teams,proto3" json:"teams,omitempty"`     // 阵容信息
}

func (x *PBCreateBattleInfo) Reset() {
	*x = PBCreateBattleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PBCreateBattleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PBCreateBattleInfo) ProtoMessage() {}

func (x *PBCreateBattleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PBCreateBattleInfo.ProtoReflect.Descriptor instead.
func (*PBCreateBattleInfo) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{0}
}

func (x *PBCreateBattleInfo) GetPlayers() []*public.PBBattlePlayerInfo {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *PBCreateBattleInfo) GetTeams() []*public.PBBattleTeamInfo {
	if x != nil {
		return x.Teams
	}
	return nil
}

type CreateBattleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateInfo *PBCreateBattleInfo `protobuf:"bytes,1,opt,name=createInfo,proto3" json:"createInfo,omitempty"`
}

func (x *CreateBattleReq) Reset() {
	*x = CreateBattleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBattleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBattleReq) ProtoMessage() {}

func (x *CreateBattleReq) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBattleReq.ProtoReflect.Descriptor instead.
func (*CreateBattleReq) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBattleReq) GetCreateInfo() *PBCreateBattleInfo {
	if x != nil {
		return x.CreateInfo
	}
	return nil
}

type CreateBattleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	ServerId int64 `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`
	BattleId int64 `protobuf:"varint,3,opt,name=battleId,proto3" json:"battleId,omitempty"`
}

func (x *CreateBattleResp) Reset() {
	*x = CreateBattleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBattleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBattleResp) ProtoMessage() {}

func (x *CreateBattleResp) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBattleResp.ProtoReflect.Descriptor instead.
func (*CreateBattleResp) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{2}
}

func (x *CreateBattleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateBattleResp) GetServerId() int64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *CreateBattleResp) GetBattleId() int64 {
	if x != nil {
		return x.BattleId
	}
	return 0
}

type EnterBattleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 玩家ID
}

func (x *EnterBattleReq) Reset() {
	*x = EnterBattleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterBattleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterBattleReq) ProtoMessage() {}

func (x *EnterBattleReq) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterBattleReq.ProtoReflect.Descriptor instead.
func (*EnterBattleReq) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{3}
}

func (x *EnterBattleReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type EnterBattleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *EnterBattleResp) Reset() {
	*x = EnterBattleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterBattleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterBattleResp) ProtoMessage() {}

func (x *EnterBattleResp) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterBattleResp.ProtoReflect.Descriptor instead.
func (*EnterBattleResp) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{4}
}

func (x *EnterBattleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type SelectBufferReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 玩家ID
	BufferID int32  `protobuf:"varint,2,opt,name=bufferID,proto3" json:"bufferID,omitempty"`
}

func (x *SelectBufferReq) Reset() {
	*x = SelectBufferReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectBufferReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectBufferReq) ProtoMessage() {}

func (x *SelectBufferReq) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectBufferReq.ProtoReflect.Descriptor instead.
func (*SelectBufferReq) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{5}
}

func (x *SelectBufferReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *SelectBufferReq) GetBufferID() int32 {
	if x != nil {
		return x.BufferID
	}
	return 0
}

type SelectBufferResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	NewHeroes []*public.PBCheckerBoard `protobuf:"bytes,2,rep,name=newHeroes,proto3" json:"newHeroes,omitempty"` // 选择Buff后，本回合新生成的英雄信息
}

func (x *SelectBufferResp) Reset() {
	*x = SelectBufferResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectBufferResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectBufferResp) ProtoMessage() {}

func (x *SelectBufferResp) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectBufferResp.ProtoReflect.Descriptor instead.
func (*SelectBufferResp) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{6}
}

func (x *SelectBufferResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SelectBufferResp) GetNewHeroes() []*public.PBCheckerBoard {
	if x != nil {
		return x.NewHeroes
	}
	return nil
}

type MergeHeroReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   uint64                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`    // 玩家ID
	From  int32                     `protobuf:"varint,2,opt,name=from,proto3" json:"from,omitempty"`  // 合成源格子ID
	To    int32                     `protobuf:"varint,3,opt,name=to,proto3" json:"to,omitempty"`      // 合成目标格子ID
	Moves []*public.PBMoveOperation `protobuf:"bytes,4,rep,name=moves,proto3" json:"moves,omitempty"` // 本次合成前发生的所有移动操作
}

func (x *MergeHeroReq) Reset() {
	*x = MergeHeroReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeHeroReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeHeroReq) ProtoMessage() {}

func (x *MergeHeroReq) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeHeroReq.ProtoReflect.Descriptor instead.
func (*MergeHeroReq) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{7}
}

func (x *MergeHeroReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *MergeHeroReq) GetFrom() int32 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *MergeHeroReq) GetTo() int32 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *MergeHeroReq) GetMoves() []*public.PBMoveOperation {
	if x != nil {
		return x.Moves
	}
	return nil
}

type MergeHeroResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	From     int32                    `protobuf:"varint,2,opt,name=from,proto3" json:"from,omitempty"`        //合成源格子ID
	To       int32                    `protobuf:"varint,3,opt,name=to,proto3" json:"to,omitempty"`            //合成目标格子ID
	NewHeros []*public.PBCheckerBoard `protobuf:"bytes,4,rep,name=newHeros,proto3" json:"newHeros,omitempty"` // 合成的新英雄数据
}

func (x *MergeHeroResp) Reset() {
	*x = MergeHeroResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeHeroResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeHeroResp) ProtoMessage() {}

func (x *MergeHeroResp) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeHeroResp.ProtoReflect.Descriptor instead.
func (*MergeHeroResp) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{8}
}

func (x *MergeHeroResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MergeHeroResp) GetFrom() int32 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *MergeHeroResp) GetTo() int32 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *MergeHeroResp) GetNewHeros() []*public.PBCheckerBoard {
	if x != nil {
		return x.NewHeros
	}
	return nil
}

//准备
type ReadyBattleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   uint64                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`    // 玩家ID
	Moves []*public.PBMoveOperation `protobuf:"bytes,2,rep,name=moves,proto3" json:"moves,omitempty"` // 确认准备前要同步的移动操作
}

func (x *ReadyBattleReq) Reset() {
	*x = ReadyBattleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadyBattleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadyBattleReq) ProtoMessage() {}

func (x *ReadyBattleReq) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadyBattleReq.ProtoReflect.Descriptor instead.
func (*ReadyBattleReq) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{9}
}

func (x *ReadyBattleReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *ReadyBattleReq) GetMoves() []*public.PBMoveOperation {
	if x != nil {
		return x.Moves
	}
	return nil
}

//准备
type ReadyBattleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ReadyBattleResp) Reset() {
	*x = ReadyBattleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadyBattleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadyBattleResp) ProtoMessage() {}

func (x *ReadyBattleResp) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadyBattleResp.ProtoReflect.Descriptor instead.
func (*ReadyBattleResp) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{10}
}

func (x *ReadyBattleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type EndBattleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 玩家ID
	Win bool   `protobuf:"varint,2,opt,name=win,proto3" json:"win,omitempty"`
}

func (x *EndBattleReq) Reset() {
	*x = EndBattleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndBattleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndBattleReq) ProtoMessage() {}

func (x *EndBattleReq) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndBattleReq.ProtoReflect.Descriptor instead.
func (*EndBattleReq) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{11}
}

func (x *EndBattleReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *EndBattleReq) GetWin() bool {
	if x != nil {
		return x.Win
	}
	return false
}

type EndBattleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *EndBattleResp) Reset() {
	*x = EndBattleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndBattleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndBattleResp) ProtoMessage() {}

func (x *EndBattleResp) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndBattleResp.ProtoReflect.Descriptor instead.
func (*EndBattleResp) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{12}
}

func (x *EndBattleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

// 离开战斗请求
type LeaveBattleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 玩家ID
}

func (x *LeaveBattleReq) Reset() {
	*x = LeaveBattleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveBattleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveBattleReq) ProtoMessage() {}

func (x *LeaveBattleReq) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveBattleReq.ProtoReflect.Descriptor instead.
func (*LeaveBattleReq) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{13}
}

func (x *LeaveBattleReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// 离开战斗响应
type LeaveBattleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 响应码：0=成功，其他=失败
}

func (x *LeaveBattleResp) Reset() {
	*x = LeaveBattleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleService_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveBattleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveBattleResp) ProtoMessage() {}

func (x *LeaveBattleResp) ProtoReflect() protoreflect.Message {
	mi := &file_BattleService_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveBattleResp.ProtoReflect.Descriptor instead.
func (*LeaveBattleResp) Descriptor() ([]byte, []int) {
	return file_BattleService_proto_rawDescGZIP(), []int{14}
}

func (x *LeaveBattleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_BattleService_proto protoreflect.FileDescriptor

var file_BattleService_proto_rawDesc = []byte{
	0x0a, 0x13, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x1a, 0x10,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6c, 0x0a, 0x12, 0x50, 0x42, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a,
	0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x50, 0x42, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x27, 0x0a, 0x05,
	0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x50, 0x42,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05,
	0x74, 0x65, 0x61, 0x6d, 0x73, 0x22, 0x4e, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6e,
	0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x42, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5e, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x22, 0x0a, 0x0e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x25, 0x0a, 0x0f, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x3f, 0x0a, 0x0f, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x44, 0x22, 0x55, 0x0a, 0x10, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x42, 0x75, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x09, 0x6e, 0x65, 0x77,
	0x48, 0x65, 0x72, 0x6f, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x50,
	0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x09, 0x6e,
	0x65, 0x77, 0x48, 0x65, 0x72, 0x6f, 0x65, 0x73, 0x22, 0x6c, 0x0a, 0x0c, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x26,
	0x0a, 0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x50, 0x42, 0x4d, 0x6f, 0x76, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x22, 0x74, 0x0a, 0x0d, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x48,
	0x65, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x74, 0x6f, 0x12,
	0x2b, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x48, 0x65, 0x72, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x50, 0x42, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x48, 0x65, 0x72, 0x6f, 0x73, 0x22, 0x4a, 0x0a, 0x0e,
	0x52, 0x65, 0x61, 0x64, 0x79, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x50, 0x42, 0x4d, 0x6f, 0x76, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x05, 0x6d, 0x6f, 0x76, 0x65, 0x73, 0x22, 0x25, 0x0a, 0x0f, 0x52, 0x65, 0x61, 0x64,
	0x79, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0x32, 0x0a, 0x0c, 0x45, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03,
	0x77, 0x69, 0x6e, 0x22, 0x23, 0x0a, 0x0d, 0x45, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x22, 0x0a, 0x0e, 0x4c, 0x65, 0x61, 0x76,
	0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x25, 0x0a, 0x0f,
	0x4c, 0x65, 0x61, 0x76, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x32, 0xfb, 0x03, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x19, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x0b, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x17, 0x2e, 0x6e, 0x61, 0x74, 0x73,
	0x72, 0x70, 0x63, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x04, 0x80, 0xb5,
	0x18, 0x01, 0x12, 0x49, 0x0a, 0x0c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x42, 0x75, 0x66, 0x66,
	0x65, 0x72, 0x12, 0x18, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x6e,
	0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x42, 0x75, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x12, 0x40, 0x0a,
	0x09, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x48, 0x65, 0x72, 0x6f, 0x12, 0x15, 0x2e, 0x6e, 0x61, 0x74,
	0x73, 0x72, 0x70, 0x63, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x4d, 0x65, 0x72, 0x67,
	0x65, 0x48, 0x65, 0x72, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x12,
	0x46, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x17,
	0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x79, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70,
	0x63, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x79, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x12, 0x40, 0x0a, 0x09, 0x45, 0x6e, 0x64, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x12, 0x15, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x45,
	0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x6e, 0x61,
	0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6e, 0x64, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x04, 0x80, 0xb5, 0x18, 0x01, 0x12, 0x46, 0x0a, 0x0b, 0x4c, 0x65, 0x61,
	0x76, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x17, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72,
	0x70, 0x63, 0x2e, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x18, 0x2e, 0x6e, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2e, 0x4c, 0x65, 0x61, 0x76,
	0x65, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x04, 0x80, 0xb5, 0x18,
	0x01, 0x42, 0x3a, 0x5a, 0x21, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e,
	0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0xaa, 0x02, 0x14, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_BattleService_proto_rawDescOnce sync.Once
	file_BattleService_proto_rawDescData = file_BattleService_proto_rawDesc
)

func file_BattleService_proto_rawDescGZIP() []byte {
	file_BattleService_proto_rawDescOnce.Do(func() {
		file_BattleService_proto_rawDescData = protoimpl.X.CompressGZIP(file_BattleService_proto_rawDescData)
	})
	return file_BattleService_proto_rawDescData
}

var file_BattleService_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_BattleService_proto_goTypes = []interface{}{
	(*PBCreateBattleInfo)(nil),        // 0: natsrpc.PBCreateBattleInfo
	(*CreateBattleReq)(nil),           // 1: natsrpc.CreateBattleReq
	(*CreateBattleResp)(nil),          // 2: natsrpc.CreateBattleResp
	(*EnterBattleReq)(nil),            // 3: natsrpc.EnterBattleReq
	(*EnterBattleResp)(nil),           // 4: natsrpc.EnterBattleResp
	(*SelectBufferReq)(nil),           // 5: natsrpc.SelectBufferReq
	(*SelectBufferResp)(nil),          // 6: natsrpc.SelectBufferResp
	(*MergeHeroReq)(nil),              // 7: natsrpc.MergeHeroReq
	(*MergeHeroResp)(nil),             // 8: natsrpc.MergeHeroResp
	(*ReadyBattleReq)(nil),            // 9: natsrpc.ReadyBattleReq
	(*ReadyBattleResp)(nil),           // 10: natsrpc.ReadyBattleResp
	(*EndBattleReq)(nil),              // 11: natsrpc.EndBattleReq
	(*EndBattleResp)(nil),             // 12: natsrpc.EndBattleResp
	(*LeaveBattleReq)(nil),            // 13: natsrpc.LeaveBattleReq
	(*LeaveBattleResp)(nil),           // 14: natsrpc.LeaveBattleResp
	(*public.PBBattlePlayerInfo)(nil), // 15: PBBattlePlayerInfo
	(*public.PBBattleTeamInfo)(nil),   // 16: PBBattleTeamInfo
	(*public.PBCheckerBoard)(nil),     // 17: PBCheckerBoard
	(*public.PBMoveOperation)(nil),    // 18: PBMoveOperation
}
var file_BattleService_proto_depIdxs = []int32{
	15, // 0: natsrpc.PBCreateBattleInfo.players:type_name -> PBBattlePlayerInfo
	16, // 1: natsrpc.PBCreateBattleInfo.teams:type_name -> PBBattleTeamInfo
	0,  // 2: natsrpc.CreateBattleReq.createInfo:type_name -> natsrpc.PBCreateBattleInfo
	17, // 3: natsrpc.SelectBufferResp.newHeroes:type_name -> PBCheckerBoard
	18, // 4: natsrpc.MergeHeroReq.moves:type_name -> PBMoveOperation
	17, // 5: natsrpc.MergeHeroResp.newHeros:type_name -> PBCheckerBoard
	18, // 6: natsrpc.ReadyBattleReq.moves:type_name -> PBMoveOperation
	1,  // 7: natsrpc.BattleService.CreateBattle:input_type -> natsrpc.CreateBattleReq
	3,  // 8: natsrpc.BattleService.EnterBattle:input_type -> natsrpc.EnterBattleReq
	5,  // 9: natsrpc.BattleService.SelectBuffer:input_type -> natsrpc.SelectBufferReq
	7,  // 10: natsrpc.BattleService.MergeHero:input_type -> natsrpc.MergeHeroReq
	9,  // 11: natsrpc.BattleService.BattleReady:input_type -> natsrpc.ReadyBattleReq
	11, // 12: natsrpc.BattleService.EndBattle:input_type -> natsrpc.EndBattleReq
	13, // 13: natsrpc.BattleService.LeaveBattle:input_type -> natsrpc.LeaveBattleReq
	2,  // 14: natsrpc.BattleService.CreateBattle:output_type -> natsrpc.CreateBattleResp
	4,  // 15: natsrpc.BattleService.EnterBattle:output_type -> natsrpc.EnterBattleResp
	6,  // 16: natsrpc.BattleService.SelectBuffer:output_type -> natsrpc.SelectBufferResp
	8,  // 17: natsrpc.BattleService.MergeHero:output_type -> natsrpc.MergeHeroResp
	10, // 18: natsrpc.BattleService.BattleReady:output_type -> natsrpc.ReadyBattleResp
	12, // 19: natsrpc.BattleService.EndBattle:output_type -> natsrpc.EndBattleResp
	14, // 20: natsrpc.BattleService.LeaveBattle:output_type -> natsrpc.LeaveBattleResp
	14, // [14:21] is the sub-list for method output_type
	7,  // [7:14] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_BattleService_proto_init() }
func file_BattleService_proto_init() {
	if File_BattleService_proto != nil {
		return
	}
	file_descriptor_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_BattleService_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PBCreateBattleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBattleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBattleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterBattleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterBattleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectBufferReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectBufferResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeHeroReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeHeroResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadyBattleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadyBattleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndBattleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndBattleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveBattleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleService_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveBattleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_BattleService_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_BattleService_proto_goTypes,
		DependencyIndexes: file_BattleService_proto_depIdxs,
		MessageInfos:      file_BattleService_proto_msgTypes,
	}.Build()
	File_BattleService_proto = out.File
	file_BattleService_proto_rawDesc = nil
	file_BattleService_proto_goTypes = nil
	file_BattleService_proto_depIdxs = nil
}
