// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.23.2
// source: BattleProto.proto

package cs

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//战斗数据
type PbBattle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      int64       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`            //战斗唯一ID
	Type     int32       `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`          //战斗类型,可改统一枚举
	Seed     int32       `protobuf:"varint,3,opt,name=seed,proto3" json:"seed,omitempty"`          //随机种子
	Frame    int32       `protobuf:"varint,4,opt,name=frame,proto3" json:"frame,omitempty"`        //当前逻辑帧
	AccUid   int32       `protobuf:"varint,5,opt,name=accUid,proto3" json:"accUid,omitempty"`      //实体递增唯一ID
	Map      string      `protobuf:"bytes,6,opt,name=map,proto3" json:"map,omitempty"`             //地图
	Width    int32       `protobuf:"varint,7,opt,name=width,proto3" json:"width,omitempty"`        //地图宽
	Height   int32       `protobuf:"varint,8,opt,name=height,proto3" json:"height,omitempty"`      //地图高
	GridSize float32     `protobuf:"fixed32,9,opt,name=gridSize,proto3" json:"gridSize,omitempty"` //格子尺寸
	TeamId   int32       `protobuf:"varint,10,opt,name=teamId,proto3" json:"teamId,omitempty"`     //0下方 1 上
	Entities []*PbEntity `protobuf:"bytes,11,rep,name=entities,proto3" json:"entities,omitempty"`  //实体数组
}

func (x *PbBattle) Reset() {
	*x = PbBattle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbBattle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbBattle) ProtoMessage() {}

func (x *PbBattle) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbBattle.ProtoReflect.Descriptor instead.
func (*PbBattle) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{0}
}

func (x *PbBattle) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PbBattle) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *PbBattle) GetSeed() int32 {
	if x != nil {
		return x.Seed
	}
	return 0
}

func (x *PbBattle) GetFrame() int32 {
	if x != nil {
		return x.Frame
	}
	return 0
}

func (x *PbBattle) GetAccUid() int32 {
	if x != nil {
		return x.AccUid
	}
	return 0
}

func (x *PbBattle) GetMap() string {
	if x != nil {
		return x.Map
	}
	return ""
}

func (x *PbBattle) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *PbBattle) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *PbBattle) GetGridSize() float32 {
	if x != nil {
		return x.GridSize
	}
	return 0
}

func (x *PbBattle) GetTeamId() int32 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *PbBattle) GetEntities() []*PbEntity {
	if x != nil {
		return x.Entities
	}
	return nil
}

//实体
type PbEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid       int32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`            //实体唯一ID
	Dirty     bool          `protobuf:"varint,2,opt,name=dirty,proto3" json:"dirty,omitempty"`        //状态
	BaseInfo  *PbEntityBase `protobuf:"bytes,3,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`   //基础信息
	Transform *PbTransform  `protobuf:"bytes,4,opt,name=transform,proto3" json:"transform,omitempty"` //位置信息
	Attribute *PbAttribute  `protobuf:"bytes,5,opt,name=attribute,proto3" json:"attribute,omitempty"` //属性信息
	State     *PbState      `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`         //状态信息
	Behavior  *PbBehavior   `protobuf:"bytes,7,opt,name=behavior,proto3" json:"behavior,omitempty"`   //行为信息
	Way       *PbWayPoint   `protobuf:"bytes,8,opt,name=way,proto3" json:"way,omitempty"`             //寻路点信息
}

func (x *PbEntity) Reset() {
	*x = PbEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbEntity) ProtoMessage() {}

func (x *PbEntity) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbEntity.ProtoReflect.Descriptor instead.
func (*PbEntity) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{1}
}

func (x *PbEntity) GetUid() int32 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PbEntity) GetDirty() bool {
	if x != nil {
		return x.Dirty
	}
	return false
}

func (x *PbEntity) GetBaseInfo() *PbEntityBase {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *PbEntity) GetTransform() *PbTransform {
	if x != nil {
		return x.Transform
	}
	return nil
}

func (x *PbEntity) GetAttribute() *PbAttribute {
	if x != nil {
		return x.Attribute
	}
	return nil
}

func (x *PbEntity) GetState() *PbState {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *PbEntity) GetBehavior() *PbBehavior {
	if x != nil {
		return x.Behavior
	}
	return nil
}

func (x *PbEntity) GetWay() *PbWayPoint {
	if x != nil {
		return x.Way
	}
	return nil
}

//基础信息
type PbEntityBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId int32 `protobuf:"varint,1,opt,name=teamId,proto3" json:"teamId,omitempty"` //队伍ID
	HeroId int32 `protobuf:"varint,2,opt,name=heroId,proto3" json:"heroId,omitempty"` //英雄ID
	Level  int32 `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`   //等级
	Star   int32 `protobuf:"varint,4,opt,name=star,proto3" json:"star,omitempty"`     //星级
	Evo    int32 `protobuf:"varint,5,opt,name=evo,proto3" json:"evo,omitempty"`       //觉醒等级
}

func (x *PbEntityBase) Reset() {
	*x = PbEntityBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbEntityBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbEntityBase) ProtoMessage() {}

func (x *PbEntityBase) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbEntityBase.ProtoReflect.Descriptor instead.
func (*PbEntityBase) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{2}
}

func (x *PbEntityBase) GetTeamId() int32 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *PbEntityBase) GetHeroId() int32 {
	if x != nil {
		return x.HeroId
	}
	return 0
}

func (x *PbEntityBase) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PbEntityBase) GetStar() int32 {
	if x != nil {
		return x.Star
	}
	return 0
}

func (x *PbEntityBase) GetEvo() int32 {
	if x != nil {
		return x.Evo
	}
	return 0
}

//位置信息
type PbTransform struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X float32 `protobuf:"fixed32,1,opt,name=x,proto3" json:"x,omitempty"`
	Y float32 `protobuf:"fixed32,2,opt,name=y,proto3" json:"y,omitempty"`
}

func (x *PbTransform) Reset() {
	*x = PbTransform{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbTransform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbTransform) ProtoMessage() {}

func (x *PbTransform) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbTransform.ProtoReflect.Descriptor instead.
func (*PbTransform) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{3}
}

func (x *PbTransform) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *PbTransform) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

//属性信息
type PbAttribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttrBase  []int32   `protobuf:"varint,1,rep,packed,name=attrBase,proto3" json:"attrBase,omitempty"`    //一级属性
	AttrValue []float32 `protobuf:"fixed32,2,rep,packed,name=attrValue,proto3" json:"attrValue,omitempty"` //二级属性
}

func (x *PbAttribute) Reset() {
	*x = PbAttribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbAttribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbAttribute) ProtoMessage() {}

func (x *PbAttribute) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbAttribute.ProtoReflect.Descriptor instead.
func (*PbAttribute) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{4}
}

func (x *PbAttribute) GetAttrBase() []int32 {
	if x != nil {
		return x.AttrBase
	}
	return nil
}

func (x *PbAttribute) GetAttrValue() []float32 {
	if x != nil {
		return x.AttrValue
	}
	return nil
}

//状态信息
type PbState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`   //基础状态信息 idle,move,attak,die
	Action  int32 `protobuf:"varint,2,opt,name=action,proto3" json:"action,omitempty"`   //基础行动状态
	Control int32 `protobuf:"varint,3,opt,name=control,proto3" json:"control,omitempty"` //控制状态
	Effect  int32 `protobuf:"varint,4,opt,name=effect,proto3" json:"effect,omitempty"`   //其他状态
}

func (x *PbState) Reset() {
	*x = PbState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbState) ProtoMessage() {}

func (x *PbState) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbState.ProtoReflect.Descriptor instead.
func (*PbState) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{5}
}

func (x *PbState) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PbState) GetAction() int32 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *PbState) GetControl() int32 {
	if x != nil {
		return x.Control
	}
	return 0
}

func (x *PbState) GetEffect() int32 {
	if x != nil {
		return x.Effect
	}
	return 0
}

//技能
type PbSkill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //技能ID
}

func (x *PbSkill) Reset() {
	*x = PbSkill{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbSkill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbSkill) ProtoMessage() {}

func (x *PbSkill) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbSkill.ProtoReflect.Descriptor instead.
func (*PbSkill) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{6}
}

func (x *PbSkill) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

//效果
type PbBuff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //ID
	Caster   int32 `protobuf:"varint,2,opt,name=caster,proto3" json:"caster,omitempty"`     //施法者UID
	Duration int32 `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"` //持续回合
}

func (x *PbBuff) Reset() {
	*x = PbBuff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbBuff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbBuff) ProtoMessage() {}

func (x *PbBuff) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbBuff.ProtoReflect.Descriptor instead.
func (*PbBuff) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{7}
}

func (x *PbBuff) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PbBuff) GetCaster() int32 {
	if x != nil {
		return x.Caster
	}
	return 0
}

func (x *PbBuff) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

//行为
type PbBehavior struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Target int32      `protobuf:"varint,1,opt,name=target,proto3" json:"target,omitempty"` //当前的索敌目标
	Skills []*PbSkill `protobuf:"bytes,2,rep,name=skills,proto3" json:"skills,omitempty"`  //技能数组
	Buffs  []*PbBuff  `protobuf:"bytes,3,rep,name=buffs,proto3" json:"buffs,omitempty"`    //Buff数组
}

func (x *PbBehavior) Reset() {
	*x = PbBehavior{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbBehavior) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbBehavior) ProtoMessage() {}

func (x *PbBehavior) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbBehavior.ProtoReflect.Descriptor instead.
func (*PbBehavior) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{8}
}

func (x *PbBehavior) GetTarget() int32 {
	if x != nil {
		return x.Target
	}
	return 0
}

func (x *PbBehavior) GetSkills() []*PbSkill {
	if x != nil {
		return x.Skills
	}
	return nil
}

func (x *PbBehavior) GetBuffs() []*PbBuff {
	if x != nil {
		return x.Buffs
	}
	return nil
}

//寻路点
type PbWayPoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Grids       []int32 `protobuf:"varint,1,rep,packed,name=grids,proto3" json:"grids,omitempty"`      //路径中格子的索引
	CurrentGrid int32   `protobuf:"varint,2,opt,name=currentGrid,proto3" json:"currentGrid,omitempty"` //当前格子索引
}

func (x *PbWayPoint) Reset() {
	*x = PbWayPoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BattleProto_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PbWayPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PbWayPoint) ProtoMessage() {}

func (x *PbWayPoint) ProtoReflect() protoreflect.Message {
	mi := &file_BattleProto_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PbWayPoint.ProtoReflect.Descriptor instead.
func (*PbWayPoint) Descriptor() ([]byte, []int) {
	return file_BattleProto_proto_rawDescGZIP(), []int{9}
}

func (x *PbWayPoint) GetGrids() []int32 {
	if x != nil {
		return x.Grids
	}
	return nil
}

func (x *PbWayPoint) GetCurrentGrid() int32 {
	if x != nil {
		return x.CurrentGrid
	}
	return 0
}

var File_BattleProto_proto protoreflect.FileDescriptor

var file_BattleProto_proto_rawDesc = []byte{
	0x0a, 0x11, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x22, 0x94, 0x02, 0x0a, 0x08,
	0x50, 0x62, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x65,
	0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x63, 0x55,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x63, 0x63, 0x55, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x61, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x67, 0x72, 0x69, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x08, 0x67, 0x72, 0x69, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e,
	0x50, 0x62, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x22, 0xc7, 0x02, 0x0a, 0x08, 0x50, 0x62, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x69, 0x72, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x64, 0x69, 0x72, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x62, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x2e, 0x50, 0x62, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x09, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e, 0x50, 0x62, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72,
	0x6d, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x31, 0x0a, 0x09,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e, 0x50, 0x62, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x52, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12,
	0x25, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e, 0x50, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x08, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69,
	0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x2e, 0x50, 0x62, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x08, 0x62, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x03, 0x77, 0x61, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e, 0x50, 0x62, 0x57,
	0x61, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x03, 0x77, 0x61, 0x79, 0x22, 0x7a, 0x0a, 0x0c,
	0x50, 0x62, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x74, 0x61, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x76, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x76, 0x6f, 0x22, 0x29, 0x0a, 0x0b, 0x50, 0x62, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x01, 0x79, 0x22, 0x47, 0x0a, 0x0b, 0x50, 0x62, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x74, 0x74, 0x72, 0x42, 0x61, 0x73, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x08, 0x61, 0x74, 0x74, 0x72, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x02, 0x52, 0x09, 0x61, 0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x6b, 0x0a, 0x07,
	0x50, 0x62, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x22, 0x19, 0x0a, 0x07, 0x50, 0x62, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x4c, 0x0a, 0x06, 0x50, 0x62, 0x42, 0x75, 0x66, 0x66, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x63, 0x61, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x63, 0x61, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x73, 0x0a, 0x0a, 0x50, 0x62, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x27, 0x0a, 0x06, 0x73, 0x6b, 0x69, 0x6c,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x2e, 0x50, 0x62, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x52, 0x06, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x73, 0x12, 0x24, 0x0a, 0x05, 0x62, 0x75, 0x66, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e, 0x50, 0x62, 0x42, 0x75, 0x66, 0x66,
	0x52, 0x05, 0x62, 0x75, 0x66, 0x66, 0x73, 0x22, 0x44, 0x0a, 0x0a, 0x50, 0x62, 0x57, 0x61, 0x79,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x67, 0x72, 0x69, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x69, 0x64, 0x42, 0x3f, 0x5a,
	0x23, 0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x73, 0x2f, 0x63, 0x73, 0xaa, 0x02, 0x17, 0x58, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x2e, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_BattleProto_proto_rawDescOnce sync.Once
	file_BattleProto_proto_rawDescData = file_BattleProto_proto_rawDesc
)

func file_BattleProto_proto_rawDescGZIP() []byte {
	file_BattleProto_proto_rawDescOnce.Do(func() {
		file_BattleProto_proto_rawDescData = protoimpl.X.CompressGZIP(file_BattleProto_proto_rawDescData)
	})
	return file_BattleProto_proto_rawDescData
}

var file_BattleProto_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_BattleProto_proto_goTypes = []interface{}{
	(*PbBattle)(nil),     // 0: battle.PbBattle
	(*PbEntity)(nil),     // 1: battle.PbEntity
	(*PbEntityBase)(nil), // 2: battle.PbEntityBase
	(*PbTransform)(nil),  // 3: battle.PbTransform
	(*PbAttribute)(nil),  // 4: battle.PbAttribute
	(*PbState)(nil),      // 5: battle.PbState
	(*PbSkill)(nil),      // 6: battle.PbSkill
	(*PbBuff)(nil),       // 7: battle.PbBuff
	(*PbBehavior)(nil),   // 8: battle.PbBehavior
	(*PbWayPoint)(nil),   // 9: battle.PbWayPoint
}
var file_BattleProto_proto_depIdxs = []int32{
	1, // 0: battle.PbBattle.entities:type_name -> battle.PbEntity
	2, // 1: battle.PbEntity.baseInfo:type_name -> battle.PbEntityBase
	3, // 2: battle.PbEntity.transform:type_name -> battle.PbTransform
	4, // 3: battle.PbEntity.attribute:type_name -> battle.PbAttribute
	5, // 4: battle.PbEntity.state:type_name -> battle.PbState
	8, // 5: battle.PbEntity.behavior:type_name -> battle.PbBehavior
	9, // 6: battle.PbEntity.way:type_name -> battle.PbWayPoint
	6, // 7: battle.PbBehavior.skills:type_name -> battle.PbSkill
	7, // 8: battle.PbBehavior.buffs:type_name -> battle.PbBuff
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_BattleProto_proto_init() }
func file_BattleProto_proto_init() {
	if File_BattleProto_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_BattleProto_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbBattle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbEntityBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbTransform); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbAttribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbSkill); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbBuff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbBehavior); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BattleProto_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PbWayPoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_BattleProto_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_BattleProto_proto_goTypes,
		DependencyIndexes: file_BattleProto_proto_depIdxs,
		MessageInfos:      file_BattleProto_proto_msgTypes,
	}.Build()
	File_BattleProto_proto = out.File
	file_BattleProto_proto_rawDesc = nil
	file_BattleProto_proto_goTypes = nil
	file_BattleProto_proto_depIdxs = nil
}
