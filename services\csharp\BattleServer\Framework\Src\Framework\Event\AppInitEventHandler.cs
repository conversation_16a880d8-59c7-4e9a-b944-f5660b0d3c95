﻿//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-11-8
//*********************************************************

namespace Aurora.Framework
{
    ////事件定义
    //public struct AppInitEvent
    //{
    //}

    ////事件处理
    //public class AppInitEventHandler : EventHandler<AppInitEvent>
    //{
    //    protected override void Handle(AppInitEvent e)
    //    {
    //        Log.Info("AppInitEventHandler:Handler");
    //    }
    //}
}
