#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableActivityLimitGift
	{

		public static readonly string TName="ActivityLimitGift.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 组ID 
		/// </summary> 
		public int GroupId {get; set;}
		/// <summary> 
		/// 下一个ID 
		/// </summary> 
		public int NextId {get; set;}
		/// <summary> 
		/// 领取条件 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 条件参数 
		/// </summary> 
		public int TypeValue {get; set;}
		/// <summary> 
		/// 奖励 
		/// </summary> 
		public int[][] drop {get; set;}
		/// <summary> 
		/// 最终奖励 
		/// </summary> 
		public int[][] FinalDrop {get; set;}
		#endregion

		public static TableActivityLimitGift GetData(int ID)
		{
			return TableManager.ActivityLimitGiftData.Get(ID);
		}

		public static List<TableActivityLimitGift> GetAllData()
		{
			return TableManager.ActivityLimitGiftData.GetAll();
		}

	}
	public sealed partial class TableActivityLimitGiftData
	{
		private Dictionary<int, TableActivityLimitGift> dict = new Dictionary<int, TableActivityLimitGift>();
		private List<TableActivityLimitGift> dataList = new List<TableActivityLimitGift>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableActivityLimitGift.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableActivityLimitGift>>(jsonContent);
			foreach (TableActivityLimitGift config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableActivityLimitGift Get(int id)
		{
			if (dict.TryGetValue(id, out TableActivityLimitGift item))
				return item;
			return null;
		}

		public List<TableActivityLimitGift> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
