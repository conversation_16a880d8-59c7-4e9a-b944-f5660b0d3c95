﻿namespace Aurora.Framework
{
    public interface IPerformanceMonitor
    {
        public void EnterMethod(string strMethodName);

        public void LeaveMethod(string strMethodName);
    }

    public class PerformanceMonitorManager : Singleton<PerformanceMonitorManager>
    {     
        private IPerformanceMonitor m_Monitor;

        //需要时候打开
        //public const bool IsOpen = true; 

        public IPerformanceMonitor Monitor
        {
            get
            {
                return m_Monitor;
            }
        }

        public void SetMonitor(IPerformanceMonitor monitor)
        {
            m_Monitor = monitor;
        }

        public static void EnterMethod(string strMethodName)
        {
#if OPEN_PERFORMANCE
            Instance.Monitor?.EnterMethod(strMethodName);
#endif        
        }

        public static void LeaveMethod(string strMethodName)
        {
#if OPEN_PERFORMANCE
            Instance.Monitor?.LeaveMethod(strMethodName);
#endif           
        }
    }
}