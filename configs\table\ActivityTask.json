[{"ID": 1001, "Name": 1001, "Des": 2001, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 22, "ConditionValue": [100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1002, "Name": 1002, "Des": 2002, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 23, "ConditionValue": [30], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1003, "Name": 1003, "Des": 2003, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 24, "ConditionValue": [3000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1004, "Name": 1004, "Des": 2004, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 23, "ConditionValue": [90], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1005, "Name": 1005, "Des": 2005, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 25, "ConditionValue": [500], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1006, "Name": 1006, "Des": 2006, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 26, "ConditionValue": [1000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1007, "Name": 1007, "Des": 2007, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 27, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1008, "Name": 1008, "Des": 2008, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 27, "ConditionValue": [2], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1009, "Name": 1009, "Des": 2009, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 28, "ConditionValue": [5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1010, "Name": 1010, "Des": 2010, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 29, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1011, "Name": 1011, "Des": 2011, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 30, "ConditionValue": [5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1012, "Name": 1012, "Des": 2012, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 24, "ConditionValue": [1000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1013, "Name": 1013, "Des": 2013, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 22, "ConditionValue": [100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1014, "Name": 1014, "Des": 2014, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 25, "ConditionValue": [120], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1015, "Name": 1015, "Des": 2015, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 1, "Condition": 30, "ConditionValue": [5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1016, "Name": 1016, "Des": 2016, "GroupId": 1001, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 1, "Condition": 26, "ConditionValue": [200], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1017, "Name": 1017, "Des": 2017, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 1, "ConditionValue": [200000], "CountType": 1, "Drop": [[101, 999]]}, {"ID": 1018, "Name": 1018, "Des": 2018, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 2, "ConditionValue": [10000], "CountType": 1, "Drop": [[101, 999]]}, {"ID": 1019, "Name": 1019, "Des": 2019, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 3, "ConditionValue": [50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1020, "Name": 1020, "Des": 2020, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 4, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1021, "Name": 1021, "Des": 2021, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 5, "ConditionValue": [50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1022, "Name": 1022, "Des": 2022, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 6, "ConditionValue": [1000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1023, "Name": 1023, "Des": 2023, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 2, "Condition": 7, "ConditionValue": [6], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1024, "Name": 1024, "Des": 2024, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 2, "Condition": 6, "ConditionValue": [600], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1025, "Name": 1025, "Des": 2025, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 2, "Condition": 8, "ConditionValue": [20], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1026, "Name": 1026, "Des": 2026, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 1, "OpenType": 2, "Condition": 9, "ConditionValue": [5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1027, "Name": 1027, "Des": 2027, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 4, "ConditionValue": [2], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1028, "Name": 1028, "Des": 2028, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 10, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1029, "Name": 1029, "Des": 2029, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 11, "ConditionValue": [3], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1030, "Name": 1030, "Des": 2030, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 12, "ConditionValue": [2000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1031, "Name": 1031, "Des": 2031, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 13, "ConditionValue": [20], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1032, "Name": 1032, "Des": 2032, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 1, "Condition": 14, "ConditionValue": [2, 1, 10], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1033, "Name": 1033, "Des": 2033, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 2, "Condition": 15, "ConditionValue": [1, 50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1034, "Name": 1034, "Des": 2034, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 2, "Condition": 15, "ConditionValue": [1, 100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1035, "Name": 1035, "Des": 2035, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 2, "Condition": 14, "ConditionValue": [2, 1, 5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1036, "Name": 1036, "Des": 2036, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 2, "OpenType": 2, "Condition": 14, "ConditionValue": [2, 1, 8], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1037, "Name": 1037, "Des": 2037, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 4, "ConditionValue": [3], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1038, "Name": 1038, "Des": 2038, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 16, "ConditionValue": [4, 10], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1039, "Name": 1039, "Des": 2039, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 16, "ConditionValue": [1, 1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1040, "Name": 1040, "Des": 2040, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 17, "ConditionValue": [1, 5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1041, "Name": 1041, "Des": 2041, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 17, "ConditionValue": [2, 1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1042, "Name": 1042, "Des": 2042, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 1, "Condition": 14, "ConditionValue": [2, 2, 10], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1043, "Name": 1043, "Des": 2043, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 2, "Condition": 15, "ConditionValue": [2, 50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1044, "Name": 1044, "Des": 2044, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 2, "Condition": 15, "ConditionValue": [2, 100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1045, "Name": 1045, "Des": 2045, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 2, "Condition": 14, "ConditionValue": [2, 2, 5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1046, "Name": 1046, "Des": 2046, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 3, "OpenType": 2, "Condition": 14, "ConditionValue": [2, 2, 8], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1047, "Name": 1047, "Des": 2047, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 1, "Condition": 4, "ConditionValue": [4], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1048, "Name": 1048, "Des": 2048, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 1, "Condition": 18, "ConditionValue": [300], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1049, "Name": 1049, "Des": 2049, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 1, "Condition": 15, "ConditionValue": [3, 30], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1050, "Name": 1050, "Des": 2050, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 1, "Condition": 19, "ConditionValue": [10], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1051, "Name": 1051, "Des": 2051, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 1, "Condition": 20, "ConditionValue": [5, 5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1052, "Name": 1052, "Des": 2052, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 2, "Condition": 19, "ConditionValue": [5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1053, "Name": 1053, "Des": 2053, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 2, "Condition": 19, "ConditionValue": [8], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1054, "Name": 1054, "Des": 2054, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 2, "Condition": 15, "ConditionValue": [3, 15], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1055, "Name": 1055, "Des": 2055, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 4, "OpenType": 2, "Condition": 15, "ConditionValue": [3, 25], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1056, "Name": 1056, "Des": 2056, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 1, "Condition": 4, "ConditionValue": [5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1057, "Name": 1057, "Des": 2057, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 1, "Condition": 21, "ConditionValue": [1, 20], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1058, "Name": 1058, "Des": 2058, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 1, "Condition": 17, "ConditionValue": [1, 5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1059, "Name": 1059, "Des": 2059, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 1, "Condition": 17, "ConditionValue": [2, 1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1060, "Name": 1060, "Des": 2060, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 1, "Condition": 21, "ConditionValue": [3, 150], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1061, "Name": 1061, "Des": 2061, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 2, "Condition": 21, "ConditionValue": [3, 50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1062, "Name": 1062, "Des": 2062, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 2, "Condition": 21, "ConditionValue": [3, 100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1063, "Name": 1063, "Des": 2063, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 2, "Condition": 21, "ConditionValue": [1, 10], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1064, "Name": 1064, "Des": 2064, "GroupId": 1002, "Type": 3, "SubType": 1, "ResetType": 1, "OpenDay": 5, "OpenType": 2, "Condition": 21, "ConditionValue": [1, 15], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1065, "Name": 1065, "Des": 2065, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 30, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1066, "Name": 1066, "Des": 2066, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 24, "ConditionValue": [50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1067, "Name": 1067, "Des": 2067, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 30, "ConditionValue": [2], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1068, "Name": 1068, "Des": 2068, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 22, "ConditionValue": [100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1069, "Name": 1069, "Des": 2069, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 31, "ConditionValue": [5000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1070, "Name": 1070, "Des": 2070, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 24, "ConditionValue": [200], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1071, "Name": 1071, "Des": 2071, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 30, "ConditionValue": [4], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1072, "Name": 1072, "Des": 2072, "GroupId": 1003, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 32, "ConditionValue": [1000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1073, "Name": 1073, "Des": 2073, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 33, "ConditionValue": [5], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1074, "Name": 1074, "Des": 2074, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 34, "ConditionValue": [50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1075, "Name": 1075, "Des": 2075, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 34, "ConditionValue": [100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1076, "Name": 1076, "Des": 2076, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 35, "ConditionValue": [98], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1077, "Name": 1077, "Des": 2077, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 35, "ConditionValue": [300], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1078, "Name": 1078, "Des": 2078, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 35, "ConditionValue": [600], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1079, "Name": 1079, "Des": 2079, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 35, "ConditionValue": [1000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1080, "Name": 1080, "Des": 2080, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 35, "ConditionValue": [2000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1081, "Name": 1081, "Des": 2081, "GroupId": 1003, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 35, "ConditionValue": [3000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1082, "Name": 1082, "Des": 2082, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [200], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1083, "Name": 1083, "Des": 2083, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [500], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1084, "Name": 1084, "Des": 2084, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [1000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1085, "Name": 1085, "Des": 2085, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [1500], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1086, "Name": 1086, "Des": 2086, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [2000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1087, "Name": 1087, "Des": 2087, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [3000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1088, "Name": 1088, "Des": 2088, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [5000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1089, "Name": 1089, "Des": 2089, "GroupId": 1004, "Type": 3, "SubType": 2, "ResetType": 4, "OpenDay": 1, "OpenType": 1, "Condition": 36, "ConditionValue": [8000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1090, "Name": 1090, "Des": 2090, "GroupId": 1005, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 30, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1091, "Name": 1091, "Des": 2091, "GroupId": 1005, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 30, "ConditionValue": [4], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1092, "Name": 1092, "Des": 2092, "GroupId": 1005, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 37, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1093, "Name": 1093, "Des": 2093, "GroupId": 1005, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 38, "ConditionValue": [100], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1094, "Name": 1094, "Des": 2094, "GroupId": 1005, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 24, "ConditionValue": [50], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1095, "Name": 1095, "Des": 2095, "GroupId": 1005, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 31, "ConditionValue": [5000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1096, "Name": 1096, "Des": 2096, "GroupId": 1005, "Type": 3, "SubType": 1, "ResetType": 2, "OpenDay": 1, "OpenType": 1, "Condition": 32, "ConditionValue": [1000], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1097, "Name": 1097, "Des": 2097, "GroupId": 1005, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 37, "ConditionValue": [15], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1098, "Name": 1098, "Des": 2098, "GroupId": 1005, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 29, "ConditionValue": [1], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1099, "Name": 1099, "Des": 2099, "GroupId": 1005, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 35, "ConditionValue": [168], "CountType": 2, "Drop": [[101, 999]]}, {"ID": 1100, "Name": 1100, "Des": 2100, "GroupId": 1005, "Type": 3, "SubType": 2, "ResetType": 1, "OpenDay": 1, "OpenType": 1, "Condition": 39, "ConditionValue": [3], "CountType": 2, "Drop": [[101, 999]]}]