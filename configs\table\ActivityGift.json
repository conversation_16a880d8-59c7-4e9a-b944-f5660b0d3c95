[{"ID": 101, "Name": 1001, "Des": 2001, "GroupId": 101, "CurrencyType": 2, "Price": 6, "Type": 0, "TypeValue": 0, "TriggerCondition": 1, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 102, "Name": 1002, "Des": 2002, "GroupId": 101, "CurrencyType": 2, "Price": 12, "Type": 0, "TypeValue": 0, "TriggerCondition": 1, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 103, "Name": 1003, "Des": 2003, "GroupId": 101, "CurrencyType": 2, "Price": 98, "Type": 0, "TypeValue": 0, "TriggerCondition": 1, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 201, "Name": 1004, "Des": 2004, "GroupId": 102, "CurrencyType": 2, "Price": 6, "Type": 2, "TypeValue": 0, "TriggerCondition": 1, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 202, "Name": 1005, "Des": 2005, "GroupId": 102, "CurrencyType": 0, "Price": 0, "Type": 2, "TypeValue": 0, "TriggerCondition": 1, "TriggerConditionValue": 0, "BuyNumber": 0, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 203, "Name": 1006, "Des": 2006, "GroupId": 102, "CurrencyType": 0, "Price": 0, "Type": 2, "TypeValue": 0, "TriggerCondition": 1, "TriggerConditionValue": 0, "BuyNumber": 0, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 301, "Name": 1007, "Des": 2007, "GroupId": 103, "CurrencyType": 1, "Price": 500, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 302, "Name": 1008, "Des": 2008, "GroupId": 103, "CurrencyType": 2, "Price": 68, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 303, "Name": 1009, "Des": 2009, "GroupId": 103, "CurrencyType": 2, "Price": 328, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 304, "Name": 1010, "Des": 2010, "GroupId": 103, "CurrencyType": 2, "Price": 68, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 305, "Name": 1011, "Des": 2011, "GroupId": 103, "CurrencyType": 2, "Price": 30, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 0, "ResetType": 1, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 306, "Name": 1012, "Des": 2012, "GroupId": 103, "CurrencyType": 2, "Price": 88, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 0, "ResetType": 1, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 307, "Name": 1013, "Des": 2013, "GroupId": 103, "CurrencyType": 2, "Price": 168, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 0, "ResetType": 1, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 308, "Name": 1014, "Des": 2014, "GroupId": 103, "CurrencyType": 2, "Price": 328, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 0, "ResetType": 1, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 309, "Name": 1015, "Des": 2015, "GroupId": 103, "CurrencyType": 2, "Price": 648, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 0, "ResetType": 1, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 401, "Name": 1016, "Des": 2016, "GroupId": 104, "CurrencyType": 2, "Price": 12, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 402, "Name": 1017, "Des": 2017, "GroupId": 104, "CurrencyType": 2, "Price": 40, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 403, "Name": 1018, "Des": 2018, "GroupId": 104, "CurrencyType": 2, "Price": 88, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 404, "Name": 1019, "Des": 2019, "GroupId": 104, "CurrencyType": 2, "Price": 168, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 405, "Name": 1020, "Des": 2020, "GroupId": 104, "CurrencyType": 2, "Price": 328, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 406, "Name": 1021, "Des": 2021, "GroupId": 104, "CurrencyType": 2, "Price": 328, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 407, "Name": 1022, "Des": 2022, "GroupId": 105, "CurrencyType": 0, "Price": 0, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 408, "Name": 1023, "Des": 2023, "GroupId": 105, "CurrencyType": 1, "Price": 300, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 409, "Name": 1024, "Des": 2024, "GroupId": 105, "CurrencyType": 2, "Price": 18, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 3001}, {"ID": 410, "Name": 1025, "Des": 2025, "GroupId": 105, "CurrencyType": 2, "Price": 68, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 1, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 3002}, {"ID": 411, "Name": 1026, "Des": 2026, "GroupId": 105, "CurrencyType": 2, "Price": 128, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 5, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 3003}, {"ID": 412, "Name": 1027, "Des": 2027, "GroupId": 105, "CurrencyType": 2, "Price": 198, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 5, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 3004}, {"ID": 413, "Name": 1028, "Des": 2028, "GroupId": 106, "CurrencyType": 1, "Price": 200, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 2, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 414, "Name": 1029, "Des": 2029, "GroupId": 106, "CurrencyType": 2, "Price": 30, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 415, "Name": 1030, "Des": 2030, "GroupId": 106, "CurrencyType": 2, "Price": 68, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 2, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 416, "Name": 1031, "Des": 2031, "GroupId": 106, "CurrencyType": 2, "Price": 68, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 417, "Name": 1032, "Des": 2032, "GroupId": 106, "CurrencyType": 2, "Price": 168, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 3, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}, {"ID": 418, "Name": 1033, "Des": 2033, "GroupId": 106, "CurrencyType": 2, "Price": 328, "Type": 2, "TypeValue": 0, "TriggerCondition": 7, "TriggerConditionValue": 0, "BuyNumber": 4, "ResetType": 0, "Drop": [[101, 99], [102, 99]], "Duration": 0, "IconDes": 0}]