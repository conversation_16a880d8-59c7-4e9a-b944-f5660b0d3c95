/**
 * 这个文件导表时自动生成的，不要手动修改！ 
  */
package table_data



import (
	"errors"
	"path"
	"reflect"
	"runtime/debug"
	"liteframe/pkg/tablereader"
	"liteframe/pkg/csvloader"
	"liteframe/pkg/log"
	"liteframe/pkg/util"
)


type TableActivityTimeConfig struct {
	// ============= 变量定义 =============
	// ID
	ID int32
	// 活动类型枚举值0.战令 1.七日任务（仅占位） 2.七日签到（仅占位）3. 开服活动.4英雄赛季buff
	Type int32
	// 时间计算类型0.以开服时间日零点为基准1.以绝对时间为基准2.以玩家创建角色时间为基准
	TimeCalcType int32
	// 准备时间(约定非固定日期的。 则生成的配置。是今天的秒比如 10：00:00 是36000秒。 这里的格式 只能是 时分秒。或是年月日。时分秒。 显示的有问题需要特殊处理)(暂不使用，根据需求再实现）
	ReadyTime string
	// 开始时间(分钟数或时间格式字符串）
	StartTime string
	// 结束时间(分钟数或时间格式字符串）
	EndTime string
	// 关闭时间(暂不使用，根据需求再实现）
	CloseTime string
	// 下期活动的Id
	NextId int32
	// 兑换商店ID
	ExchangeId int32
}




// TableActivityTimeConfigData 表格
type TableActivityTimeConfigData struct {
	file    string
	dataMap map[int32]*TableActivityTimeConfig
	Data    []*TableActivityTimeConfig
	md5     string
}

// load 加载
func (tb *TableActivityTimeConfigData) load(dir string, reader tablereader.ITableReader) error {
	p := path.Join(dir, tb.file)

	tData, err := reader.Read(p, reflect.TypeOf(TableActivityTimeConfig{}))
	if err != nil {
		return err
	}

	typeData := make([]*TableActivityTimeConfig, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivityTimeConfig)
		if !ok {
			return errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	tb.Data = typeData
	tb.dataMap = make(map[int32]*TableActivityTimeConfig, len(tData))
	for _, v := range tb.Data {
		tb.dataMap[v.ID] = v
	}

	tb.md5, _ = util.GetFileMD5(p)

	log.Debug("load success", log.Kv("file_name", tb.file))
	return nil
}

// reload 重新表格
// 重新加载不会做减量，只做增量和改变
func (tb *TableActivityTimeConfigData) reload(dir string) (bool, error) {
	//中间处理不可预料得错误一定要恢复回来
	defer func() {
		if err := recover(); nil != err {
			log.Fatal("reload", log.Kv("file_name", tb.file), log.Kv("err", err), log.Kv("stack", string(debug.Stack())))
		}
	}()
	p := path.Join(dir, tb.file)
	// 计算MD5
	md5, err := util.GetFileMD5(p)
	if nil != err {
		return false, err
	}

	//检查是否需要reload
	if md5 == tb.md5 {
		return false, nil
	}

	tData, err := csvloader.LoadCSVConfig(p, reflect.TypeOf(TableActivityTimeConfig{}))
	if err != nil {
		return false, err
	}

	typeData := make([]*TableActivityTimeConfig, len(tData))
	for i, v := range tData {
		typeV, ok := v.(*TableActivityTimeConfig)
		if !ok {
			return false, errors.New("convert interface{} to struct error")
		}
		typeData[i] = typeV
	}

	for _, v := range typeData {
		//已有的要修改值，新增得直接增加
		if data, ok := tb.dataMap[v.ID]; ok {
			util.DeepCopy(data, v)
		} else {
			tb.dataMap[v.ID] = v
			tb.Data = append(tb.Data, v)
		}
	}

	tb.md5 = md5
	log.Warn("reload success", log.Kv("file_name", tb.file))

	return true, nil
}

// GetFileName 获取table的文件名
func (tb *TableActivityTimeConfigData) GetFileName() string {
	return tb.file
}

// GetById 根据ID查找
func (tb *TableActivityTimeConfigData) GetById(id int32) *TableActivityTimeConfig {
	v := tb.dataMap[id]
	return v
}

// GetCloneById 根据ID查找克隆体(性能很差，慎用)
func (tb *TableActivityTimeConfigData) GetCloneById(id int32) *TableActivityTimeConfig {
	v := tb.dataMap[id]
	out := &TableActivityTimeConfig{}
	util.DeepCopy(out, v)
	return out
}

// Count 总个数
func (tb *TableActivityTimeConfigData) Count() int {
	return len(tb.dataMap)
}

// Foreach 遍历
func (tb *TableActivityTimeConfigData) Foreach(call func(*TableActivityTimeConfig) bool) {
	for _, v := range tb.Data {
		if call(v) {
			break
		}
	}
}

// MD5 输出表的MD5
func (tb *TableActivityTimeConfigData) MD5() string {
	return tb.md5
}

// Clone 数据复制
func (tb *TableActivityTimeConfigData) Clone() ITable {
	ntb := &TableActivityTimeConfigData{
		file:    tb.file,
		dataMap: make(map[int32]*TableActivityTimeConfig),
		Data:    make([]*TableActivityTimeConfig, 0, len(tb.Data)),
		md5:     tb.md5,
	}

	for _, d := range tb.Data {
		tmp := &TableActivityTimeConfig{}
		util.DeepCopy(tmp, d)
		ntb.Data = append(ntb.Data, tmp)
		ntb.dataMap[d.ID] = tmp
	}

	return ntb
}
