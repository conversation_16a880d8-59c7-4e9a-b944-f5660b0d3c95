﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Aurora.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Game.Core
{
    public struct NewHero
    {
        public List<int> CampList;
        public int Count;
        public int StarLevel;

        public NewHero()
        {
            CampList = new List<int>();
        }
    }

    public struct StarLevelUp
    {
        public int StarLevel;
        public int MaxStarLevel;
    }

    public sealed partial class TableServerSkill
    {
        public static StarLevelUp GetLevelUpSkill(int id)
        {
            return TableManager.ServerSkillData.GetLevelUpSkill(id);
        }

        public static NewHero GetNewHeroSkill(int id)
        {
            return TableManager.ServerSkillData.GetNewHeroSkill(id);
        }

        public static bool RandomWeight(int id)
        {
            int randNum = RandomGenerator.RandomNumberInclude(1, 1000);

            TableServerSkill skill = GetData(id);
            if (randNum > skill.TiggerPro)
            {
                return false;
            }

            return true;
        }
    }

    public sealed partial class TableServerSkillData
    {
        private Dictionary<int, NewHero> mNewHeroSkill = new Dictionary<int, NewHero>();
        private Dictionary<int, StarLevelUp> mStarLevelUpSkill = new Dictionary<int, StarLevelUp>();
        private Dictionary<int, bool> mFreeMerge = new Dictionary<int, bool>();
        public void InitHelper()
        {
            foreach (var tableSkill in dataList)
            {
                if (tableSkill.LogicType == 1)
                {
                    var parsedObjects = JsonConvert.DeserializeObject<List<object>>(tableSkill.LogicParam);
                    JToken campToken = (JToken)parsedObjects[0];
                    NewHero newHero = new NewHero();
                    newHero.CampList.AddRange(campToken.ToObject<List<int>>());

                    newHero.Count = (int)(long)parsedObjects[1];
                    newHero.StarLevel = (int)(long)parsedObjects[2];

                    mNewHeroSkill.Add(tableSkill.ID, newHero);
                }
                else if (tableSkill.LogicType == 2)
                {
                    mFreeMerge.Add(tableSkill.ID, true);
                }
                else if (tableSkill.LogicType == 3)
                {
                    var numberLists = JsonConvert.DeserializeObject<List<int>>(tableSkill.LogicParam);

                    StarLevelUp starLevelUp = new StarLevelUp();
                    starLevelUp.StarLevel = numberLists[0];
                    starLevelUp.MaxStarLevel = numberLists[1];
                    mStarLevelUpSkill.Add(tableSkill.ID, starLevelUp);
                }
            }
        }

        public StarLevelUp GetLevelUpSkill(int id)
        {
            StarLevelUp levelUp;
            mStarLevelUpSkill.TryGetValue(id, out levelUp);
            return levelUp;
        }

        public NewHero GetNewHeroSkill(int id)
        {
            return mNewHeroSkill[id];
        }
    }
}
