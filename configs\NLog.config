﻿<?xml version="1.0" encoding="utf-8" ?>

<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<targets async="true">
		<target name="ServerDebug" xsi:type="File"
				openFileCacheTimeout="10"
				keepFileOpen="true"
				archiveNumbering="Date"
				archiveEvery="Hour"
				archiveDateFormat="yyyyMMddHH"
				archiveFileName="${basedir}/log/${logger}.${var:appIdFormat}.{#}.Debug.log"
				fileName="${basedir}/log/${logger}.${var:appIdFormat}.${date:format=yyyyMMddHHmm}.Debug.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=3} ${message}" />
	</targets>

	<targets async="true">
		<target name="ServerOne" xsi:type="File"
				keepFileOpen="true"
				openFileCacheTimeout="30"
				deleteOldFileOnStartup="false"
				archiveOldFileOnStartup="false"
				archiveEvery="Hour"
				archiveNumbering="Date"
				archiveDateFormat="yyyyMMddHH"
				archiveFileName="${basedir}/log/${logger}.${var:appIdFormat}.{#}.log"
				fileName="${basedir}/log/${logger}.${var:appIdFormat}.${date:format=yyyyMMddHH}.log"
				bufferSize="20480000"
				concurrentWrites="true"
				enableArchiveLocking="true"
				layout="[${longdate}]${message}^TId:${threadid}]">
			<!-- 启用内部日志记录，便于调试 -->
			<internalLogFile value="${basedir}/log/internal-nlog.txt"  />
		</target>
	</targets>
	<targets async="true">
		<target name="ServerFatal" xsi:type="File"
				keepFileOpen="true"
				openFileCacheTimeout="30"
				deleteOldFileOnStartup="false"
				archiveOldFileOnStartup="false"
				archiveEvery="Hour"
				archiveNumbering="Date"
				archiveDateFormat="yyyyMMddHH"
				archiveFileName="${basedir}/log/${logger}.${var:appIdFormat}.{#}.Fatal.log"
				fileName="${basedir}/log/${logger}.${var:appIdFormat}.${date:format=yyyyMMddHH}.Fatal.log"
				bufferSize="20480000"
				concurrentWrites="true"
				enableArchiveLocking="true"
				layout="[${longdate}]${message}^TId:${threadid}]">
			<!-- 启用内部日志记录，便于调试 -->
			<internalLogFile value="${basedir}/log/internal-nlog.txt"  />
		</target>
	</targets>
	<targets async="true">
		<target name="BILog" xsi:type="File"
				bufferSize="10240000"
				openFileCacheTimeout="10"
				keepFileOpen="true"
				concurrentWrites="true"
				fileName="${basedir}/log/cylog/${event-properties:BILogModule}.log.${date:format=yyyy-MM-dd}"
				deleteOldFileOnStartup="false"
				layout="${message}" />
	</targets>
	<targets async="true">
		<target name="ServerInfo" xsi:type="File"
				bufferSize="10240"
				openFileCacheTimeout="30"
				keepFileOpen="true"
				archiveNumbering="Date"
				archiveEvery="Hour"
				archiveDateFormat="yyyyMMddHH"
				archiveFileName="${basedir}/log/${logger}.${var:appIdFormat}.{#}.Info.log"
				fileName="${basedir}/log/${logger}.${var:appIdFormat}.${date:format=yyyyMMddHHmm}.Info.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${message}" />
	</targets>

	<targets async="true">
		<target name="ServerWarn" xsi:type="File"
				bufferSize="10240"
				openFileCacheTimeout="30"
				keepFileOpen="true"
				archiveNumbering="Date"
				archiveEvery="Hour"
				archiveDateFormat="yyyyMMddHH"
				archiveFileName="${basedir}/log/${logger}.${var:appIdFormat}.{#}.Warn.log"
				fileName="${basedir}/log/${logger}.${var:appIdFormat}.${date:format=yyyyMMddHHmm}.Warn.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${message}" />
	</targets>

	<targets async="true">
		<target name="ServerError" xsi:type="File"
				openFileCacheTimeout="10"
				keepFileOpen="true"
				archiveNumbering="Date"
				archiveEvery="Hour"
				archiveDateFormat="yyyyMMddHH"
				archiveFileName="${basedir}/log/${logger}.${var:appIdFormat}.{#}.Error.log"
				fileName="${basedir}/log/${logger}.${var:appIdFormat}.${date:format=yyyyMMddHHmm}.Error.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${message}" />
	</targets>

	<targets async="true">
		<target name="ErrorConsole" xsi:type="Console" layout="${longdate} ${message}" />
	</targets>

	<targets async="true">
		<target name="Watcher" xsi:type="File"
				openFileCacheTimeout="10"
				keepFileOpen="true"
				fileName="${basedir}/log/${logger}.${var:appIdFormat}.Debug.log"
				deleteOldFileOnStartup="false"
				layout="${longdate} ${callsite:className=false:methodName=false:fileName=true:includeSourcePath=false:skipFrames=3} ${message}" />
	</targets>

	<targets async="true">
		<target name="RobotConsole" xsi:type="Console" layout="${message}" />
	</targets>



	<rules>
		<logger ruleName="ServerOne" name="BattleServcer" minlevel="Trace" maxlevel="Fatal" writeTo="ServerOne" />
		<logger ruleName="ServerFatal" name="BattleServcer" minlevel="Error" maxlevel="Fatal" writeTo="ServerFatal" />
	</rules>
</nlog>