//*********************************************************
// Framework
// Author:  Jasen
// Date  :  2022-10-25
//*********************************************************


using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Aurora.Framework
{
    public class ObjectPoolSystem : Singleton<ObjectPoolSystem>
    {
        private readonly Dictionary<Type, ObjectPoolInternal> m_ObjectPools = new Dictionary<Type, ObjectPoolInternal>();

        public int Count
        {
            get { return m_ObjectPools.Count; }
        }

        public bool HasObjectPool<T>() where T : SpawnObject
        {
            Type tp = typeof(T);
            return HasObjectPool(tp);
        }

        public bool HasObjectPool(Type objectType)
        {
            return m_ObjectPools.ContainsKey(objectType);
        }

        public IObjectPool<T> GetObjectPool<T>() where T : SpawnObject
        {
            Type tp = typeof(T);
            ObjectPoolInternal result = null;
            if(!m_ObjectPools.TryGetValue(tp,out result))
            {
                return null;
            }
            return (IObjectPool<T>) result;
        }

        public List<ObjectPoolInternal> GetAllObjectPools()
        {
            return m_ObjectPools.Values.ToList();
        }

        public IObjectPool<T> CreateObjectPool<T>(int capacity, float expireTime, float autoReleaseTime) where T : SpawnObject
        {
            if(HasObjectPool<T>())
            {
                throw new FrameworkException($"Already exist object pool '{typeof(T).FullName}'.");
            }
            ObjectPool<T> newPool = new ObjectPool<T>(capacity, expireTime, autoReleaseTime, false);
            m_ObjectPools.Add(typeof(T), newPool);
            return newPool;
        }

        public IObjectPool<T> CreateMultiObjectPool<T>(int capacity, float expireTime, float autoReleaseTime) where T : SpawnObject
        {
            if (HasObjectPool<T>())
            {
                throw new FrameworkException($"Already exist object pool '{typeof(T).FullName}'.");
            }
            ObjectPool<T> newPool = new ObjectPool<T>(capacity, expireTime, autoReleaseTime, true);
            m_ObjectPools.Add(typeof(T), newPool);
            return newPool;
        }
        public bool DestroyObjectPool<T>() where T : SpawnObject
        {
            Type tp = typeof(T);
            ObjectPoolInternal result = null;
            if (!m_ObjectPools.TryGetValue(tp, out result))
            {
                return false;
            }
            result.Shutdown();
            m_ObjectPools.Remove(tp);
            return true;
        }

        public void Release()
        {
            foreach(var p in m_ObjectPools)
            {
                p.Value.Release();
            }
        }

        public void ReleaseAllUnused()
        {
            foreach (var p in m_ObjectPools)
            {
                p.Value.ReleaseUnused();
            }
        }
        public void Shutdown()
        {
            foreach (var p in m_ObjectPools)
            {
                p.Value.Shutdown();
            }
            m_ObjectPools.Clear();
        }

        public void Update(float elapseSeconds)
        {
            foreach (var p in m_ObjectPools)
            {
                p.Value.Update(elapseSeconds);
            }
        }
    }
}

