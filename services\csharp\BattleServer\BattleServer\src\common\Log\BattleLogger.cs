using System;
using System.Collections.Generic;
using System.IO;
using NLog;
using NLog.Config;
using NLog.Targets;

namespace BattleServer.Server
{
    /// <summary>
    /// 战斗专用日志记录器
    /// 为每场战斗创建独立的日志文件：年月日时分秒_战斗ID.log
    /// 同时让所有现有的Log.Info等调用都输出到该文件
    /// </summary>
    public class BattleLogger
    {
        private readonly string _battleLogFile;
        private readonly FileTarget _battleFileTarget;
        private readonly string _targetName;
        private readonly string _loggerName;

        /// <summary>
        /// 创建战斗日志记录器
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        public BattleLogger(long battleId)
        {
            // 生成日志文件名：年-月-日_时-分-秒_战斗ID（更清晰的格式）
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
            _battleLogFile = $"battle_{timestamp}_{battleId}";
            _targetName = $"BattleFile_{battleId}";
            _loggerName = $"AutoChessScene_{battleId}";

            // 确定正确的日志目录路径
            var currentDir = Environment.CurrentDirectory;
            var logDir = Path.Combine(currentDir, "battlelog");

            // 如果当前目录已经是bin目录，则直接使用battlelog
            if (currentDir.EndsWith("bin"))
            {
                logDir = Path.Combine(currentDir, "battlelog");
            }
            else
            {
                // 如果当前目录是liteframe，则使用bin/battlelog
                logDir = Path.Combine(currentDir, "bin", "battlelog");
            }

            // 确保目录存在
            Directory.CreateDirectory(logDir);

            // 创建战斗专用的文件目标
            var logFilePath = Path.Combine(logDir, $"{_battleLogFile}.log");

            _battleFileTarget = new FileTarget(_targetName)
            {
                FileName = logFilePath,
                Layout = "[${longdate}] ${message}",
                KeepFileOpen = true,
                OpenFileCacheTimeout = 30,
                DeleteOldFileOnStartup = false
            };

            // 动态添加到NLog配置
            AddBattleLogTarget();
        }

        /// <summary>
        /// 动态添加战斗日志目标到NLog配置
        /// </summary>
        private void AddBattleLogTarget()
        {
            var config = LogManager.Configuration ?? new LoggingConfiguration();

            // 添加文件目标
            config.AddTarget(_targetName, _battleFileTarget);

            // 为BattleServcer logger添加规则，让所有日志都输出到战斗文件
            var rule = new LoggingRule("BattleServcer", LogLevel.Trace, _battleFileTarget);
            config.LoggingRules.Add(rule);

            // 添加通用规则，捕获所有可能的日志
            var generalRule = new LoggingRule("*", LogLevel.Trace, _battleFileTarget);
            config.LoggingRules.Add(generalRule);

            // 应用配置
            LogManager.Configuration = config;
        }

        /// <summary>
        /// 移除战斗日志目标（在战斗结束时调用）
        /// </summary>
        public void RemoveBattleLogTarget()
        {
            try
            {
                var config = LogManager.Configuration;
                if (config != null)
                {
                    // 移除规则
                    var rulesToRemove = new List<LoggingRule>();
                    foreach (var rule in config.LoggingRules)
                    {
                        if (rule.Targets.Contains(_battleFileTarget))
                        {
                            rulesToRemove.Add(rule);
                        }
                    }

                    foreach (var rule in rulesToRemove)
                    {
                        config.LoggingRules.Remove(rule);
                    }

                    // 移除目标
                    if (config.AllTargets.Contains(_battleFileTarget))
                    {
                        config.RemoveTarget(_targetName);
                    }

                    // 应用配置
                    LogManager.Configuration = config;
                }

                // 关闭文件目标
                _battleFileTarget?.Dispose();
            }
            catch (Exception ex)
            {
                // 避免在清理时抛出异常
                Console.WriteLine($"Error removing battle log target: {ex.Message}");
            }
        }
    }
}
