#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableModel
	{

		public static readonly string TName="Model.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// AssName 
		/// </summary> 
		public string AssName {get; set;}
		/// <summary> 
		/// ABName 
		/// </summary> 
		public string AB_Name {get; set;}
		/// <summary> 
		/// 类型 
		/// </summary> 
		public int Type {get; set;}
		/// <summary> 
		/// 编辑器下的路径信息 
		/// </summary> 
		public string EditorPath {get; set;}
		/// <summary> 
		/// 模型的半径 
		/// </summary> 
		public float Radius {get; set;}
		/// <summary> 
		/// 受击动作 
		/// </summary> 
		public int[] HurtAnims {get; set;}
		/// <summary> 
		/// 受击融合动作 
		/// </summary> 
		public int HurtFusionAnim {get; set;}
		/// <summary> 
		/// 模型的大中小类型（0小、1中、2大） 目前针对冰冻眩晕特效使用 
		/// </summary> 
		public int SizeType {get; set;}
		/// <summary> 
		/// 子节点Id 
		/// </summary> 
		public int Node {get; set;}
		/// <summary> 
		/// 特效是否具有物理属性 
		/// </summary> 
		public int Physics {get; set;}
		/// <summary> 
		/// 缩放比例（缩放模型自身） 
		/// </summary> 
		public float Scale {get; set;}
		/// <summary> 
		/// 旋转 
		/// </summary> 
		public float[] Rotation {get; set;}
		/// <summary> 
		/// RT相机下的坐标偏移 
		/// </summary> 
		public float[] Location {get; set;}
		/// <summary> 
		/// RT相机的缩放（第一位默认的缩放，第二位无限塔的缩放） 
		/// </summary> 
		public float[] RTScaleValues {get; set;}
		/// <summary> 
		/// 出场的渐变 
		/// </summary> 
		public float StartAlphaTime {get; set;}
		/// <summary> 
		/// 出场的特效 
		/// </summary> 
		public int StartEffectId {get; set;}
		/// <summary> 
		/// 出场的音效id 
		/// </summary> 
		public int StartSoundId {get; set;}
		/// <summary> 
		/// 死亡特效 
		/// </summary> 
		public int DeadEffectId {get; set;}
		/// <summary> 
		/// 死亡音效1 
		/// </summary> 
		public int DeadSound {get; set;}
		/// <summary> 
		/// 受击特效 
		/// </summary> 
		public int HurtEffectId {get; set;}
		/// <summary> 
		/// 受击音效 
		/// </summary> 
		public int HurtSound {get; set;}
		/// <summary> 
		/// 伤害信息偏移 
		/// </summary> 
		public float HeadInfoDamageHeight {get; set;}
		/// <summary> 
		/// 血条偏移 
		/// </summary> 
		public float HeadInfoBloodHeight {get; set;}
		/// <summary> 
		/// 称号的偏移量 
		/// </summary> 
		public float HeadTitleHeightY {get; set;}
		/// <summary> 
		/// HeadPoint位置 
		/// </summary> 
		public float[] HeadPointHeightXY {get; set;}
		/// <summary> 
		/// BoodyPoint位置偏移 
		/// </summary> 
		public float[] BodyPointHeightXY {get; set;}
		/// <summary> 
		/// 模型特效缩放 
		/// </summary> 
		public float UIModelEffectScale {get; set;}
		/// <summary> 
		/// 头顶偏移 
		/// </summary> 
		public float HeadOffset {get; set;}
		/// <summary> 
		/// 脚底偏移 
		/// </summary> 
		public float FootOffset {get; set;}
		/// <summary> 
		/// 死亡是是否慢镜头 
		/// </summary> 
		public int DeathSlowShot {get; set;}
		/// <summary> 
		/// 是否显示武器 
		/// </summary> 
		public int IsShowWeapon {get; set;}
		/// <summary> 
		/// 聊天泡泡偏移量x 
		/// </summary> 
		public float ChatBubbleOffsetX {get; set;}
		/// <summary> 
		/// 聊天泡泡偏移量y 
		/// </summary> 
		public float ChatBubbleOffsetY {get; set;}
		/// <summary> 
		/// 聊天泡泡内容 
		/// </summary> 
		public int[] ChatBubbleIds {get; set;}
		/// <summary> 
		/// 聊天泡泡底板类型 
		/// </summary> 
		public int[] ChatBubbleTypes {get; set;}
		#endregion

		public static TableModel GetData(int ID)
		{
			return TableManager.ModelData.Get(ID);
		}

		public static List<TableModel> GetAllData()
		{
			return TableManager.ModelData.GetAll();
		}

	}
	public sealed partial class TableModelData
	{
		private Dictionary<int, TableModel> dict = new Dictionary<int, TableModel>();
		private List<TableModel> dataList = new List<TableModel>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableModel.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableModel>>(jsonContent);
			foreach (TableModel config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableModel Get(int id)
		{
			if (dict.TryGetValue(id, out TableModel item))
				return item;
			return null;
		}

		public List<TableModel> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
